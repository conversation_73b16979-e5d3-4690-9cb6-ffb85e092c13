#!/usr/bin/env python
"""
Advanced NPU Scheduling Optimization Demo
Demonstrates all five key optimization techniques:
1. Reinforcement Learning for intelligent task scheduling
2. Runtime Dynamic Adjustment with feedback control
3. Multi-core Distribution with NUMA awareness
4. Predictive Data Prefetching with ML models
5. Energy-aware Multi-objective Optimization
"""

import time
import json
import logging
import sys
import os
from pathlib import Path

# Add project directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'schedulers'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'optimizers'))

from optimized_npu_scheduler import OptimizedNPUScheduler, SchedulingStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demonstrate_advanced_optimizations():
    """Demonstrate all advanced optimization techniques"""
    
    print("="*80)
    print("Advanced NPU Scheduling Optimization Demo")
    print("="*80)
    
    # Load test data
    data_dir = Path("data")
    test_files = list(data_dir.glob("*.json"))
    
    if not test_files:
        print("No test data found in data/ directory")
        return
    
    # Use first available test file
    test_file = test_files[0]
    print(f"Using test file: {test_file.name}")
    
    # Create scheduler with advanced optimizations
    scheduler = OptimizedNPUScheduler(str(test_file))
    
    print(f"Loaded graph with {len(scheduler.nodes)} nodes and {len(scheduler.data['Edges'])} edges")
    
    # Get optimization statistics
    stats = scheduler.get_optimization_statistics()
    print("\nAvailable Optimizations:")
    for optimizer, info in stats.items():
        if optimizer != 'basic_optimizers':
            status = "✓" if info.get('available', False) else "✗"
            print(f"  {status} {optimizer.replace('_', ' ').title()}")
    
    print("\n" + "="*80)
    print("Testing Different Scheduling Strategies")
    print("="*80)
    
    # Test all available strategies
    strategies_to_test = [
        SchedulingStrategy.GREEDY,
        SchedulingStrategy.HEFT,
        SchedulingStrategy.CRITICAL_PATH,
        SchedulingStrategy.MEMORY_AWARE,
        SchedulingStrategy.HYBRID,
        SchedulingStrategy.RL_BASED,
        SchedulingStrategy.ADAPTIVE,
        SchedulingStrategy.DISTRIBUTED,
        SchedulingStrategy.ENERGY_AWARE
    ]
    
    results = {}
    
    for strategy in strategies_to_test:
        print(f"\nTesting {strategy.value} strategy...")
        
        try:
            start_time = time.time()
            result = scheduler.solve_problem1(strategy)
            execution_time = time.time() - start_time
            
            results[strategy.value] = {
                'max_cache': result.max_cache,
                'execution_time': execution_time,
                'success': True
            }
            
            print(f"  ✓ Max cache: {result.max_cache} bytes")
            print(f"  ✓ Execution time: {execution_time:.3f}s")
            
        except Exception as e:
            results[strategy.value] = {
                'error': str(e),
                'success': False
            }
            print(f"  ✗ Failed: {e}")
    
    # Find best strategy
    successful_results = {k: v for k, v in results.items() if v.get('success', False)}
    
    if successful_results:
        best_strategy = min(successful_results.keys(), 
                          key=lambda x: successful_results[x]['max_cache'])
        
        print(f"\n🏆 Best strategy: {best_strategy}")
        print(f"   Max cache: {successful_results[best_strategy]['max_cache']} bytes")
        print(f"   Time: {successful_results[best_strategy]['execution_time']:.3f}s")
    
    print("\n" + "="*80)
    print("Advanced Optimization Features Demo")
    print("="*80)
    
    # Demonstrate RL training (if available)
    if stats['rl_scheduler']['available']:
        print("\n🧠 Reinforcement Learning Training Demo")
        print("-" * 40)
        
        if not stats['rl_scheduler'].get('model_trained', False):
            print("Training RL model (this may take a while)...")
            success = scheduler.train_rl_model(total_timesteps=10000)  # Reduced for demo
            
            if success:
                print("✓ RL model training completed")
                
                # Test RL-based scheduling
                print("Testing RL-based scheduling...")
                rl_result = scheduler.solve_problem1(SchedulingStrategy.RL_BASED)
                print(f"✓ RL schedule max cache: {rl_result.max_cache} bytes")
            else:
                print("✗ RL model training failed")
        else:
            print("✓ RL model already trained")
    
    # Demonstrate adaptive scheduling
    if stats['adaptive_scheduler']['available']:
        print("\n⚡ Adaptive Scheduling Demo")
        print("-" * 40)
        
        print("Running adaptive scheduling with performance monitoring...")
        adaptive_result = scheduler.solve_problem1(SchedulingStrategy.ADAPTIVE)
        print(f"✓ Adaptive schedule max cache: {adaptive_result.max_cache} bytes")
        
        # Get adaptation statistics
        adaptive_stats = scheduler.adaptive_scheduler.get_adaptation_statistics()
        if adaptive_stats:
            print(f"✓ Total adaptations: {adaptive_stats.get('total_adaptations', 0)}")
    
    # Demonstrate distributed scheduling
    if stats['distributed_scheduler']['available']:
        print("\n🌐 Distributed Scheduling Demo")
        print("-" * 40)
        
        print("Running distributed scheduling with NUMA awareness...")
        dist_result = scheduler.solve_problem1(SchedulingStrategy.DISTRIBUTED)
        print(f"✓ Distributed schedule max cache: {dist_result.max_cache} bytes")
        
        # Get distribution statistics
        dist_stats = scheduler.distributed_scheduler.get_distribution_statistics()
        if dist_stats:
            print(f"✓ Load balance: {dist_stats.get('load_balance', 0):.3f}")
            print(f"✓ Partitions: {dist_stats.get('num_partitions', 0)}")
    
    # Demonstrate energy-aware optimization
    if stats['energy_optimizer']['available']:
        print("\n🔋 Energy-aware Optimization Demo")
        print("-" * 40)
        
        print("Running multi-objective energy optimization...")
        energy_result = scheduler.solve_problem1(SchedulingStrategy.ENERGY_AWARE)
        print(f"✓ Energy-aware schedule max cache: {energy_result.max_cache} bytes")
        
        # Show optimization runs
        opt_runs = stats['energy_optimizer'].get('optimization_runs', 0)
        print(f"✓ Optimization runs completed: {opt_runs}")
    
    # Demonstrate prefetch optimization
    if stats['prefetch_optimizer']['available']:
        print("\n🎯 Prefetch Optimization Demo")
        print("-" * 40)
        
        print("Running schedule with prefetch optimization...")
        # Use hybrid strategy with prefetch optimization
        prefetch_result = scheduler.solve_problem1(SchedulingStrategy.HYBRID)
        print(f"✓ Schedule with prefetch optimization max cache: {prefetch_result.max_cache} bytes")
        
        # Get prefetch statistics
        prefetch_stats = scheduler.prefetch_optimizer.get_prefetch_statistics()
        if prefetch_stats:
            accuracy = prefetch_stats.get('prefetch_accuracy', 0)
            print(f"✓ Prefetch accuracy: {accuracy:.1%}")
    
    print("\n" + "="*80)
    print("Complete Problem Solving Demo")
    print("="*80)
    
    # Solve all three problems with best strategy
    if successful_results:
        print(f"\nSolving all problems with {best_strategy} strategy...")
        
        try:
            # Problem 1: Minimum cache scheduling
            result1 = scheduler.solve_problem1(SchedulingStrategy[best_strategy.upper()])
            print(f"✓ Problem 1 - Max cache: {result1.max_cache} bytes")
            
            # Problem 2: Cache allocation with SPILL
            result2 = scheduler.solve_problem2(result1.schedule)
            print(f"✓ Problem 2 - SPILL operations: {result2.spill_count}")
            print(f"✓ Problem 2 - Total spill data: {result2.total_spill_data} bytes")
            
            # Problem 3: Performance optimization
            result3 = scheduler.solve_problem3(result2.schedule, result2.allocations)
            print(f"✓ Problem 3 - Makespan: {result3.makespan} cycles")
            
            print(f"\n🎉 Complete optimization finished!")
            print(f"   Final makespan: {result3.makespan} cycles")
            print(f"   Peak memory: {result3.max_cache} bytes")
            print(f"   SPILL overhead: {result2.total_spill_data} bytes")
            
        except Exception as e:
            print(f"✗ Complete problem solving failed: {e}")
    
    print("\n" + "="*80)
    print("Performance Comparison Summary")
    print("="*80)
    
    # Create comparison table
    print(f"{'Strategy':<20} {'Max Cache (bytes)':<15} {'Time (s)':<10} {'Status'}")
    print("-" * 55)
    
    for strategy, result in results.items():
        if result.get('success', False):
            cache = result['max_cache']
            time_taken = result['execution_time']
            status = "✓"
            print(f"{strategy:<20} {cache:<15} {time_taken:<10.3f} {status}")
        else:
            print(f"{strategy:<20} {'N/A':<15} {'N/A':<10} ✗")
    
    # Cleanup
    print("\n" + "="*80)
    print("Cleanup")
    print("="*80)
    
    scheduler.cleanup_optimizers()
    print("✓ Optimizer cleanup completed")
    
    print("\n🎯 Advanced NPU Scheduling Optimization Demo Complete!")
    print("   This demo showcased 5 key optimization techniques:")
    print("   1. ✓ Reinforcement Learning with Graph Neural Networks")
    print("   2. ✓ Runtime Dynamic Adjustment with Feedback Control")
    print("   3. ✓ Multi-core Distribution with NUMA Awareness")
    print("   4. ✓ Predictive Data Prefetching with ML Models")
    print("   5. ✓ Energy-aware Multi-objective Optimization")


def benchmark_optimization_techniques():
    """Benchmark different optimization techniques"""
    
    print("\n" + "="*80)
    print("Optimization Techniques Benchmark")
    print("="*80)
    
    data_dir = Path("data")
    test_files = list(data_dir.glob("*.json"))
    
    if not test_files:
        print("No test data found for benchmarking")
        return
    
    benchmark_results = {}
    
    for test_file in test_files[:3]:  # Limit to first 3 files for demo
        print(f"\nBenchmarking {test_file.name}...")
        
        try:
            scheduler = OptimizedNPUScheduler(str(test_file))
            file_results = {}
            
            # Test key strategies
            strategies = [
                SchedulingStrategy.HYBRID,
                SchedulingStrategy.RL_BASED,
                SchedulingStrategy.ENERGY_AWARE
            ]
            
            for strategy in strategies:
                try:
                    start_time = time.time()
                    result = scheduler.solve_problem1(strategy)
                    end_time = time.time()
                    
                    file_results[strategy.value] = {
                        'max_cache': result.max_cache,
                        'execution_time': end_time - start_time,
                        'improvement': 0.0  # Will calculate later
                    }
                    
                except Exception as e:
                    file_results[strategy.value] = {'error': str(e)}
            
            # Calculate improvements relative to hybrid
            if 'hybrid' in file_results and 'max_cache' in file_results['hybrid']:
                baseline = file_results['hybrid']['max_cache']
                
                for strategy, result in file_results.items():
                    if 'max_cache' in result:
                        improvement = (baseline - result['max_cache']) / baseline * 100
                        result['improvement'] = improvement
            
            benchmark_results[test_file.name] = file_results
            scheduler.cleanup_optimizers()
            
        except Exception as e:
            print(f"  ✗ Failed to benchmark {test_file.name}: {e}")
    
    # Print benchmark summary
    print("\n" + "="*80)
    print("Benchmark Results Summary")
    print("="*80)
    
    for filename, results in benchmark_results.items():
        print(f"\n📊 {filename}")
        print("-" * 40)
        
        for strategy, result in results.items():
            if 'max_cache' in result:
                cache = result['max_cache']
                time_taken = result['execution_time']
                improvement = result['improvement']
                
                print(f"  {strategy:<15}: {cache:>8} bytes ({improvement:+5.1f}%) in {time_taken:.3f}s")
            else:
                print(f"  {strategy:<15}: Failed")


if __name__ == "__main__":
    try:
        demonstrate_advanced_optimizations()
        benchmark_optimization_techniques()
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()
