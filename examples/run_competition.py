#!/usr/bin/env python
"""
竞赛运行主脚本
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from matplotlib import font_manager

from optimized_npu_scheduler import OptimizedNPUScheduler, SchedulingStrategy

# 配置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def run_competition():
    """运行竞赛求解"""
    print("="*70)
    print("2025年中国研究生数学建模竞赛A题 - NPU核内调度优化")
    print("="*70)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 定义测试案例
    test_cases = [
        # 原始简单案例
        "Matmul_Case0",
        "Matmul_Case1",
        "FlashAttention_Case0",
        "FlashAttention_Case1",
        "Conv_Case0",
        "Conv_Case1",
        # 复杂测试案例
        # "Matmul_Case0_Complex",
        # "Conv_Case0_Complex",
        # "FlashAttention_Case0_Complex"
    ]
    
    results_summary = []
    
    for case_name in test_cases:
        json_file = Path("data") / f"{case_name}.json"
        
        if not json_file.exists():
            print(f"警告: 文件 {json_file} 不存在，跳过")
            continue
        
        print(f"\n处理: {case_name}")
        print("-"*50)
        
        try:
            scheduler = OptimizedNPUScheduler(str(json_file))
            results = scheduler.solve_all(SchedulingStrategy.HYBRID)
            
            # 收集结果
            results_summary.append({
                "任务名": case_name,
                "节点数": results["node_count"],
                "边数": results["edge_count"],
                "最大缓存(bytes)": results["problem1"]["max_cache"],
                "SPILL数据量(bytes)": results["problem2"]["total_spill_data"],
                "执行时间(cycles)": results["problem3"]["makespan"],
                "求解时间(秒)": results["total_time"]
            })
            
            print(f"✓ 完成 - 最大缓存: {results['problem1']['max_cache']}, "
                  f"SPILL: {results['problem2']['total_spill_data']}, "
                  f"Makespan: {results['problem3']['makespan']}")
            
        except Exception as e:
            print(f"✗ 错误: {e}")
            continue
    
    # 生成结果报告
    if results_summary:
        df = pd.DataFrame(results_summary)
        
        # 保存到Excel
        excel_path = Path("output") / "competition_results.xlsx"
        df.to_excel(excel_path, index=False)
        print(f"\n结果已保存到: {excel_path}")
        
        # 打印汇总表
        print("\n" + "="*70)
        print("结果汇总")
        print("="*70)
        print(df.to_string(index=False))
        
        # 生成可视化
        generate_visualization(df)
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def generate_visualization(df):
    """生成结果可视化"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 1. 最大缓存对比
    ax = axes[0, 0]
    ax.bar(df['任务名'], df['最大缓存(bytes)'], color='skyblue')
    ax.set_xlabel('任务')
    ax.set_ylabel('最大缓存 (bytes)')
    ax.set_title('问题1: 最大缓存驻留')
    ax.tick_params(axis='x', rotation=45)
    
    # 2. SPILL数据量
    ax = axes[0, 1]
    ax.bar(df['任务名'], df['SPILL数据量(bytes)'], color='lightcoral')
    ax.set_xlabel('任务')
    ax.set_ylabel('SPILL数据量 (bytes)')
    ax.set_title('问题2: 总额外数据搬运量')
    ax.tick_params(axis='x', rotation=45)
    
    # 3. 执行时间
    ax = axes[1, 0]
    ax.bar(df['任务名'], df['执行时间(cycles)'], color='lightgreen')
    ax.set_xlabel('任务')
    ax.set_ylabel('执行时间 (cycles)')
    ax.set_title('问题3: 总执行时间')
    ax.tick_params(axis='x', rotation=45)
    
    # 4. 求解时间
    ax = axes[1, 1]
    ax.bar(df['任务名'], df['求解时间(秒)'], color='gold')
    ax.set_xlabel('任务')
    ax.set_ylabel('求解时间 (秒)')
    ax.set_title('算法运行时间')
    ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    output_file = Path("output") / "competition_results.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"可视化结果已保存到: {output_file}")
    
    plt.show()

if __name__ == "__main__":
    run_competition()