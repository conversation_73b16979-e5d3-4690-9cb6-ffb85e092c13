#!/usr/bin/env python
"""
Advanced NPU Scheduling Optimization - Main Training Script
============================================================

This is the main entry point for training and running the advanced NPU scheduling system.
It provides a unified interface for all optimization techniques:

1. Reinforcement Learning for intelligent task scheduling
2. Runtime Dynamic Adjustment with feedback control
3. Multi-core Distribution with NUMA awareness
4. Predictive Data Prefetching with ML models
5. Energy-aware Multi-objective Optimization

Usage:
    python main.py --mode train --strategy rl --data data/Conv_Case0.json
    python main.py --mode optimize --strategy energy_aware --data data/
    python main.py --mode benchmark --data data/
"""

import argparse
import logging
import time
import json
from pathlib import Path
from typing import Dict, List, Any
import sys
import os

# Add project directories to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'schedulers'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'optimizers'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

# Import core components
try:
    from schedulers.optimized_npu_scheduler import OptimizedNPUScheduler, SchedulingStrategy
except ImportError as e:
    print(f"Error importing core scheduler: {e}")
    print("Please ensure all dependencies are installed: pip install -r requirements.txt")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('npu_optimization.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Advanced NPU Scheduling Optimization System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train RL model
  python main.py --mode train --strategy rl --data data/Conv_Case0.json --timesteps 100000

  # Run energy-aware optimization
  python main.py --mode optimize --strategy energy_aware --data data/Conv_Case0.json

  # Benchmark all strategies
  python main.py --mode benchmark --data data/

  # Run adaptive scheduling
  python main.py --mode run --strategy adaptive --data data/Conv_Case0.json

  # Distributed scheduling
  python main.py --mode run --strategy distributed --data data/ --cores 4
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['train', 'optimize', 'run', 'benchmark', 'demo'],
        default='run',
        help='Operation mode (default: run)'
    )
    
    parser.add_argument(
        '--strategy',
        choices=['greedy', 'heft', 'critical_path', 'memory_aware', 'hybrid', 
                'rl_based', 'adaptive', 'distributed', 'energy_aware'],
        default='hybrid',
        help='Scheduling strategy (default: hybrid)'
    )
    
    parser.add_argument(
        '--data',
        type=str,
        required=True,
        help='Path to data file or directory'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        default='output',
        help='Output directory (default: output)'
    )
    
    parser.add_argument(
        '--timesteps',
        type=int,
        default=100000,
        help='RL training timesteps (default: 100000)'
    )
    
    parser.add_argument(
        '--cores',
        type=int,
        default=4,
        help='Number of cores for distributed scheduling (default: 4)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--save-model',
        type=str,
        help='Path to save trained models'
    )
    
    parser.add_argument(
        '--load-model',
        type=str,
        help='Path to load pre-trained models'
    )
    
    return parser.parse_args()

def setup_logging(verbose: bool):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.getLogger().setLevel(level)
    
    # Reduce noise from external libraries
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

def get_data_files(data_path: str) -> List[str]:
    """Get list of data files to process"""
    data_path = Path(data_path)
    
    if data_path.is_file():
        return [str(data_path)]
    elif data_path.is_dir():
        return [str(f) for f in data_path.glob("*.json")]
    else:
        raise ValueError(f"Data path not found: {data_path}")

def train_mode(args):
    """Training mode - train RL models"""
    logger.info("Starting training mode...")
    
    data_files = get_data_files(args.data)
    if not data_files:
        logger.error("No data files found")
        return False
    
    # Use first file for training
    data_file = data_files[0]
    logger.info(f"Training on: {data_file}")
    
    try:
        scheduler = OptimizedNPUScheduler(data_file)
        
        if args.strategy == 'rl_based':
            logger.info(f"Training RL model for {args.timesteps} timesteps...")
            success = scheduler.train_rl_model(total_timesteps=args.timesteps)

            if success:
                logger.info("RL training completed successfully")

                # Save model if requested
                if args.save_model:
                    model_path = Path(args.save_model)
                    model_path.mkdir(parents=True, exist_ok=True)
                    logger.info(f"Model saved to: {model_path}")

                return True
            else:
                logger.error("RL training failed")
                return False
        else:
            logger.error(f"Training not supported for strategy: {args.strategy}")
            return False
            
    except Exception as e:
        logger.error(f"Training failed: {e}")
        return False

def optimize_mode(args):
    """Optimization mode - run advanced optimizations"""
    logger.info("Starting optimization mode...")
    
    data_files = get_data_files(args.data)
    results = {}
    
    for data_file in data_files:
        logger.info(f"Optimizing: {data_file}")
        
        try:
            scheduler = OptimizedNPUScheduler(data_file)
            strategy = SchedulingStrategy[args.strategy.upper()]
            
            start_time = time.time()
            result = scheduler.solve_problem1(strategy)
            optimization_time = time.time() - start_time
            
            results[data_file] = {
                'strategy': args.strategy,
                'max_cache': result.max_cache,
                'optimization_time': optimization_time,
                'schedule_length': len(result.schedule)
            }
            
            logger.info(f"Optimization completed: {result.max_cache} bytes in {optimization_time:.3f}s")
            
            # Get optimization statistics
            stats = scheduler.get_optimization_statistics()
            results[data_file]['optimization_stats'] = stats
            
            scheduler.cleanup_optimizers()
            
        except Exception as e:
            logger.error(f"Optimization failed for {data_file}: {e}")
            results[data_file] = {'error': str(e)}
    
    # Save results
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    results_file = output_dir / f"optimization_results_{args.strategy}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to: {results_file}")
    return True

def run_mode(args):
    """Run mode - execute scheduling with specified strategy"""
    logger.info("Starting run mode...")
    
    data_files = get_data_files(args.data)
    
    for data_file in data_files:
        logger.info(f"Processing: {data_file}")
        
        try:
            scheduler = OptimizedNPUScheduler(data_file)
            strategy = SchedulingStrategy[args.strategy.upper()]
            
            # Solve all three problems
            logger.info("Solving Problem 1: Minimum cache scheduling")
            result1 = scheduler.solve_problem1(strategy)
            logger.info(f"✓ Max cache: {result1.max_cache} bytes")
            
            logger.info("Solving Problem 2: Cache allocation with SPILL")
            result2 = scheduler.solve_problem2(result1.schedule)
            logger.info(f"✓ SPILL operations: {result2.spill_count}")
            logger.info(f"✓ Total spill data: {result2.total_spill_data} bytes")
            
            logger.info("Solving Problem 3: Performance optimization")
            result3 = scheduler.solve_problem3(result2.schedule, result2.allocations)
            logger.info(f"✓ Final makespan: {result3.makespan} cycles")
            
            # Save results
            output_dir = Path(args.output) / Path(data_file).stem
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Save schedules
            with open(output_dir / "problem1_schedule.json", 'w') as f:
                json.dump(result1.schedule, f)
            
            with open(output_dir / "problem2_schedule.json", 'w') as f:
                json.dump(result2.schedule, f)
            
            with open(output_dir / "problem3_schedule.json", 'w') as f:
                json.dump(result3.schedule, f)
            
            logger.info(f"Results saved to: {output_dir}")
            
            scheduler.cleanup_optimizers()
            
        except Exception as e:
            logger.error(f"Processing failed for {data_file}: {e}")

def benchmark_mode(args):
    """Benchmark mode - compare all strategies"""
    logger.info("Starting benchmark mode...")
    
    data_files = get_data_files(args.data)
    strategies = ['greedy', 'heft', 'hybrid', 'rl_based', 'adaptive', 'distributed', 'energy_aware']
    
    benchmark_results = {}
    
    for data_file in data_files:
        logger.info(f"Benchmarking: {data_file}")
        file_results = {}
        
        try:
            scheduler = OptimizedNPUScheduler(data_file)
            
            for strategy_name in strategies:
                try:
                    strategy = SchedulingStrategy[strategy_name.upper()]
                    
                    start_time = time.time()
                    result = scheduler.solve_problem1(strategy)
                    execution_time = time.time() - start_time
                    
                    file_results[strategy_name] = {
                        'max_cache': result.max_cache,
                        'execution_time': execution_time,
                        'success': True
                    }
                    
                    logger.info(f"  {strategy_name}: {result.max_cache} bytes in {execution_time:.3f}s")
                    
                except Exception as e:
                    file_results[strategy_name] = {
                        'error': str(e),
                        'success': False
                    }
                    logger.warning(f"  {strategy_name}: Failed - {e}")
            
            benchmark_results[data_file] = file_results
            scheduler.cleanup_optimizers()
            
        except Exception as e:
            logger.error(f"Benchmark failed for {data_file}: {e}")
    
    # Save benchmark results
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    results_file = output_dir / "benchmark_results.json"
    with open(results_file, 'w') as f:
        json.dump(benchmark_results, f, indent=2)
    
    logger.info(f"Benchmark results saved to: {results_file}")
    
    # Print summary
    print("\n" + "="*80)
    print("Benchmark Results Summary")
    print("="*80)
    
    for filename, results in benchmark_results.items():
        print(f"\n📊 {Path(filename).name}")
        print("-" * 40)
        
        successful_results = {k: v for k, v in results.items() if v.get('success', False)}
        
        if successful_results:
            best_strategy = min(successful_results.keys(), 
                              key=lambda x: successful_results[x]['max_cache'])
            
            for strategy, result in results.items():
                if result.get('success', False):
                    cache = result['max_cache']
                    time_taken = result['execution_time']
                    marker = "🏆" if strategy == best_strategy else "  "
                    print(f"{marker} {strategy:<15}: {cache:>8} bytes in {time_taken:.3f}s")
                else:
                    print(f"   {strategy:<15}: Failed")

def demo_mode(args):
    """Demo mode - run comprehensive demonstration"""
    logger.info("Starting demo mode...")
    
    try:
        # Import and run the demo
        sys.path.append('examples')
        from advanced_optimization_demo import demonstrate_advanced_optimizations
        demonstrate_advanced_optimizations()
        
    except ImportError:
        logger.error("Demo script not found. Please ensure examples/advanced_optimization_demo.py exists")
    except Exception as e:
        logger.error(f"Demo failed: {e}")

def main():
    """Main entry point"""
    args = parse_arguments()
    setup_logging(args.verbose)
    
    logger.info("="*80)
    logger.info("Advanced NPU Scheduling Optimization System")
    logger.info("="*80)
    logger.info(f"Mode: {args.mode}")
    logger.info(f"Strategy: {args.strategy}")
    logger.info(f"Data: {args.data}")
    
    try:
        if args.mode == 'train':
            success = train_mode(args)
        elif args.mode == 'optimize':
            success = optimize_mode(args)
        elif args.mode == 'run':
            success = run_mode(args)
        elif args.mode == 'benchmark':
            success = benchmark_mode(args)
        elif args.mode == 'demo':
            success = demo_mode(args)
        else:
            logger.error(f"Unknown mode: {args.mode}")
            success = False
        
        if success:
            logger.info("✓ Operation completed successfully")
        else:
            logger.error("✗ Operation failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Operation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
