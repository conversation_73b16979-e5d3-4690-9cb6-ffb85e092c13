"""
Advanced Optimization Modules Package
=====================================

Five key optimization techniques for next-generation NPU schedulers:
1. Reinforcement Learning for intelligent task scheduling
2. Runtime Dynamic Adjustment with feedback control
3. Multi-core Distribution with NUMA awareness
4. Predictive Data Prefetching with ML models
5. Energy-aware Multi-objective Optimization
"""

# Import optimization modules with error handling
try:
    from .rl_scheduler import RLNPUScheduler, NPUSchedulingEnv
    RL_AVAILABLE = True
except ImportError:
    RL_AVAILABLE = False

try:
    from .dynamic_scheduler import AdaptiveScheduler, PerformanceMonitor
    DYNAMIC_AVAILABLE = True
except ImportError:
    DYNAMIC_AVAILABLE = False

try:
    from .multicore_scheduler import DistributedScheduler, NUMAManager
    MULTICORE_AVAILABLE = True
except ImportError:
    MULTICORE_AVAILABLE = False

try:
    from .prefetch_optimizer import NPUVector<PERSON><PERSON>head, MemoryLatencyHider
    PREFETCH_AVAILABLE = True
except ImportError:
    PREFETCH_AVAILABLE = False

try:
    from .energy_optimizer import EnergyAwareOptimizer, PowerProfile
    ENERGY_AVAILABLE = True
except ImportError:
    ENERGY_AVAILABLE = False

__all__ = [
    'RLNPUScheduler', 'NPUSchedulingEnv',
    'AdaptiveScheduler', 'PerformanceMonitor', 
    'DistributedScheduler', 'NUMAManager',
    'NPUVectorRunahead', 'MemoryLatencyHider',
    'EnergyAwareOptimizer', 'PowerProfile',
    'RL_AVAILABLE', 'DYNAMIC_AVAILABLE', 'MULTICORE_AVAILABLE',
    'PREFETCH_AVAILABLE', 'ENERGY_AVAILABLE'
]
