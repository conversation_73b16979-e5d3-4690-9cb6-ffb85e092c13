"""
Runtime Dynamic Adjustment for NPU Scheduling
Implements performance monitoring with feedback control theory
Includes work-stealing algorithms and adaptive scheduling
"""

import time
import threading
import queue
from collections import deque, defaultdict
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Any, Callable
import numpy as np
import psutil
import logging
from abc import ABC, abstractmethod
from tqdm import tqdm

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for monitoring"""
    timestamp: float
    cpu_utilization: float
    memory_usage: float
    cache_hit_ratio: float
    task_completion_rate: float
    queue_depth: int
    thermal_status: float
    prediction_error: float
    
    def to_dict(self) -> Dict[str, float]:
        return {
            'timestamp': self.timestamp,
            'cpu_utilization': self.cpu_utilization,
            'memory_usage': self.memory_usage,
            'cache_hit_ratio': self.cache_hit_ratio,
            'task_completion_rate': self.task_completion_rate,
            'queue_depth': self.queue_depth,
            'thermal_status': self.thermal_status,
            'prediction_error': self.prediction_error
        }


class PerformanceMonitor:
    """
    Real-time performance monitoring system
    Tracks NPU execution metrics with sub-millisecond sampling
    """
    
    def __init__(self, sampling_interval: float = 0.01):  # 10ms default
        self.sampling_interval = sampling_interval
        self.metrics_history = deque(maxlen=1000)
        self.is_monitoring = False
        self.monitor_thread = None
        self.callbacks = []
        
        # Metrics tracking
        self.task_start_times = {}
        self.task_predictions = {}
        self.completed_tasks = 0
        self.total_prediction_error = 0.0
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("Performance monitoring stopped")
    
    def add_callback(self, callback: Callable[[PerformanceMetrics], None]):
        """Add callback for metric updates"""
        self.callbacks.append(callback)
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Notify callbacks
                for callback in self.callbacks:
                    try:
                        callback(metrics)
                    except Exception as e:
                        logger.warning(f"Callback error: {e}")
                
                time.sleep(self.sampling_interval)
                
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(self.sampling_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics"""
        current_time = time.time()
        
        # System metrics
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # NPU-specific metrics (simulated for now)
        cache_hit_ratio = self._estimate_cache_hit_ratio()
        task_completion_rate = self._calculate_completion_rate()
        queue_depth = self._get_queue_depth()
        thermal_status = self._get_thermal_status()
        prediction_error = self._calculate_prediction_error()
        
        return PerformanceMetrics(
            timestamp=current_time,
            cpu_utilization=cpu_percent,
            memory_usage=memory_usage,
            cache_hit_ratio=cache_hit_ratio,
            task_completion_rate=task_completion_rate,
            queue_depth=queue_depth,
            thermal_status=thermal_status,
            prediction_error=prediction_error
        )
    
    def _estimate_cache_hit_ratio(self) -> float:
        """Estimate cache hit ratio"""
        # Simplified estimation based on recent performance
        if len(self.metrics_history) < 2:
            return 0.8  # Default assumption
        
        recent_metrics = list(self.metrics_history)[-10:]
        avg_completion_rate = np.mean([m.task_completion_rate for m in recent_metrics])
        
        # Higher completion rate suggests better cache performance
        return min(0.95, max(0.5, avg_completion_rate / 100.0))
    
    def _calculate_completion_rate(self) -> float:
        """Calculate task completion rate (tasks/second)"""
        if len(self.metrics_history) < 2:
            return 0.0
        
        time_window = 1.0  # 1 second window
        current_time = time.time()
        
        recent_completions = sum(1 for t in self.task_start_times.values() 
                               if current_time - t <= time_window)
        
        return float(recent_completions)
    
    def _get_queue_depth(self) -> int:
        """Get current queue depth"""
        # Simplified queue depth estimation
        return max(0, len(self.task_start_times) - self.completed_tasks)
    
    def _get_thermal_status(self) -> float:
        """Get thermal status (0.0 = cool, 1.0 = hot)"""
        # Simplified thermal estimation based on CPU usage
        if len(self.metrics_history) == 0:
            return 0.0
        
        recent_cpu = [m.cpu_utilization for m in list(self.metrics_history)[-5:]]
        avg_cpu = np.mean(recent_cpu)
        
        # Thermal status correlates with sustained high CPU usage
        return min(1.0, avg_cpu / 100.0)
    
    def _calculate_prediction_error(self) -> float:
        """Calculate prediction error rate"""
        if self.completed_tasks == 0:
            return 0.0
        
        return self.total_prediction_error / self.completed_tasks
    
    def record_task_start(self, task_id: int, predicted_time: float):
        """Record task start with prediction"""
        self.task_start_times[task_id] = time.time()
        self.task_predictions[task_id] = predicted_time
    
    def record_task_completion(self, task_id: int):
        """Record task completion"""
        if task_id not in self.task_start_times:
            return
        
        actual_time = time.time() - self.task_start_times[task_id]
        predicted_time = self.task_predictions.get(task_id, actual_time)
        
        # Calculate prediction error
        error = abs(actual_time - predicted_time) / max(predicted_time, 0.001)
        self.total_prediction_error += error
        self.completed_tasks += 1
        
        # Cleanup
        del self.task_start_times[task_id]
        if task_id in self.task_predictions:
            del self.task_predictions[task_id]
    
    def get_recent_metrics(self, window_size: int = 10) -> List[PerformanceMetrics]:
        """Get recent metrics"""
        return list(self.metrics_history)[-window_size:]


class FeedbackController:
    """
    Feedback control system for adaptive scheduling
    Uses PID control for stable adjustments
    """
    
    def __init__(self, kp: float = 1.0, ki: float = 0.1, kd: float = 0.05):
        self.kp = kp  # Proportional gain
        self.ki = ki  # Integral gain
        self.kd = kd  # Derivative gain
        
        self.setpoint = 0.8  # Target performance (80% efficiency)
        self.integral = 0.0
        self.previous_error = 0.0
        self.previous_time = time.time()
    
    def update(self, current_performance: float) -> float:
        """Update controller and return adjustment signal"""
        current_time = time.time()
        dt = current_time - self.previous_time
        
        if dt <= 0:
            return 0.0
        
        # Calculate error
        error = self.setpoint - current_performance
        
        # Proportional term
        proportional = self.kp * error
        
        # Integral term
        self.integral += error * dt
        integral = self.ki * self.integral
        
        # Derivative term
        derivative = self.kd * (error - self.previous_error) / dt
        
        # PID output
        output = proportional + integral + derivative
        
        # Update for next iteration
        self.previous_error = error
        self.previous_time = current_time
        
        return output
    
    def reset(self):
        """Reset controller state"""
        self.integral = 0.0
        self.previous_error = 0.0
        self.previous_time = time.time()


class WorkStealingScheduler:
    """
    Work-stealing scheduler for load balancing
    Implements A2WS (Adaptive Asynchronous Work-Stealing)
    """
    
    def __init__(self, num_workers: int = 4):
        self.num_workers = num_workers
        self.worker_queues = [deque() for _ in range(num_workers)]
        self.worker_locks = [threading.Lock() for _ in range(num_workers)]
        self.global_queue = queue.Queue()
        self.steal_attempts = defaultdict(int)
        self.execution_history = defaultdict(list)
    
    def add_task(self, task: Any, preferred_worker: Optional[int] = None):
        """Add task to scheduler"""
        if preferred_worker is not None and 0 <= preferred_worker < self.num_workers:
            with self.worker_locks[preferred_worker]:
                self.worker_queues[preferred_worker].append(task)
        else:
            self.global_queue.put(task)
    
    def get_task(self, worker_id: int) -> Optional[Any]:
        """Get next task for worker"""
        # Try local queue first
        with self.worker_locks[worker_id]:
            if self.worker_queues[worker_id]:
                return self.worker_queues[worker_id].popleft()
        
        # Try global queue
        try:
            return self.global_queue.get_nowait()
        except queue.Empty:
            pass
        
        # Try work stealing
        return self._steal_work(worker_id)
    
    def _steal_work(self, worker_id: int) -> Optional[Any]:
        """Attempt to steal work from other workers"""
        # Smart victim selection based on execution history
        victims = self._select_victims(worker_id)
        
        for victim_id in victims:
            if victim_id == worker_id:
                continue
            
            # Try to steal from victim's queue
            with self.worker_locks[victim_id]:
                if self.worker_queues[victim_id]:
                    task = self.worker_queues[victim_id].pop()  # Steal from end
                    self.steal_attempts[worker_id] += 1
                    return task
        
        return None
    
    def _select_victims(self, worker_id: int) -> List[int]:
        """Select victims for work stealing based on execution history"""
        # Calculate victim scores based on queue length and execution history
        victim_scores = []
        
        for i in range(self.num_workers):
            if i == worker_id:
                continue
            
            queue_length = len(self.worker_queues[i])
            avg_execution_time = np.mean(self.execution_history[i]) if self.execution_history[i] else 1.0
            
            # Higher score = better victim (more work, faster execution)
            score = queue_length / max(avg_execution_time, 0.1)
            victim_scores.append((score, i))
        
        # Sort by score (descending) and return worker IDs
        victim_scores.sort(reverse=True)
        return [worker_id for _, worker_id in victim_scores]
    
    def record_execution_time(self, worker_id: int, execution_time: float):
        """Record execution time for worker"""
        self.execution_history[worker_id].append(execution_time)
        
        # Keep only recent history
        if len(self.execution_history[worker_id]) > 100:
            self.execution_history[worker_id] = self.execution_history[worker_id][-50:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get work stealing statistics"""
        total_steals = sum(self.steal_attempts.values())
        queue_lengths = [len(q) for q in self.worker_queues]
        
        return {
            'total_steal_attempts': total_steals,
            'steal_attempts_per_worker': dict(self.steal_attempts),
            'queue_lengths': queue_lengths,
            'queue_balance': np.std(queue_lengths) if queue_lengths else 0.0
        }


class AdaptiveScheduler:
    """
    Adaptive NPU scheduler with runtime adjustment
    Combines performance monitoring, feedback control, and work stealing
    """

    def __init__(self, nodes: Dict, edges: List[Tuple], num_workers: int = 4):
        self.nodes = nodes
        self.edges = edges
        self.num_workers = num_workers

        # Initialize components
        self.monitor = PerformanceMonitor()
        self.controller = FeedbackController()
        self.work_stealer = WorkStealingScheduler(num_workers)

        # Scheduling state
        self.current_schedule = []
        self.scheduled_nodes = set()
        self.adaptation_history = []
        self.performance_threshold = 0.7  # Trigger adaptation below 70% efficiency

        # Setup monitoring callbacks
        self.monitor.add_callback(self._on_performance_update)

        # Adaptation parameters
        self.adaptation_params = {
            'priority_weights': {'memory': 0.4, 'time': 0.6},
            'scheduling_strategy': 'hybrid',
            'work_stealing_enabled': True,
            'prefetch_distance': 2
        }

    def start_adaptive_scheduling(self):
        """Start adaptive scheduling with monitoring"""
        self.monitor.start_monitoring()
        logger.info("Adaptive scheduling started")

    def stop_adaptive_scheduling(self):
        """Stop adaptive scheduling"""
        self.monitor.stop_monitoring()
        logger.info("Adaptive scheduling stopped")

    def _on_performance_update(self, metrics: PerformanceMetrics):
        """Handle performance metric updates"""
        # Calculate overall performance score
        performance_score = self._calculate_performance_score(metrics)

        # Get control signal
        control_signal = self.controller.update(performance_score)

        # Apply adaptations if needed
        if performance_score < self.performance_threshold or abs(control_signal) > 0.1:
            self._apply_adaptations(metrics, control_signal)

    def _calculate_performance_score(self, metrics: PerformanceMetrics) -> float:
        """Calculate overall performance score (0.0 to 1.0)"""
        # Weighted combination of metrics
        weights = {
            'cpu_utilization': 0.3,
            'cache_hit_ratio': 0.3,
            'task_completion_rate': 0.2,
            'prediction_accuracy': 0.2
        }

        # Normalize metrics
        cpu_score = min(1.0, metrics.cpu_utilization / 80.0)  # Target 80% CPU
        cache_score = metrics.cache_hit_ratio
        completion_score = min(1.0, metrics.task_completion_rate / 10.0)  # Target 10 tasks/sec
        prediction_score = max(0.0, 1.0 - metrics.prediction_error)

        # Thermal penalty
        thermal_penalty = metrics.thermal_status * 0.2

        score = (weights['cpu_utilization'] * cpu_score +
                weights['cache_hit_ratio'] * cache_score +
                weights['task_completion_rate'] * completion_score +
                weights['prediction_accuracy'] * prediction_score) - thermal_penalty

        return max(0.0, min(1.0, score))

    def _apply_adaptations(self, metrics: PerformanceMetrics, control_signal: float):
        """Apply scheduling adaptations based on performance"""
        adaptations = []

        # Memory pressure adaptation
        if metrics.memory_usage > 85.0:
            self.adaptation_params['priority_weights']['memory'] = 0.7
            self.adaptation_params['priority_weights']['time'] = 0.3
            adaptations.append("Increased memory priority")

        # Thermal throttling adaptation
        if metrics.thermal_status > 0.8:
            self.adaptation_params['scheduling_strategy'] = 'conservative'
            adaptations.append("Switched to conservative scheduling")

        # Cache miss adaptation
        if metrics.cache_hit_ratio < 0.6:
            self.adaptation_params['prefetch_distance'] = min(5,
                self.adaptation_params['prefetch_distance'] + 1)
            adaptations.append("Increased prefetch distance")

        # Queue depth adaptation
        if metrics.queue_depth > 20:
            self.adaptation_params['work_stealing_enabled'] = True
            adaptations.append("Enabled work stealing")

        # Prediction error adaptation
        if metrics.prediction_error > 0.3:
            self._adjust_prediction_model()
            adaptations.append("Adjusted prediction model")

        # Log adaptations
        if adaptations:
            self.adaptation_history.append({
                'timestamp': metrics.timestamp,
                'performance_score': self._calculate_performance_score(metrics),
                'control_signal': control_signal,
                'adaptations': adaptations,
                'params': self.adaptation_params.copy()
            })

            logger.debug(f"Applied adaptations: {', '.join(adaptations)}")

    def _adjust_prediction_model(self):
        """Adjust prediction model based on recent errors"""
        # Simple adaptation: adjust prediction confidence
        recent_metrics = self.monitor.get_recent_metrics(10)
        if recent_metrics:
            avg_error = np.mean([m.prediction_error for m in recent_metrics])

            # Increase conservatism if predictions are poor
            if avg_error > 0.5:
                self.adaptation_params['prediction_conservatism'] = 1.2
            else:
                self.adaptation_params['prediction_conservatism'] = 1.0

    def generate_adaptive_schedule(self, base_schedule: List[int]) -> List[int]:
        """Generate adaptive schedule based on current conditions"""
        if not self.adaptation_history:
            return base_schedule

        # Get latest adaptation parameters
        latest_params = self.adaptation_params

        # Apply adaptations to schedule
        adapted_schedule = self._apply_schedule_adaptations(base_schedule, latest_params)

        return adapted_schedule

    def _apply_schedule_adaptations(self, schedule: List[int], params: Dict) -> List[int]:
        """Apply adaptation parameters to schedule"""
        adapted_schedule = schedule.copy()

        # Memory-first adaptation
        if params['priority_weights']['memory'] > 0.5:
            adapted_schedule = self._prioritize_memory_operations(adapted_schedule)

        # Conservative scheduling adaptation
        if params.get('scheduling_strategy') == 'conservative':
            adapted_schedule = self._apply_conservative_scheduling(adapted_schedule)

        # Work stealing preparation
        if params.get('work_stealing_enabled'):
            adapted_schedule = self._prepare_for_work_stealing(adapted_schedule)

        return adapted_schedule

    def _prioritize_memory_operations(self, schedule: List[int]) -> List[int]:
        """Prioritize memory operations (FREE before ALLOC)"""
        memory_ops = []
        other_ops = []

        for node_id in schedule:
            node = self.nodes[node_id]
            if node['op'] in ['ALLOC', 'FREE']:
                memory_ops.append(node_id)
            else:
                other_ops.append(node_id)

        # Sort memory operations: FREE first, then small ALLOC
        memory_ops.sort(key=lambda x: (
            0 if self.nodes[x]['op'] == 'FREE' else 1,
            self.nodes[x].get('size', 0) if self.nodes[x]['op'] == 'ALLOC' else 0
        ))

        # Interleave memory and compute operations
        adapted = []
        mem_idx = 0
        other_idx = 0

        while mem_idx < len(memory_ops) or other_idx < len(other_ops):
            # Add memory operation if available and needed
            if mem_idx < len(memory_ops):
                adapted.append(memory_ops[mem_idx])
                mem_idx += 1

            # Add compute operation if available
            if other_idx < len(other_ops):
                adapted.append(other_ops[other_idx])
                other_idx += 1

        return adapted

    def _apply_conservative_scheduling(self, schedule: List[int]) -> List[int]:
        """Apply conservative scheduling to reduce thermal stress"""
        # Insert small delays between intensive operations
        adapted = []

        for i, node_id in enumerate(schedule):
            adapted.append(node_id)

            node = self.nodes[node_id]
            # Add cooling periods after high-cycle operations
            if node.get('cycles', 0) > 100:
                # Insert a low-impact operation if available
                for next_node_id in schedule[i+1:]:
                    next_node = self.nodes[next_node_id]
                    if next_node.get('cycles', 0) < 10:
                        # Move this low-impact operation forward
                        adapted.append(next_node_id)
                        schedule.remove(next_node_id)
                        break

        return adapted

    def _prepare_for_work_stealing(self, schedule: List[int]) -> List[int]:
        """Prepare schedule for work stealing by identifying parallel opportunities"""
        # Group independent operations for parallel execution
        parallel_groups = []
        current_group = []
        dependencies = set()

        for node_id in schedule:
            node = self.nodes[node_id]
            node_deps = set(node.get('predecessors', []))

            # Check if this node can be grouped with current group
            if not (node_deps & set(current_group)):
                current_group.append(node_id)
            else:
                # Start new group
                if current_group:
                    parallel_groups.append(current_group)
                current_group = [node_id]

        if current_group:
            parallel_groups.append(current_group)

        # Flatten groups while maintaining some parallelism info
        adapted = []
        for group in parallel_groups:
            adapted.extend(group)

        return adapted

    def get_adaptation_statistics(self) -> Dict[str, Any]:
        """Get adaptation statistics"""
        if not self.adaptation_history:
            return {}

        recent_adaptations = self.adaptation_history[-10:]

        return {
            'total_adaptations': len(self.adaptation_history),
            'recent_performance_scores': [a['performance_score'] for a in recent_adaptations],
            'adaptation_frequency': len(recent_adaptations) / max(1, len(self.adaptation_history)),
            'current_params': self.adaptation_params.copy(),
            'work_stealing_stats': self.work_stealer.get_statistics()
        }
