"""
Energy-aware Multi-objective Optimization for NPU Scheduling
Implements NSGA-II/MOEA-D algorithms with DVFS integration and carbon-aware scheduling
Uses Pymoo framework for sustainable computing optimization
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass
from collections import defaultdict
import time
import logging
import requests
import threading
from abc import ABC, abstractmethod

# Optional imports with fallbacks
try:
    from pymoo.algorithms.moo.nsga2 import NSGA2
    from pymoo.algorithms.moo.moead import MOEAD
    from pymoo.core.problem import Problem
    from pymoo.optimize import minimize
    from pymoo.operators.crossover.sbx import SBX
    from pymoo.operators.mutation.pm import PM
    from pymoo.operators.sampling.rnd import FloatRandomSampling
    PYMOO_AVAILABLE = True
except ImportError:
    PYMOO_AVAILABLE = False
    logging.warning("Pymoo not available, using simplified optimization")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available, using simulated power monitoring")

logger = logging.getLogger(__name__)

@dataclass
class PowerProfile:
    """Power consumption profile for different operating modes"""
    idle_power: float  # Watts
    compute_power: float  # Watts per operation
    memory_power: float  # Watts per MB
    thermal_coefficient: float  # Power increase per degree C
    dvfs_levels: Dict[str, Tuple[float, float]]  # frequency -> (voltage, power_multiplier)

@dataclass
class EnergyMetrics:
    """Energy consumption metrics"""
    total_energy: float  # Joules
    compute_energy: float  # Joules
    memory_energy: float  # Joules
    idle_energy: float  # Joules
    carbon_footprint: float  # gCO2eq
    power_efficiency: float  # Operations per Watt

@dataclass
class OptimizationObjectives:
    """Multi-objective optimization objectives"""
    makespan: float  # Execution time (minimize)
    energy: float  # Energy consumption (minimize)
    memory_usage: float  # Peak memory usage (minimize)
    thermal_impact: float  # Thermal impact (minimize)
    carbon_footprint: float  # Carbon emissions (minimize)


class PowerMonitor:
    """
    Real-time power monitoring system
    Integrates with hardware power measurement interfaces
    """
    
    def __init__(self, sampling_interval: float = 0.1):
        self.sampling_interval = sampling_interval
        self.power_history = []
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Power measurement interfaces
        self.rapl_available = self._check_rapl_availability()
        self.nvml_available = self._check_nvml_availability()
        
        # Baseline power consumption
        self.baseline_power = self._measure_baseline_power()
    
    def _check_rapl_availability(self) -> bool:
        """Check if Intel RAPL is available"""
        try:
            import os
            rapl_path = "/sys/class/powercap/intel-rapl"
            return os.path.exists(rapl_path)
        except:
            return False
    
    def _check_nvml_availability(self) -> bool:
        """Check if NVIDIA NVML is available"""
        try:
            import pynvml
            pynvml.nvmlInit()
            return True
        except:
            return False
    
    def _measure_baseline_power(self) -> float:
        """Measure baseline power consumption"""
        if self.rapl_available:
            return self._read_rapl_power()
        elif PSUTIL_AVAILABLE:
            # Estimate based on CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            return 10.0 + (cpu_percent / 100.0) * 50.0  # 10-60W range
        else:
            return 25.0  # Default assumption
    
    def _read_rapl_power(self) -> float:
        """Read power from Intel RAPL"""
        try:
            rapl_path = "/sys/class/powercap/intel-rapl/intel-rapl:0/energy_uj"
            with open(rapl_path, 'r') as f:
                energy_uj = int(f.read().strip())
                return energy_uj / 1000000.0  # Convert to Joules
        except:
            return 0.0
    
    def start_monitoring(self):
        """Start power monitoring"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("Power monitoring started")
    
    def stop_monitoring(self):
        """Stop power monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("Power monitoring stopped")
    
    def _monitoring_loop(self):
        """Main power monitoring loop"""
        last_energy = self._read_rapl_power() if self.rapl_available else 0.0
        last_time = time.time()
        
        while self.is_monitoring:
            try:
                current_time = time.time()
                
                if self.rapl_available:
                    current_energy = self._read_rapl_power()
                    power = (current_energy - last_energy) / (current_time - last_time)
                    last_energy = current_energy
                else:
                    # Estimate power based on system metrics
                    power = self._estimate_power()
                
                self.power_history.append({
                    'timestamp': current_time,
                    'power': power,
                    'energy': current_energy if self.rapl_available else 0.0
                })
                
                # Maintain history size
                if len(self.power_history) > 1000:
                    self.power_history = self.power_history[-500:]
                
                last_time = current_time
                time.sleep(self.sampling_interval)
                
            except Exception as e:
                logger.error(f"Power monitoring error: {e}")
                time.sleep(self.sampling_interval)
    
    def _estimate_power(self) -> float:
        """Estimate power consumption based on system metrics"""
        if not PSUTIL_AVAILABLE:
            return self.baseline_power
        
        # CPU power
        cpu_percent = psutil.cpu_percent()
        cpu_power = 10.0 + (cpu_percent / 100.0) * 40.0  # 10-50W for CPU
        
        # Memory power
        memory = psutil.virtual_memory()
        memory_power = (memory.used / (1024**3)) * 2.0  # 2W per GB
        
        # Disk I/O power
        disk_io = psutil.disk_io_counters()
        if hasattr(disk_io, 'read_bytes') and hasattr(disk_io, 'write_bytes'):
            io_power = min(5.0, (disk_io.read_bytes + disk_io.write_bytes) / (1024**3))
        else:
            io_power = 1.0
        
        total_power = cpu_power + memory_power + io_power
        return total_power
    
    def get_current_power(self) -> float:
        """Get current power consumption"""
        if self.power_history:
            return self.power_history[-1]['power']
        return self.baseline_power
    
    def get_energy_consumption(self, start_time: float, end_time: float) -> float:
        """Get energy consumption over time period"""
        relevant_samples = [
            sample for sample in self.power_history
            if start_time <= sample['timestamp'] <= end_time
        ]
        
        if len(relevant_samples) < 2:
            duration = end_time - start_time
            return self.baseline_power * duration
        
        # Integrate power over time
        total_energy = 0.0
        for i in range(1, len(relevant_samples)):
            dt = relevant_samples[i]['timestamp'] - relevant_samples[i-1]['timestamp']
            avg_power = (relevant_samples[i]['power'] + relevant_samples[i-1]['power']) / 2
            total_energy += avg_power * dt
        
        return total_energy


class DVFSController:
    """
    Dynamic Voltage and Frequency Scaling controller
    Manages power-performance trade-offs through frequency scaling
    """
    
    def __init__(self, power_profile: PowerProfile):
        self.power_profile = power_profile
        self.current_frequency = 'nominal'
        self.frequency_history = []
        
        # Available DVFS levels
        self.dvfs_levels = power_profile.dvfs_levels
        self.frequency_order = list(self.dvfs_levels.keys())
    
    def set_frequency(self, frequency: str) -> bool:
        """Set CPU frequency level"""
        if frequency not in self.dvfs_levels:
            logger.warning(f"Invalid frequency level: {frequency}")
            return False
        
        try:
            # In real implementation, this would call hardware interface
            # For simulation, we just track the setting
            old_frequency = self.current_frequency
            self.current_frequency = frequency
            
            self.frequency_history.append({
                'timestamp': time.time(),
                'old_frequency': old_frequency,
                'new_frequency': frequency
            })
            
            logger.debug(f"DVFS: {old_frequency} -> {frequency}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set frequency: {e}")
            return False
    
    def get_optimal_frequency(self, workload_intensity: float, 
                            thermal_status: float) -> str:
        """Get optimal frequency for current conditions"""
        # Simple policy: reduce frequency if thermal stress is high
        if thermal_status > 0.8:
            # High thermal stress - use lower frequency
            return 'low'
        elif thermal_status > 0.6:
            # Medium thermal stress - use medium frequency
            return 'medium'
        elif workload_intensity > 0.8:
            # High workload - use high frequency
            return 'high'
        else:
            # Normal conditions - use nominal frequency
            return 'nominal'
    
    def calculate_power_multiplier(self, frequency: str) -> float:
        """Calculate power multiplier for frequency level"""
        if frequency in self.dvfs_levels:
            _, power_multiplier = self.dvfs_levels[frequency]
            return power_multiplier
        return 1.0
    
    def calculate_performance_multiplier(self, frequency: str) -> float:
        """Calculate performance multiplier for frequency level"""
        # Simplified: performance scales linearly with frequency
        frequency_map = {
            'low': 0.6,
            'medium': 0.8,
            'nominal': 1.0,
            'high': 1.2,
            'turbo': 1.4
        }
        return frequency_map.get(frequency, 1.0)


class CarbonAwareScheduler:
    """
    Carbon-aware scheduler that considers grid carbon intensity
    Optimizes scheduling based on renewable energy availability
    """
    
    def __init__(self, region: str = "US"):
        self.region = region
        self.carbon_intensity_cache = {}
        self.cache_timeout = 3600  # 1 hour cache
        self.default_intensity = 400  # gCO2/kWh default
    
    def get_carbon_intensity(self) -> float:
        """Get current carbon intensity of electricity grid"""
        current_time = time.time()
        
        # Check cache
        if (self.region in self.carbon_intensity_cache and
            current_time - self.carbon_intensity_cache[self.region]['timestamp'] < self.cache_timeout):
            return self.carbon_intensity_cache[self.region]['intensity']
        
        # Fetch from API (simplified - would use real carbon intensity API)
        try:
            intensity = self._fetch_carbon_intensity_api()
            
            self.carbon_intensity_cache[self.region] = {
                'intensity': intensity,
                'timestamp': current_time
            }
            
            return intensity
            
        except Exception as e:
            logger.warning(f"Failed to fetch carbon intensity: {e}")
            return self.default_intensity
    
    def _fetch_carbon_intensity_api(self) -> float:
        """Fetch carbon intensity from external API"""
        # Simplified simulation - in practice would use APIs like:
        # - WattTime API
        # - ElectricityMap API
        # - Grid carbon intensity APIs
        
        # Simulate time-varying carbon intensity
        hour = time.localtime().tm_hour
        
        # Lower intensity during day (more solar), higher at night
        base_intensity = 300
        daily_variation = 100 * np.sin(2 * np.pi * (hour - 6) / 24)
        
        # Add some randomness
        random_variation = np.random.normal(0, 20)
        
        intensity = base_intensity + daily_variation + random_variation
        return max(200, min(600, intensity))  # Clamp to reasonable range
    
    def calculate_carbon_footprint(self, energy_consumption: float) -> float:
        """Calculate carbon footprint for energy consumption"""
        intensity = self.get_carbon_intensity()  # gCO2/kWh
        energy_kwh = energy_consumption / 3600000.0  # Convert J to kWh
        
        carbon_footprint = intensity * energy_kwh  # gCO2
        return carbon_footprint
    
    def get_optimal_scheduling_window(self, duration_hours: float) -> Tuple[float, float]:
        """Get optimal time window for scheduling based on carbon intensity"""
        # Predict carbon intensity for next 24 hours
        current_time = time.time()
        predictions = []
        
        for hour_offset in range(24):
            future_time = current_time + hour_offset * 3600
            # Simplified prediction - in practice would use forecasting
            predicted_intensity = self._predict_carbon_intensity(hour_offset)
            predictions.append((future_time, predicted_intensity))
        
        # Find window with lowest average carbon intensity
        best_start_time = current_time
        best_avg_intensity = float('inf')
        
        window_hours = int(np.ceil(duration_hours))
        
        for i in range(24 - window_hours + 1):
            window_intensities = [predictions[j][1] for j in range(i, i + window_hours)]
            avg_intensity = np.mean(window_intensities)
            
            if avg_intensity < best_avg_intensity:
                best_avg_intensity = avg_intensity
                best_start_time = predictions[i][0]
        
        best_end_time = best_start_time + duration_hours * 3600
        
        return best_start_time, best_end_time
    
    def _predict_carbon_intensity(self, hour_offset: int) -> float:
        """Predict carbon intensity for future hour"""
        # Simplified prediction based on daily patterns
        current_hour = time.localtime().tm_hour
        future_hour = (current_hour + hour_offset) % 24
        
        # Model daily carbon intensity pattern
        base_intensity = 300
        daily_variation = 100 * np.sin(2 * np.pi * (future_hour - 6) / 24)
        
        return base_intensity + daily_variation


class NPUSchedulingProblem(Problem):
    """
    Multi-objective optimization problem for NPU scheduling
    Compatible with Pymoo optimization framework
    """
    
    def __init__(self, nodes: Dict, edges: List[Tuple], 
                 power_profile: PowerProfile, **kwargs):
        self.nodes = nodes
        self.edges = edges
        self.power_profile = power_profile
        self.carbon_scheduler = CarbonAwareScheduler()
        
        n_tasks = len(nodes)
        
        # Decision variables: task scheduling order and DVFS levels
        n_var = n_tasks + n_tasks  # schedule + frequency levels
        n_obj = 5  # makespan, energy, memory, thermal, carbon
        n_constr = 2  # dependency constraints, thermal constraints
        
        # Variable bounds
        xl = np.concatenate([
            np.zeros(n_tasks),  # Schedule positions
            np.zeros(n_tasks)   # Frequency levels (0-4)
        ])
        
        xu = np.concatenate([
            np.full(n_tasks, n_tasks - 1),  # Schedule positions
            np.full(n_tasks, 4)  # Frequency levels (0-4)
        ])
        
        super().__init__(n_var=n_var, n_obj=n_obj, n_constr=n_constr,
                         xl=xl, xu=xu, **kwargs)
    
    def _evaluate(self, X, out, *args, **kwargs):
        """Evaluate objectives and constraints for population"""
        n_pop = X.shape[0]
        n_tasks = len(self.nodes)
        
        # Initialize output arrays
        F = np.zeros((n_pop, self.n_obj))
        G = np.zeros((n_pop, self.n_constr))
        
        for i in range(n_pop):
            # Decode solution
            schedule_vars = X[i, :n_tasks]
            freq_vars = X[i, n_tasks:]
            
            # Convert to actual schedule
            schedule = self._decode_schedule(schedule_vars)
            frequencies = self._decode_frequencies(freq_vars)
            
            # Evaluate objectives
            objectives = self._evaluate_objectives(schedule, frequencies)
            F[i, 0] = objectives.makespan
            F[i, 1] = objectives.energy
            F[i, 2] = objectives.memory_usage
            F[i, 3] = objectives.thermal_impact
            F[i, 4] = objectives.carbon_footprint
            
            # Evaluate constraints
            G[i, 0] = self._dependency_constraint_violation(schedule)
            G[i, 1] = self._thermal_constraint_violation(objectives.thermal_impact)
        
        out["F"] = F
        out["G"] = G
    
    def _decode_schedule(self, schedule_vars: np.ndarray) -> List[int]:
        """Decode schedule variables to task order"""
        # Sort tasks by schedule variables
        task_ids = list(self.nodes.keys())
        sorted_pairs = sorted(zip(schedule_vars, task_ids))
        schedule = [task_id for _, task_id in sorted_pairs]
        
        # Fix dependency violations
        return self._fix_dependencies(schedule)
    
    def _decode_frequencies(self, freq_vars: np.ndarray) -> List[str]:
        """Decode frequency variables to DVFS levels"""
        freq_levels = ['low', 'medium', 'nominal', 'high', 'turbo']
        frequencies = []
        
        for freq_var in freq_vars:
            level_idx = int(np.clip(freq_var, 0, len(freq_levels) - 1))
            frequencies.append(freq_levels[level_idx])
        
        return frequencies
    
    def _fix_dependencies(self, schedule: List[int]) -> List[int]:
        """Fix dependency violations in schedule"""
        # Build dependency map
        dependencies = defaultdict(set)
        for start, end in self.edges:
            dependencies[end].add(start)
        
        # Topological sort to fix violations
        fixed_schedule = []
        remaining = set(schedule)
        scheduled = set()
        
        while remaining:
            # Find nodes with satisfied dependencies
            ready = [nid for nid in remaining 
                    if dependencies[nid].issubset(scheduled)]
            
            if not ready:
                # Break cycles by adding arbitrary node
                ready = [next(iter(remaining))]
            
            # Sort by original order preference
            ready.sort(key=lambda x: schedule.index(x))
            
            # Add first ready node
            node_id = ready[0]
            fixed_schedule.append(node_id)
            scheduled.add(node_id)
            remaining.remove(node_id)
        
        return fixed_schedule
    
    def _evaluate_objectives(self, schedule: List[int], 
                           frequencies: List[str]) -> OptimizationObjectives:
        """Evaluate all optimization objectives"""
        # Calculate makespan with DVFS
        makespan = self._calculate_makespan_with_dvfs(schedule, frequencies)
        
        # Calculate energy consumption
        energy = self._calculate_energy_consumption(schedule, frequencies)
        
        # Calculate memory usage
        memory_usage = self._calculate_memory_usage(schedule)
        
        # Calculate thermal impact
        thermal_impact = self._calculate_thermal_impact(schedule, frequencies)
        
        # Calculate carbon footprint
        carbon_footprint = self.carbon_scheduler.calculate_carbon_footprint(energy)
        
        return OptimizationObjectives(
            makespan=makespan,
            energy=energy,
            memory_usage=memory_usage,
            thermal_impact=thermal_impact,
            carbon_footprint=carbon_footprint
        )
    
    def _calculate_makespan_with_dvfs(self, schedule: List[int], 
                                    frequencies: List[str]) -> float:
        """Calculate makespan considering DVFS"""
        finish_times = {}
        processor_finish = defaultdict(float)
        
        for i, node_id in enumerate(schedule):
            node = self.nodes[node_id]
            frequency = frequencies[i] if i < len(frequencies) else 'nominal'
            
            # Performance scaling
            dvfs_controller = DVFSController(self.power_profile)
            perf_multiplier = dvfs_controller.calculate_performance_multiplier(frequency)
            
            # Adjusted execution time
            base_cycles = node.get('cycles', 0)
            adjusted_cycles = base_cycles / perf_multiplier
            
            # Calculate start time
            start_time = 0.0
            
            # Dependency constraints
            for pred_id in node.get('predecessors', []):
                if pred_id in finish_times:
                    start_time = max(start_time, finish_times[pred_id])
            
            # Resource constraints
            pipe = node.get('pipe')
            if pipe:
                start_time = max(start_time, processor_finish[pipe])
            
            # Calculate finish time
            finish_time = start_time + adjusted_cycles
            finish_times[node_id] = finish_time
            
            if pipe:
                processor_finish[pipe] = finish_time
        
        return max(finish_times.values()) if finish_times else 0.0
    
    def _calculate_energy_consumption(self, schedule: List[int], 
                                    frequencies: List[str]) -> float:
        """Calculate total energy consumption"""
        total_energy = 0.0
        dvfs_controller = DVFSController(self.power_profile)
        
        for i, node_id in enumerate(schedule):
            node = self.nodes[node_id]
            frequency = frequencies[i] if i < len(frequencies) else 'nominal'
            
            # Base power consumption
            base_power = self.power_profile.compute_power
            
            # DVFS scaling
            power_multiplier = dvfs_controller.calculate_power_multiplier(frequency)
            actual_power = base_power * power_multiplier
            
            # Execution time
            base_cycles = node.get('cycles', 0)
            perf_multiplier = dvfs_controller.calculate_performance_multiplier(frequency)
            execution_time = base_cycles / perf_multiplier
            
            # Energy = Power × Time
            node_energy = actual_power * execution_time
            total_energy += node_energy
            
            # Memory energy
            if node.get('op') in ['ALLOC', 'FREE']:
                memory_energy = node.get('size', 0) * self.power_profile.memory_power * 0.001
                total_energy += memory_energy
        
        return total_energy
    
    def _calculate_memory_usage(self, schedule: List[int]) -> float:
        """Calculate peak memory usage"""
        current_memory = 0.0
        peak_memory = 0.0
        buffer_sizes = {}
        
        for node_id in schedule:
            node = self.nodes[node_id]
            
            if node['op'] == 'ALLOC':
                size = node.get('size', 0)
                buffer_sizes[node.get('buf_id')] = size
                current_memory += size
                peak_memory = max(peak_memory, current_memory)
            elif node['op'] == 'FREE':
                buf_id = node.get('buf_id')
                if buf_id in buffer_sizes:
                    current_memory -= buffer_sizes[buf_id]
                    del buffer_sizes[buf_id]
        
        return peak_memory
    
    def _calculate_thermal_impact(self, schedule: List[int], 
                                frequencies: List[str]) -> float:
        """Calculate thermal impact score"""
        thermal_accumulation = 0.0
        dvfs_controller = DVFSController(self.power_profile)
        
        for i, node_id in enumerate(schedule):
            node = self.nodes[node_id]
            frequency = frequencies[i] if i < len(frequencies) else 'nominal'
            
            # Power consumption
            base_power = self.power_profile.compute_power
            power_multiplier = dvfs_controller.calculate_power_multiplier(frequency)
            power = base_power * power_multiplier
            
            # Thermal contribution (simplified model)
            execution_time = node.get('cycles', 0)
            thermal_contribution = power * execution_time * 0.001  # Simplified thermal model
            
            thermal_accumulation += thermal_contribution
        
        return thermal_accumulation
    
    def _dependency_constraint_violation(self, schedule: List[int]) -> float:
        """Calculate dependency constraint violation"""
        violations = 0
        scheduled_positions = {node_id: i for i, node_id in enumerate(schedule)}
        
        for start, end in self.edges:
            if (start in scheduled_positions and end in scheduled_positions and
                scheduled_positions[start] >= scheduled_positions[end]):
                violations += 1
        
        return float(violations)
    
    def _thermal_constraint_violation(self, thermal_impact: float) -> float:
        """Calculate thermal constraint violation"""
        thermal_limit = 1000.0  # Arbitrary thermal limit
        return max(0.0, thermal_impact - thermal_limit)


class EnergyAwareOptimizer:
    """
    Main energy-aware multi-objective optimizer
    Integrates NSGA-II/MOEA-D with DVFS and carbon-aware scheduling
    """

    def __init__(self, nodes: Dict, edges: List[Tuple],
                 power_profile: Optional[PowerProfile] = None):
        self.nodes = nodes
        self.edges = edges

        # Default power profile if not provided
        if power_profile is None:
            self.power_profile = PowerProfile(
                idle_power=10.0,
                compute_power=50.0,
                memory_power=2.0,
                thermal_coefficient=0.1,
                dvfs_levels={
                    'low': (0.8, 0.6),
                    'medium': (0.9, 0.8),
                    'nominal': (1.0, 1.0),
                    'high': (1.1, 1.3),
                    'turbo': (1.2, 1.6)
                }
            )
        else:
            self.power_profile = power_profile

        # Initialize components
        self.power_monitor = PowerMonitor()
        self.dvfs_controller = DVFSController(self.power_profile)
        self.carbon_scheduler = CarbonAwareScheduler()

        # Optimization results
        self.pareto_front = None
        self.optimization_history = []

    def optimize_schedule(self, algorithm: str = "nsga2",
                         population_size: int = 100,
                         generations: int = 200) -> Dict[str, Any]:
        """
        Optimize NPU schedule using multi-objective optimization

        Args:
            algorithm: "nsga2" or "moead"
            population_size: Size of optimization population
            generations: Number of optimization generations

        Returns:
            Optimization results including Pareto front
        """
        if not PYMOO_AVAILABLE:
            logger.warning("Pymoo not available, using simplified optimization")
            return self._simplified_optimization()

        # Create optimization problem
        problem = NPUSchedulingProblem(self.nodes, self.edges, self.power_profile)

        # Select algorithm
        if algorithm.lower() == "nsga2":
            optimizer = NSGA2(
                pop_size=population_size,
                sampling=FloatRandomSampling(),
                crossover=SBX(prob=0.9, eta=15),
                mutation=PM(prob=1.0/problem.n_var, eta=20),
                eliminate_duplicates=True
            )
        elif algorithm.lower() == "moead":
            optimizer = MOEAD(
                n_neighbors=15,
                decomposition="pbi",
                prob_neighbor_mating=0.7,
                sampling=FloatRandomSampling(),
                crossover=SBX(prob=0.9, eta=15),
                mutation=PM(prob=1.0/problem.n_var, eta=20)
            )
        else:
            raise ValueError(f"Unknown algorithm: {algorithm}")

        # Run optimization
        logger.info(f"Starting {algorithm.upper()} optimization...")
        start_time = time.time()

        result = minimize(
            problem,
            optimizer,
            termination=('n_gen', generations),
            verbose=True,
            save_history=True
        )

        optimization_time = time.time() - start_time

        # Process results
        self.pareto_front = result.F
        pareto_solutions = result.X

        # Convert best solutions back to schedules
        best_solutions = self._extract_best_solutions(pareto_solutions, problem)

        # Calculate additional metrics
        metrics = self._calculate_optimization_metrics(result)

        results = {
            'algorithm': algorithm,
            'optimization_time': optimization_time,
            'pareto_front': self.pareto_front,
            'best_solutions': best_solutions,
            'metrics': metrics,
            'convergence_history': [gen.opt.get("F") for gen in result.history] if hasattr(result, 'history') else []
        }

        self.optimization_history.append(results)

        logger.info(f"Optimization completed in {optimization_time:.2f}s")
        logger.info(f"Found {len(self.pareto_front)} Pareto-optimal solutions")

        return results

    def _simplified_optimization(self) -> Dict[str, Any]:
        """Simplified optimization when Pymoo is not available"""
        logger.info("Running simplified multi-objective optimization...")

        # Generate multiple candidate schedules
        candidates = []

        # Strategy 1: Minimize makespan
        schedule1 = self._optimize_for_makespan()
        candidates.append(('makespan', schedule1))

        # Strategy 2: Minimize energy
        schedule2 = self._optimize_for_energy()
        candidates.append(('energy', schedule2))

        # Strategy 3: Minimize memory
        schedule3 = self._optimize_for_memory()
        candidates.append(('memory', schedule3))

        # Strategy 4: Balanced approach
        schedule4 = self._optimize_balanced()
        candidates.append(('balanced', schedule4))

        # Evaluate all candidates
        best_solutions = []
        pareto_front = []

        for strategy, schedule in candidates:
            frequencies = ['nominal'] * len(schedule)  # Default frequencies

            # Create problem instance for evaluation
            problem = NPUSchedulingProblem(self.nodes, self.edges, self.power_profile)
            objectives = problem._evaluate_objectives(schedule, frequencies)

            solution = {
                'strategy': strategy,
                'schedule': schedule,
                'frequencies': frequencies,
                'objectives': objectives
            }

            best_solutions.append(solution)
            pareto_front.append([
                objectives.makespan,
                objectives.energy,
                objectives.memory_usage,
                objectives.thermal_impact,
                objectives.carbon_footprint
            ])

        return {
            'algorithm': 'simplified',
            'optimization_time': 0.1,
            'pareto_front': np.array(pareto_front),
            'best_solutions': best_solutions,
            'metrics': {'hypervolume': 0.0, 'spread': 0.0},
            'convergence_history': []
        }

    def _optimize_for_makespan(self) -> List[int]:
        """Optimize schedule for minimum makespan"""
        # Simple critical path scheduling
        return self._critical_path_schedule()

    def _optimize_for_energy(self) -> List[int]:
        """Optimize schedule for minimum energy"""
        # Prioritize low-power operations and memory efficiency
        return self._energy_aware_schedule()

    def _optimize_for_memory(self) -> List[int]:
        """Optimize schedule for minimum memory usage"""
        # Prioritize FREE operations and minimize peak memory
        return self._memory_aware_schedule()

    def _optimize_balanced(self) -> List[int]:
        """Optimize schedule with balanced objectives"""
        # Weighted combination of objectives
        return self._hybrid_schedule()

    def _critical_path_schedule(self) -> List[int]:
        """Critical path scheduling for makespan optimization"""
        # Calculate critical path lengths
        critical_lengths = {}

        def calculate_critical_length(node_id):
            if node_id in critical_lengths:
                return critical_lengths[node_id]

            node = self.nodes[node_id]
            successors = node.get('successors', [])

            if not successors:
                critical_lengths[node_id] = node.get('cycles', 0)
            else:
                max_successor_length = max(calculate_critical_length(succ) for succ in successors)
                critical_lengths[node_id] = node.get('cycles', 0) + max_successor_length

            return critical_lengths[node_id]

        # Calculate for all nodes
        for node_id in self.nodes:
            calculate_critical_length(node_id)

        # Sort by critical path length (descending)
        sorted_nodes = sorted(self.nodes.keys(),
                            key=lambda x: critical_lengths[x],
                            reverse=True)

        return self._topological_sort_with_priority(sorted_nodes)

    def _energy_aware_schedule(self) -> List[int]:
        """Energy-aware scheduling"""
        def energy_priority(node_id):
            node = self.nodes[node_id]

            # Prioritize FREE operations (negative energy impact)
            if node['op'] == 'FREE':
                return -1000

            # Penalize high-energy operations
            energy_cost = node.get('cycles', 0) * 0.1 + node.get('size', 0) * 0.001

            return energy_cost

        sorted_nodes = sorted(self.nodes.keys(), key=energy_priority)
        return self._topological_sort_with_priority(sorted_nodes)

    def _memory_aware_schedule(self) -> List[int]:
        """Memory-aware scheduling"""
        def memory_priority(node_id):
            node = self.nodes[node_id]

            # Highest priority for FREE operations
            if node['op'] == 'FREE':
                return -node.get('size', 0)

            # Lower priority for large ALLOC operations
            if node['op'] == 'ALLOC':
                return node.get('size', 0)

            # Medium priority for compute operations
            return 0

        sorted_nodes = sorted(self.nodes.keys(), key=memory_priority)
        return self._topological_sort_with_priority(sorted_nodes)

    def _hybrid_schedule(self) -> List[int]:
        """Hybrid scheduling with balanced priorities"""
        def hybrid_priority(node_id):
            node = self.nodes[node_id]

            # Multi-objective priority
            time_weight = 0.4
            energy_weight = 0.3
            memory_weight = 0.3

            time_cost = node.get('cycles', 0)
            energy_cost = node.get('cycles', 0) * 0.1 + node.get('size', 0) * 0.001
            memory_cost = node.get('size', 0) if node['op'] == 'ALLOC' else -node.get('size', 0)

            # FREE operations get bonus
            if node['op'] == 'FREE':
                memory_cost = -memory_cost * 2

            total_cost = (time_weight * time_cost +
                         energy_weight * energy_cost +
                         memory_weight * memory_cost)

            return total_cost

        sorted_nodes = sorted(self.nodes.keys(), key=hybrid_priority)
        return self._topological_sort_with_priority(sorted_nodes)

    def _topological_sort_with_priority(self, priority_order: List[int]) -> List[int]:
        """Topological sort respecting priority order"""
        # Build dependency map
        dependencies = defaultdict(set)
        for start, end in self.edges:
            dependencies[end].add(start)

        # Priority map
        priority_map = {node_id: i for i, node_id in enumerate(priority_order)}

        schedule = []
        scheduled = set()

        while len(schedule) < len(self.nodes):
            # Find ready nodes (dependencies satisfied)
            ready = []
            for node_id in self.nodes:
                if (node_id not in scheduled and
                    dependencies[node_id].issubset(scheduled)):
                    ready.append(node_id)

            if not ready:
                # Should not happen with valid DAG
                break

            # Sort by priority
            ready.sort(key=lambda x: priority_map.get(x, len(priority_order)))

            # Schedule highest priority ready node
            next_node = ready[0]
            schedule.append(next_node)
            scheduled.add(next_node)

        return schedule

    def _extract_best_solutions(self, pareto_solutions: np.ndarray,
                               problem: NPUSchedulingProblem) -> List[Dict]:
        """Extract best solutions from Pareto front"""
        best_solutions = []
        n_tasks = len(self.nodes)

        # Select representative solutions from Pareto front
        indices = self._select_representative_solutions(self.pareto_front)

        for idx in indices:
            solution_vars = pareto_solutions[idx]

            # Decode solution
            schedule = problem._decode_schedule(solution_vars[:n_tasks])
            frequencies = problem._decode_frequencies(solution_vars[n_tasks:])
            objectives = problem._evaluate_objectives(schedule, frequencies)

            solution = {
                'index': idx,
                'schedule': schedule,
                'frequencies': frequencies,
                'objectives': objectives,
                'pareto_rank': 0  # All solutions in final front have rank 0
            }

            best_solutions.append(solution)

        return best_solutions

    def _select_representative_solutions(self, pareto_front: np.ndarray,
                                       max_solutions: int = 5) -> List[int]:
        """Select representative solutions from Pareto front"""
        if len(pareto_front) <= max_solutions:
            return list(range(len(pareto_front)))

        # Select extreme points for each objective
        selected_indices = set()

        for obj_idx in range(pareto_front.shape[1]):
            # Best solution for this objective
            best_idx = np.argmin(pareto_front[:, obj_idx])
            selected_indices.add(best_idx)

        # Add knee points (solutions with good trade-offs)
        if len(selected_indices) < max_solutions:
            # Calculate distance from ideal point
            ideal_point = np.min(pareto_front, axis=0)
            distances = np.linalg.norm(pareto_front - ideal_point, axis=1)

            # Select solutions with smallest distances
            remaining_slots = max_solutions - len(selected_indices)
            distance_indices = np.argsort(distances)

            for idx in distance_indices:
                if len(selected_indices) >= max_solutions:
                    break
                selected_indices.add(idx)

        return list(selected_indices)

    def _calculate_optimization_metrics(self, result) -> Dict[str, float]:
        """Calculate optimization quality metrics"""
        if not hasattr(result, 'F') or result.F is None:
            return {'hypervolume': 0.0, 'spread': 0.0}

        pareto_front = result.F

        # Hypervolume (simplified calculation)
        # Reference point: worst values for each objective
        ref_point = np.max(pareto_front, axis=0) * 1.1

        # Simplified hypervolume calculation
        hypervolume = self._calculate_hypervolume(pareto_front, ref_point)

        # Spread metric
        spread = self._calculate_spread(pareto_front)

        return {
            'hypervolume': hypervolume,
            'spread': spread,
            'n_solutions': len(pareto_front)
        }

    def _calculate_hypervolume(self, front: np.ndarray, ref_point: np.ndarray) -> float:
        """Calculate hypervolume (simplified)"""
        if len(front) == 0:
            return 0.0

        # Very simplified hypervolume calculation
        # In practice, would use proper hypervolume algorithms
        volumes = []
        for point in front:
            volume = np.prod(np.maximum(0, ref_point - point))
            volumes.append(volume)

        return np.sum(volumes)

    def _calculate_spread(self, front: np.ndarray) -> float:
        """Calculate spread metric"""
        if len(front) <= 1:
            return 0.0

        # Calculate distances between consecutive points
        distances = []
        for i in range(len(front) - 1):
            dist = np.linalg.norm(front[i] - front[i + 1])
            distances.append(dist)

        if not distances:
            return 0.0

        # Spread is standard deviation of distances
        return np.std(distances)

    def get_recommended_solution(self, preferences: Dict[str, float] = None) -> Dict[str, Any]:
        """Get recommended solution based on user preferences"""
        if not self.optimization_history:
            raise ValueError("No optimization results available")

        latest_results = self.optimization_history[-1]
        best_solutions = latest_results['best_solutions']

        if not best_solutions:
            raise ValueError("No solutions found")

        # Default preferences (equal weights)
        if preferences is None:
            preferences = {
                'makespan': 0.3,
                'energy': 0.25,
                'memory': 0.2,
                'thermal': 0.15,
                'carbon': 0.1
            }

        # Calculate weighted scores for each solution
        best_solution = None
        best_score = float('inf')

        for solution in best_solutions:
            objectives = solution['objectives']

            # Normalize objectives (simple min-max normalization)
            score = (preferences.get('makespan', 0) * objectives.makespan +
                    preferences.get('energy', 0) * objectives.energy +
                    preferences.get('memory', 0) * objectives.memory_usage +
                    preferences.get('thermal', 0) * objectives.thermal_impact +
                    preferences.get('carbon', 0) * objectives.carbon_footprint)

            if score < best_score:
                best_score = score
                best_solution = solution

        return best_solution

    def apply_carbon_aware_scheduling(self, schedule: List[int],
                                    estimated_duration: float) -> Dict[str, Any]:
        """Apply carbon-aware scheduling optimization"""
        # Get optimal scheduling window
        optimal_start, optimal_end = self.carbon_scheduler.get_optimal_scheduling_window(
            estimated_duration / 3600.0  # Convert to hours
        )

        current_time = time.time()
        delay_seconds = max(0, optimal_start - current_time)

        # Calculate carbon savings
        current_intensity = self.carbon_scheduler.get_carbon_intensity()

        # Estimate energy consumption
        total_energy = sum(self.nodes[nid].get('cycles', 0) * 0.1 for nid in schedule)

        current_carbon = self.carbon_scheduler.calculate_carbon_footprint(total_energy)

        # Simulate optimal window carbon footprint
        optimal_intensity = self.carbon_scheduler._predict_carbon_intensity(
            int(delay_seconds / 3600)
        )
        optimal_carbon = (optimal_intensity / current_intensity) * current_carbon

        carbon_savings = current_carbon - optimal_carbon

        return {
            'optimal_start_time': optimal_start,
            'optimal_end_time': optimal_end,
            'delay_seconds': delay_seconds,
            'current_carbon_intensity': current_intensity,
            'optimal_carbon_intensity': optimal_intensity,
            'carbon_savings_gco2': carbon_savings,
            'carbon_reduction_percent': (carbon_savings / current_carbon) * 100 if current_carbon > 0 else 0
        }
