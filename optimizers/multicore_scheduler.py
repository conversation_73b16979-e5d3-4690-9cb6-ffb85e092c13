"""
Multi-core Distribution for NPU Scheduling
Implements METIS graph partitioning, NUMA-aware allocation, and distributed coordination
Uses Ray and MPI4py for distributed execution
"""

import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Optional, Any, Set
from collections import defaultdict, deque
from dataclasses import dataclass
import threading
import time
import logging
import os
import psutil

# Optional imports with fallbacks
try:
    import metis
    METIS_AVAILABLE = True
except (ImportError, RuntimeError) as e:
    METIS_AVAILABLE = False
    logging.warning(f"METIS not available: {e}, using fallback partitioning")

try:
    import ray
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False
    logging.warning("Ray not available, using local execution")

# MPI will be imported only when needed to avoid initialization issues
MPI_AVAILABLE = False
try:
    import mpi4py
    MPI_AVAILABLE = True
except ImportError:
    logging.warning("MPI4py not available, using local execution")

logger = logging.getLogger(__name__)

@dataclass
class CoreAssignment:
    """Core assignment information"""
    core_id: int
    numa_node: int
    tasks: List[int]
    estimated_load: float
    communication_cost: float

@dataclass
class NUMATopology:
    """NUMA topology information"""
    numa_nodes: Dict[int, List[int]]  # numa_node -> list of cpu cores
    memory_nodes: Dict[int, int]      # numa_node -> memory size (MB)
    distances: Dict[Tuple[int, int], float]  # (numa1, numa2) -> distance


class NUMAManager:
    """
    NUMA-aware resource management
    Handles CPU topology and memory locality optimization
    """
    
    def __init__(self):
        self.topology = self._detect_numa_topology()
        self.cpu_affinity_map = {}
        self.memory_policy = "first_touch"  # first_touch, interleaved, bind
    
    def _detect_numa_topology(self) -> NUMATopology:
        """Detect NUMA topology of the system"""
        numa_nodes = defaultdict(list)
        memory_nodes = {}
        distances = {}
        
        try:
            # Try to read NUMA information from /sys
            numa_path = "/sys/devices/system/node"
            if os.path.exists(numa_path):
                for node_dir in os.listdir(numa_path):
                    if node_dir.startswith("node"):
                        node_id = int(node_dir[4:])
                        
                        # Get CPU list for this NUMA node
                        cpulist_path = os.path.join(numa_path, node_dir, "cpulist")
                        if os.path.exists(cpulist_path):
                            with open(cpulist_path, 'r') as f:
                                cpulist = f.read().strip()
                                cpus = self._parse_cpulist(cpulist)
                                numa_nodes[node_id] = cpus
                        
                        # Get memory info
                        meminfo_path = os.path.join(numa_path, node_dir, "meminfo")
                        if os.path.exists(meminfo_path):
                            with open(meminfo_path, 'r') as f:
                                for line in f:
                                    if "MemTotal:" in line:
                                        mem_kb = int(line.split()[3])
                                        memory_nodes[node_id] = mem_kb // 1024  # Convert to MB
                                        break
            
            # Fallback: use psutil
            if not numa_nodes:
                cpu_count = psutil.cpu_count()
                numa_nodes[0] = list(range(cpu_count))
                memory_nodes[0] = psutil.virtual_memory().total // (1024 * 1024)
            
            # Calculate distances (simplified)
            for node1 in numa_nodes:
                for node2 in numa_nodes:
                    if node1 == node2:
                        distances[(node1, node2)] = 1.0
                    else:
                        distances[(node1, node2)] = 2.0  # Simplified inter-node distance
        
        except Exception as e:
            logger.warning(f"Failed to detect NUMA topology: {e}")
            # Fallback to single NUMA node
            cpu_count = psutil.cpu_count()
            numa_nodes[0] = list(range(cpu_count))
            memory_nodes[0] = psutil.virtual_memory().total // (1024 * 1024)
            distances[(0, 0)] = 1.0
        
        return NUMATopology(
            numa_nodes=dict(numa_nodes),
            memory_nodes=memory_nodes,
            distances=distances
        )
    
    def _parse_cpulist(self, cpulist: str) -> List[int]:
        """Parse CPU list string (e.g., '0-3,8-11')"""
        cpus = []
        for part in cpulist.split(','):
            if '-' in part:
                start, end = map(int, part.split('-'))
                cpus.extend(range(start, end + 1))
            else:
                cpus.append(int(part))
        return cpus
    
    def get_numa_node_for_data(self, data_location: str) -> int:
        """Get optimal NUMA node for data location"""
        # Simplified: hash data location to NUMA node
        numa_nodes = list(self.topology.numa_nodes.keys())
        return numa_nodes[hash(data_location) % len(numa_nodes)]
    
    def assign_cpu_affinity(self, task_id: int, numa_node: int) -> Optional[int]:
        """Assign CPU affinity for task"""
        if numa_node not in self.topology.numa_nodes:
            numa_node = 0  # Fallback to first NUMA node
        
        available_cpus = self.topology.numa_nodes[numa_node]
        if not available_cpus:
            return None
        
        # Simple round-robin assignment
        cpu_id = available_cpus[task_id % len(available_cpus)]
        self.cpu_affinity_map[task_id] = cpu_id
        
        return cpu_id
    
    def get_memory_policy(self, numa_node: int) -> Dict[str, Any]:
        """Get memory policy for NUMA node"""
        return {
            'policy': self.memory_policy,
            'numa_node': numa_node,
            'preferred_nodes': [numa_node]
        }


class GraphPartitioner:
    """
    Graph partitioning for multi-core distribution
    Uses METIS for optimal partitioning with communication minimization
    """
    
    def __init__(self, use_metis: bool = True):
        self.use_metis = use_metis and METIS_AVAILABLE
    
    def partition_graph(self, nodes: Dict, edges: List[Tuple], 
                       num_partitions: int) -> Dict[int, List[int]]:
        """Partition graph into balanced partitions"""
        if self.use_metis:
            return self._metis_partition(nodes, edges, num_partitions)
        else:
            return self._fallback_partition(nodes, edges, num_partitions)
    
    def _metis_partition(self, nodes: Dict, edges: List[Tuple], 
                        num_partitions: int) -> Dict[int, List[int]]:
        """Use METIS for graph partitioning"""
        try:
            # Build adjacency list for METIS
            node_list = list(nodes.keys())
            node_to_idx = {node_id: idx for idx, node_id in enumerate(node_list)}
            
            # Create adjacency list
            adjacency = [[] for _ in range(len(node_list))]
            for start, end in edges:
                if start in node_to_idx and end in node_to_idx:
                    start_idx = node_to_idx[start]
                    end_idx = node_to_idx[end]
                    adjacency[start_idx].append(end_idx)
                    adjacency[end_idx].append(start_idx)  # Undirected for partitioning
            
            # Node weights (based on computation cost)
            node_weights = []
            for node_id in node_list:
                node = nodes[node_id]
                weight = node.get('cycles', 1) + node.get('size', 0) // 100
                node_weights.append(max(1, weight))
            
            # Partition using METIS
            (edgecuts, parts) = metis.part_graph(
                adjacency,
                nparts=num_partitions,
                vwgt=node_weights,
                recursive=True
            )
            
            # Convert back to node IDs
            partitions = defaultdict(list)
            for idx, part_id in enumerate(parts):
                node_id = node_list[idx]
                partitions[part_id].append(node_id)
            
            logger.info(f"METIS partitioning: {edgecuts} edge cuts")
            return dict(partitions)
            
        except Exception as e:
            logger.warning(f"METIS partitioning failed: {e}, using fallback")
            return self._fallback_partition(nodes, edges, num_partitions)
    
    def _fallback_partition(self, nodes: Dict, edges: List[Tuple], 
                           num_partitions: int) -> Dict[int, List[int]]:
        """Fallback partitioning using simple heuristics"""
        # Build graph
        G = nx.DiGraph()
        for node_id, node_data in nodes.items():
            weight = node_data.get('cycles', 1) + node_data.get('size', 0) // 100
            G.add_node(node_id, weight=weight)
        
        for start, end in edges:
            G.add_edge(start, end)
        
        # Use NetworkX community detection as fallback
        try:
            # Convert to undirected for community detection
            UG = G.to_undirected()
            
            # Simple partitioning: BFS-based
            partitions = defaultdict(list)
            visited = set()
            partition_id = 0
            
            for node_id in nodes.keys():
                if node_id not in visited:
                    # BFS to create partition
                    partition = []
                    queue = deque([node_id])
                    
                    while queue and len(partition) < len(nodes) // num_partitions:
                        current = queue.popleft()
                        if current not in visited:
                            visited.add(current)
                            partition.append(current)
                            
                            # Add neighbors
                            for neighbor in UG.neighbors(current):
                                if neighbor not in visited:
                                    queue.append(neighbor)
                    
                    partitions[partition_id % num_partitions].extend(partition)
                    partition_id += 1
            
            # Balance partitions
            self._balance_partitions(partitions, nodes, num_partitions)
            
            return dict(partitions)
            
        except Exception as e:
            logger.warning(f"Fallback partitioning failed: {e}, using round-robin")
            return self._round_robin_partition(nodes, num_partitions)
    
    def _balance_partitions(self, partitions: Dict[int, List[int]], 
                           nodes: Dict, num_partitions: int):
        """Balance partition loads"""
        # Calculate partition weights
        partition_weights = {}
        for part_id, node_list in partitions.items():
            weight = sum(nodes[nid].get('cycles', 1) + nodes[nid].get('size', 0) // 100 
                        for nid in node_list)
            partition_weights[part_id] = weight
        
        # Move nodes from heavy to light partitions
        avg_weight = sum(partition_weights.values()) / num_partitions
        
        for _ in range(10):  # Max 10 balancing iterations
            heavy_parts = [(pid, w) for pid, w in partition_weights.items() 
                          if w > avg_weight * 1.2]
            light_parts = [(pid, w) for pid, w in partition_weights.items() 
                          if w < avg_weight * 0.8]
            
            if not heavy_parts or not light_parts:
                break
            
            # Move node from heaviest to lightest
            heavy_id, _ = max(heavy_parts, key=lambda x: x[1])
            light_id, _ = min(light_parts, key=lambda x: x[1])
            
            if partitions[heavy_id]:
                node_to_move = partitions[heavy_id].pop()
                partitions[light_id].append(node_to_move)
                
                # Update weights
                node_weight = (nodes[node_to_move].get('cycles', 1) + 
                             nodes[node_to_move].get('size', 0) // 100)
                partition_weights[heavy_id] -= node_weight
                partition_weights[light_id] += node_weight
    
    def _round_robin_partition(self, nodes: Dict, num_partitions: int) -> Dict[int, List[int]]:
        """Simple round-robin partitioning"""
        partitions = defaultdict(list)
        for idx, node_id in enumerate(nodes.keys()):
            partition_id = idx % num_partitions
            partitions[partition_id].append(node_id)
        
        return dict(partitions)


class CommunicationOptimizer:
    """
    Communication-aware optimization for distributed scheduling
    Minimizes inter-core data transfers and optimizes message passing
    """
    
    def __init__(self, numa_manager: NUMAManager):
        self.numa_manager = numa_manager
        self.communication_costs = {}
        self.message_buffers = defaultdict(deque)
        self.bandwidth_limits = {}
    
    def calculate_communication_cost(self, source_core: int, dest_core: int, 
                                   data_size: int) -> float:
        """Calculate communication cost between cores"""
        # Get NUMA nodes for cores
        source_numa = self._get_numa_for_core(source_core)
        dest_numa = self._get_numa_for_core(dest_core)
        
        # Base cost
        if source_core == dest_core:
            return 0.0
        elif source_numa == dest_numa:
            # Intra-NUMA communication
            base_cost = 1.0
        else:
            # Inter-NUMA communication
            distance = self.numa_manager.topology.distances.get(
                (source_numa, dest_numa), 2.0
            )
            base_cost = distance * 2.0
        
        # Scale by data size
        cost = base_cost * (data_size / 1024.0)  # Cost per KB
        
        return cost
    
    def _get_numa_for_core(self, core_id: int) -> int:
        """Get NUMA node for core"""
        for numa_id, cores in self.numa_manager.topology.numa_nodes.items():
            if core_id in cores:
                return numa_id
        return 0  # Fallback
    
    def optimize_data_placement(self, partitions: Dict[int, List[int]], 
                               nodes: Dict, edges: List[Tuple]) -> Dict[int, Dict[str, Any]]:
        """Optimize data placement for partitions"""
        placement_info = {}
        
        for partition_id, node_list in partitions.items():
            # Analyze data requirements
            data_requirements = self._analyze_data_requirements(node_list, nodes, edges)
            
            # Find optimal NUMA node
            optimal_numa = self._find_optimal_numa_node(data_requirements)
            
            placement_info[partition_id] = {
                'numa_node': optimal_numa,
                'data_requirements': data_requirements,
                'estimated_memory': sum(nodes[nid].get('size', 0) for nid in node_list),
                'communication_cost': self._estimate_communication_cost(
                    partition_id, partitions, nodes, edges
                )
            }
        
        return placement_info
    
    def _analyze_data_requirements(self, node_list: List[int], nodes: Dict, 
                                  edges: List[Tuple]) -> Dict[str, Any]:
        """Analyze data requirements for a partition"""
        total_memory = sum(nodes[nid].get('size', 0) for nid in node_list)
        
        # Count buffer operations
        alloc_count = sum(1 for nid in node_list if nodes[nid]['op'] == 'ALLOC')
        free_count = sum(1 for nid in node_list if nodes[nid]['op'] == 'FREE')
        
        # Analyze data dependencies
        external_deps = 0
        for start, end in edges:
            if start in node_list and end not in node_list:
                external_deps += 1
            elif start not in node_list and end in node_list:
                external_deps += 1
        
        return {
            'total_memory': total_memory,
            'alloc_count': alloc_count,
            'free_count': free_count,
            'external_dependencies': external_deps
        }
    
    def _find_optimal_numa_node(self, data_requirements: Dict[str, Any]) -> int:
        """Find optimal NUMA node for data requirements"""
        # Simple heuristic: choose NUMA node with most available memory
        best_numa = 0
        best_score = 0
        
        for numa_id, memory_mb in self.numa_manager.topology.memory_nodes.items():
            # Score based on available memory and CPU count
            cpu_count = len(self.numa_manager.topology.numa_nodes.get(numa_id, []))
            score = memory_mb * cpu_count
            
            if score > best_score:
                best_score = score
                best_numa = numa_id
        
        return best_numa
    
    def _estimate_communication_cost(self, partition_id: int, partitions: Dict[int, List[int]], 
                                   nodes: Dict, edges: List[Tuple]) -> float:
        """Estimate communication cost for partition"""
        partition_nodes = set(partitions[partition_id])
        total_cost = 0.0
        
        for start, end in edges:
            if start in partition_nodes and end not in partition_nodes:
                # Outgoing communication
                data_size = nodes[start].get('size', 0)
                total_cost += data_size * 0.1  # Simplified cost model
            elif start not in partition_nodes and end in partition_nodes:
                # Incoming communication
                data_size = nodes[end].get('size', 0)
                total_cost += data_size * 0.1
        
        return total_cost


class DistributedScheduler:
    """
    Distributed NPU scheduler using Ray or MPI
    Coordinates execution across multiple cores with load balancing
    """

    def __init__(self, nodes: Dict, edges: List[Tuple], num_cores: int = 4,
                 backend: str = "ray"):  # "ray", "mpi", or "local"
        self.nodes = nodes
        self.edges = edges
        self.num_cores = num_cores
        self.backend = backend

        # Initialize components
        self.numa_manager = NUMAManager()
        self.partitioner = GraphPartitioner()
        self.comm_optimizer = CommunicationOptimizer(self.numa_manager)

        # Distributed state
        self.partitions = {}
        self.core_assignments = {}
        self.execution_results = {}

        # Initialize backend
        self._initialize_backend()

    def _initialize_backend(self):
        """Initialize distributed backend"""
        if self.backend == "ray" and RAY_AVAILABLE:
            try:
                if not ray.is_initialized():
                    ray.init(num_cpus=self.num_cores, ignore_reinit_error=True)
                logger.info("Ray backend initialized")
            except Exception as e:
                logger.warning(f"Ray initialization failed: {e}, falling back to local")
                self.backend = "local"

        elif self.backend == "mpi" and MPI_AVAILABLE:
            try:
                from mpi4py import MPI
                self.mpi_comm = MPI.COMM_WORLD
                self.mpi_rank = self.mpi_comm.Get_rank()
                self.mpi_size = self.mpi_comm.Get_size()
                logger.info(f"MPI backend initialized (rank {self.mpi_rank}/{self.mpi_size})")
            except Exception as e:
                logger.warning(f"MPI initialization failed: {e}, falling back to local")
                self.backend = "local"

        else:
            self.backend = "local"
            logger.info("Using local execution backend")

    def distribute_and_schedule(self) -> List[int]:
        """Distribute graph and execute scheduling"""
        # Step 1: Partition graph
        logger.info("Partitioning graph...")
        self.partitions = self.partitioner.partition_graph(
            self.nodes, self.edges, self.num_cores
        )

        # Step 2: Optimize data placement
        logger.info("Optimizing data placement...")
        placement_info = self.comm_optimizer.optimize_data_placement(
            self.partitions, self.nodes, self.edges
        )

        # Step 3: Create core assignments
        logger.info("Creating core assignments...")
        self.core_assignments = self._create_core_assignments(placement_info)

        # Step 4: Execute distributed scheduling
        logger.info("Executing distributed scheduling...")
        if self.backend == "ray":
            return self._execute_with_ray()
        elif self.backend == "mpi":
            return self._execute_with_mpi()
        else:
            return self._execute_local()

    def _create_core_assignments(self, placement_info: Dict) -> Dict[int, CoreAssignment]:
        """Create core assignments with NUMA awareness"""
        assignments = {}

        for partition_id, node_list in self.partitions.items():
            placement = placement_info[partition_id]
            numa_node = placement['numa_node']

            # Assign CPU core from NUMA node
            cpu_core = self.numa_manager.assign_cpu_affinity(partition_id, numa_node)
            if cpu_core is None:
                cpu_core = partition_id % self.num_cores  # Fallback

            assignments[partition_id] = CoreAssignment(
                core_id=cpu_core,
                numa_node=numa_node,
                tasks=node_list,
                estimated_load=placement['estimated_memory'],
                communication_cost=placement['communication_cost']
            )

        return assignments

    def _execute_with_ray(self) -> List[int]:
        """Execute scheduling using Ray"""
        if not RAY_AVAILABLE:
            return self._execute_local()

        try:
            # Create remote workers
            @ray.remote
            class SchedulingWorker:
                def __init__(self, worker_id: int, numa_node: int):
                    self.worker_id = worker_id
                    self.numa_node = numa_node
                    self.local_schedule = []

                def execute_partition(self, nodes: Dict, edges: List[Tuple],
                                    task_list: List[int]) -> Dict[str, Any]:
                    # Set CPU affinity if possible
                    try:
                        import os
                        os.sched_setaffinity(0, {self.numa_node})
                    except:
                        pass

                    # Execute local scheduling
                    local_schedule = self._schedule_partition(nodes, edges, task_list)

                    return {
                        'worker_id': self.worker_id,
                        'schedule': local_schedule,
                        'execution_time': time.time(),
                        'numa_node': self.numa_node
                    }

                def _schedule_partition(self, nodes: Dict, edges: List[Tuple],
                                      task_list: List[int]) -> List[int]:
                    # Simple topological sort for partition
                    in_degree = defaultdict(int)
                    for start, end in edges:
                        if start in task_list and end in task_list:
                            in_degree[end] += 1

                    available = [nid for nid in task_list if in_degree[nid] == 0]
                    schedule = []

                    while available:
                        # Priority: FREE > small ALLOC > others
                        available.sort(key=lambda x: (
                            0 if nodes[x]['op'] == 'FREE' else 1,
                            nodes[x].get('size', 0) if nodes[x]['op'] == 'ALLOC' else 0
                        ))

                        node_id = available.pop(0)
                        schedule.append(node_id)

                        # Update available nodes
                        for start, end in edges:
                            if start == node_id and end in task_list:
                                in_degree[end] -= 1
                                if in_degree[end] == 0:
                                    available.append(end)

                    return schedule

            # Create workers
            workers = []
            for partition_id, assignment in self.core_assignments.items():
                worker = SchedulingWorker.remote(partition_id, assignment.numa_node)
                workers.append((worker, assignment))

            # Execute partitions in parallel
            futures = []
            for worker, assignment in workers:
                future = worker.execute_partition.remote(
                    self.nodes, self.edges, assignment.tasks
                )
                futures.append(future)

            # Collect results
            results = ray.get(futures)

            # Merge schedules
            merged_schedule = self._merge_partition_schedules(results)

            return merged_schedule

        except Exception as e:
            logger.error(f"Ray execution failed: {e}")
            return self._execute_local()

    def _execute_with_mpi(self) -> List[int]:
        """Execute scheduling using MPI"""
        if not MPI_AVAILABLE:
            return self._execute_local()

        try:
            # Distribute partitions across MPI ranks
            if self.mpi_rank == 0:
                # Master rank: distribute work
                for rank in range(1, min(self.mpi_size, len(self.partitions))):
                    if rank - 1 < len(self.core_assignments):
                        assignment = list(self.core_assignments.values())[rank - 1]
                        work_data = {
                            'nodes': self.nodes,
                            'edges': self.edges,
                            'tasks': assignment.tasks,
                            'numa_node': assignment.numa_node
                        }
                        self.mpi_comm.send(work_data, dest=rank, tag=0)

                # Execute local partition
                if 0 < len(self.core_assignments):
                    local_assignment = list(self.core_assignments.values())[0]
                    local_result = self._execute_partition_local(local_assignment)
                else:
                    local_result = {'schedule': [], 'worker_id': 0}

                # Collect results from workers
                results = [local_result]
                for rank in range(1, min(self.mpi_size, len(self.partitions))):
                    result = self.mpi_comm.recv(source=rank, tag=1)
                    results.append(result)

                # Merge and return
                merged_schedule = self._merge_partition_schedules(results)
                return merged_schedule

            else:
                # Worker rank: execute assigned partition
                if self.mpi_rank <= len(self.partitions):
                    work_data = self.mpi_comm.recv(source=0, tag=0)

                    # Create temporary assignment
                    assignment = CoreAssignment(
                        core_id=self.mpi_rank,
                        numa_node=work_data['numa_node'],
                        tasks=work_data['tasks'],
                        estimated_load=0,
                        communication_cost=0
                    )

                    result = self._execute_partition_local(assignment)
                    self.mpi_comm.send(result, dest=0, tag=1)

                return []  # Workers return empty, only master returns final result

        except Exception as e:
            logger.error(f"MPI execution failed: {e}")
            return self._execute_local()

    def _execute_local(self) -> List[int]:
        """Execute scheduling locally with threading"""
        results = []

        if len(self.core_assignments) <= 1:
            # Single-threaded execution
            for assignment in self.core_assignments.values():
                result = self._execute_partition_local(assignment)
                results.append(result)
        else:
            # Multi-threaded execution
            threads = []
            thread_results = {}

            def worker_thread(assignment: CoreAssignment):
                result = self._execute_partition_local(assignment)
                thread_results[assignment.core_id] = result

            # Start threads
            for assignment in self.core_assignments.values():
                thread = threading.Thread(target=worker_thread, args=(assignment,))
                thread.start()
                threads.append(thread)

            # Wait for completion
            for thread in threads:
                thread.join()

            # Collect results
            results = list(thread_results.values())

        # Merge schedules
        merged_schedule = self._merge_partition_schedules(results)
        return merged_schedule

    def _execute_partition_local(self, assignment: CoreAssignment) -> Dict[str, Any]:
        """Execute a single partition locally"""
        start_time = time.time()

        # Set CPU affinity if possible
        try:
            import os
            if hasattr(os, 'sched_setaffinity'):
                os.sched_setaffinity(0, {assignment.core_id})
        except:
            pass

        # Execute local scheduling (simplified topological sort)
        task_list = assignment.tasks
        in_degree = defaultdict(int)

        for start, end in self.edges:
            if start in task_list and end in task_list:
                in_degree[end] += 1

        available = [nid for nid in task_list if in_degree[nid] == 0]
        schedule = []

        while available:
            # Priority scheduling
            available.sort(key=lambda x: (
                0 if self.nodes[x]['op'] == 'FREE' else 1,
                self.nodes[x].get('size', 0) if self.nodes[x]['op'] == 'ALLOC' else 0
            ))

            node_id = available.pop(0)
            schedule.append(node_id)

            # Update available nodes
            for start, end in self.edges:
                if start == node_id and end in task_list:
                    in_degree[end] -= 1
                    if in_degree[end] == 0:
                        available.append(end)

        execution_time = time.time() - start_time

        return {
            'worker_id': assignment.core_id,
            'schedule': schedule,
            'execution_time': execution_time,
            'numa_node': assignment.numa_node,
            'task_count': len(schedule)
        }

    def _merge_partition_schedules(self, results: List[Dict]) -> List[int]:
        """Merge partition schedules into global schedule"""
        # Sort results by worker ID for consistency
        results.sort(key=lambda x: x['worker_id'])

        # Simple concatenation (could be improved with dependency analysis)
        merged_schedule = []
        for result in results:
            merged_schedule.extend(result['schedule'])

        # Validate and fix dependencies
        merged_schedule = self._fix_dependencies(merged_schedule)

        return merged_schedule

    def _fix_dependencies(self, schedule: List[int]) -> List[int]:
        """Fix dependency violations in merged schedule"""
        # Build dependency map
        dependencies = defaultdict(set)
        for start, end in self.edges:
            dependencies[end].add(start)

        # Topological sort to fix violations
        fixed_schedule = []
        remaining = set(schedule)
        scheduled = set()

        while remaining:
            # Find nodes with satisfied dependencies
            ready = []
            for node_id in remaining:
                if dependencies[node_id].issubset(scheduled):
                    ready.append(node_id)

            if not ready:
                # Circular dependency or missing nodes - add remaining arbitrarily
                ready = list(remaining)

            # Sort ready nodes by original order
            ready.sort(key=lambda x: schedule.index(x))

            # Add first ready node
            node_id = ready[0]
            fixed_schedule.append(node_id)
            scheduled.add(node_id)
            remaining.remove(node_id)

        return fixed_schedule

    def get_distribution_statistics(self) -> Dict[str, Any]:
        """Get distribution statistics"""
        if not self.core_assignments:
            return {}

        # Calculate load balance
        loads = [assignment.estimated_load for assignment in self.core_assignments.values()]
        load_balance = np.std(loads) / max(np.mean(loads), 1.0)

        # Calculate communication costs
        comm_costs = [assignment.communication_cost for assignment in self.core_assignments.values()]
        total_comm_cost = sum(comm_costs)

        return {
            'num_partitions': len(self.partitions),
            'num_cores': self.num_cores,
            'backend': self.backend,
            'load_balance': load_balance,
            'total_communication_cost': total_comm_cost,
            'partition_sizes': {pid: len(nodes) for pid, nodes in self.partitions.items()},
            'numa_distribution': {
                assignment.numa_node: assignment.core_id
                for assignment in self.core_assignments.values()
            }
        }
