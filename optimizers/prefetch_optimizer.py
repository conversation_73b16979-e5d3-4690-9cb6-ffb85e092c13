"""
Predictive Data Prefetching for NPU Scheduling
Implements NPU Vector Runahead (NVR) and Voyager-style hierarchical neural networks
Optimizes memory access patterns and reduces cache misses
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Set
from collections import defaultdict, deque
from dataclasses import dataclass
import threading
import time
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

@dataclass
class MemoryAccess:
    """Memory access pattern information"""
    address: int
    size: int
    access_type: str  # 'read', 'write', 'alloc', 'free'
    timestamp: float
    node_id: int
    buffer_id: Optional[int] = None
    
@dataclass
class PrefetchRequest:
    """Prefetch request information"""
    address: int
    size: int
    priority: float
    predicted_access_time: float
    confidence: float
    prefetch_type: str  # 'sequential', 'spatial', 'temporal'

class StrideDetector:
    """
    Stride pattern detector for sequential access prediction
    Part of NPU Vector Runahead (NVR) system
    """
    
    def __init__(self, history_size: int = 16):
        self.history_size = history_size
        self.access_history = defaultdict(deque)  # buffer_id -> access history
        self.stride_patterns = defaultdict(dict)  # buffer_id -> {stride: count}
        self.confidence_threshold = 0.7
    
    def record_access(self, access: MemoryAccess):
        """Record memory access for stride detection"""
        if access.buffer_id is None:
            return
        
        buffer_id = access.buffer_id
        history = self.access_history[buffer_id]
        
        # Add to history
        history.append((access.address, access.timestamp))
        
        # Maintain history size
        if len(history) > self.history_size:
            history.popleft()
        
        # Detect strides if we have enough history
        if len(history) >= 3:
            self._detect_strides(buffer_id)
    
    def _detect_strides(self, buffer_id: int):
        """Detect stride patterns in access history"""
        history = list(self.access_history[buffer_id])
        strides = self.stride_patterns[buffer_id]
        
        # Calculate strides between consecutive accesses
        for i in range(len(history) - 1):
            addr1, _ = history[i]
            addr2, _ = history[i + 1]
            stride = addr2 - addr1
            
            if stride != 0:  # Ignore zero strides
                strides[stride] = strides.get(stride, 0) + 1
    
    def predict_next_access(self, buffer_id: int) -> Optional[PrefetchRequest]:
        """Predict next access based on stride patterns"""
        if buffer_id not in self.access_history:
            return None
        
        history = self.access_history[buffer_id]
        if len(history) < 2:
            return None
        
        # Get most recent access
        last_addr, last_time = history[-1]
        
        # Find most confident stride
        strides = self.stride_patterns.get(buffer_id, {})
        if not strides:
            return None
        
        # Calculate confidence for each stride
        total_accesses = sum(strides.values())
        best_stride = None
        best_confidence = 0.0
        
        for stride, count in strides.items():
            confidence = count / total_accesses
            if confidence > best_confidence and confidence >= self.confidence_threshold:
                best_confidence = confidence
                best_stride = stride
        
        if best_stride is None:
            return None
        
        # Predict next address
        next_addr = last_addr + best_stride
        predicted_time = last_time + 0.001  # Assume 1ms ahead
        
        return PrefetchRequest(
            address=next_addr,
            size=64,  # Cache line size
            priority=best_confidence,
            predicted_access_time=predicted_time,
            confidence=best_confidence,
            prefetch_type='sequential'
        )


class SparseChainDetector:
    """
    Sparse chain detector for indirect memory access patterns
    Handles pointer-chasing and sparse data structures
    """
    
    def __init__(self, chain_length: int = 8):
        self.chain_length = chain_length
        self.indirect_chains = defaultdict(list)  # base_addr -> chain of addresses
        self.chain_patterns = defaultdict(dict)  # pattern_id -> access pattern
        self.pointer_map = {}  # address -> pointed_to_address
    
    def record_indirect_access(self, base_addr: int, target_addr: int, 
                              access: MemoryAccess):
        """Record indirect memory access"""
        # Build chain
        chain = self.indirect_chains[base_addr]
        chain.append((target_addr, access.timestamp))
        
        # Maintain chain length
        if len(chain) > self.chain_length:
            chain.pop(0)
        
        # Update pointer map
        self.pointer_map[base_addr] = target_addr
        
        # Detect chain patterns
        if len(chain) >= 3:
            self._detect_chain_patterns(base_addr)
    
    def _detect_chain_patterns(self, base_addr: int):
        """Detect patterns in indirect access chains"""
        chain = self.indirect_chains[base_addr]
        
        # Look for repeating patterns
        for pattern_len in range(2, min(len(chain) // 2 + 1, 4)):
            for start in range(len(chain) - 2 * pattern_len + 1):
                pattern1 = [addr for addr, _ in chain[start:start + pattern_len]]
                pattern2 = [addr for addr, _ in chain[start + pattern_len:start + 2 * pattern_len]]
                
                if pattern1 == pattern2:
                    # Found repeating pattern
                    pattern_id = hash(tuple(pattern1))
                    self.chain_patterns[pattern_id] = {
                        'pattern': pattern1,
                        'base_addr': base_addr,
                        'confidence': 0.8,  # High confidence for exact matches
                        'last_seen': time.time()
                    }
    
    def predict_chain_access(self, base_addr: int) -> List[PrefetchRequest]:
        """Predict next accesses in sparse chain"""
        if base_addr not in self.indirect_chains:
            return []
        
        chain = self.indirect_chains[base_addr]
        if len(chain) < 2:
            return []
        
        # Find matching patterns
        requests = []
        current_time = time.time()
        
        for pattern_id, pattern_info in self.chain_patterns.items():
            if pattern_info['base_addr'] != base_addr:
                continue
            
            pattern = pattern_info['pattern']
            confidence = pattern_info['confidence']
            
            # Check if current chain matches pattern prefix
            recent_addrs = [addr for addr, _ in chain[-len(pattern):]]
            
            if len(recent_addrs) >= len(pattern) and recent_addrs[-len(pattern):] == pattern:
                # Pattern matches, predict next in sequence
                if len(pattern) > 0:
                    next_addr = pattern[0]  # Assume cyclic pattern
                    
                    request = PrefetchRequest(
                        address=next_addr,
                        size=64,
                        priority=confidence * 0.8,  # Lower priority than sequential
                        predicted_access_time=current_time + 0.002,
                        confidence=confidence,
                        prefetch_type='spatial'
                    )
                    requests.append(request)
        
        return requests


class LoopBoundaryDetector:
    """
    Loop boundary detector for nested loop structures
    Identifies loop patterns and predicts iteration boundaries
    """
    
    def __init__(self, max_loop_depth: int = 4):
        self.max_loop_depth = max_loop_depth
        self.loop_stack = []  # Stack of active loops
        self.loop_patterns = {}  # loop_id -> loop information
        self.access_counters = defaultdict(int)  # address -> access count
        self.loop_boundaries = defaultdict(list)  # loop_id -> boundary addresses
    
    def detect_loop_entry(self, access: MemoryAccess) -> bool:
        """Detect if access indicates loop entry"""
        addr = access.address
        
        # Simple heuristic: repeated access to same address suggests loop
        self.access_counters[addr] += 1
        
        if self.access_counters[addr] == 2:  # Second access to same address
            # Potential loop entry
            loop_id = f"loop_{addr}_{len(self.loop_stack)}"
            
            loop_info = {
                'id': loop_id,
                'entry_addr': addr,
                'start_time': access.timestamp,
                'iteration_count': 0,
                'access_pattern': []
            }
            
            self.loop_stack.append(loop_info)
            self.loop_patterns[loop_id] = loop_info
            
            return True
        
        return False
    
    def detect_loop_exit(self, access: MemoryAccess) -> Optional[Dict]:
        """Detect if access indicates loop exit"""
        if not self.loop_stack:
            return None
        
        current_loop = self.loop_stack[-1]
        
        # Simple heuristic: access outside loop pattern suggests exit
        pattern = current_loop['access_pattern']
        
        if len(pattern) > 4:  # Need some pattern history
            # Check if current access breaks the pattern
            recent_pattern = [a.address for a in pattern[-4:]]
            if access.address not in recent_pattern:
                # Potential loop exit
                loop_info = self.loop_stack.pop()
                loop_info['end_time'] = access.timestamp
                loop_info['duration'] = access.timestamp - loop_info['start_time']
                
                return loop_info
        
        # Add access to current loop pattern
        current_loop['access_pattern'].append(access)
        current_loop['iteration_count'] += 1
        
        return None
    
    def predict_loop_prefetch(self, current_access: MemoryAccess) -> List[PrefetchRequest]:
        """Predict prefetch opportunities based on loop patterns"""
        requests = []
        
        # Check if we're in a loop
        if not self.loop_stack:
            return requests
        
        current_loop = self.loop_stack[-1]
        pattern = current_loop['access_pattern']
        
        if len(pattern) < 4:
            return requests
        
        # Predict next few accesses in loop
        pattern_addrs = [a.address for a in pattern[-4:]]
        
        # Simple pattern matching: assume cyclic access
        current_pos = len(pattern) % len(pattern_addrs)
        
        for i in range(1, min(3, len(pattern_addrs))):  # Prefetch next 2 accesses
            next_pos = (current_pos + i) % len(pattern_addrs)
            next_addr = pattern_addrs[next_pos]
            
            request = PrefetchRequest(
                address=next_addr,
                size=64,
                priority=0.6,  # Medium priority
                predicted_access_time=current_access.timestamp + i * 0.001,
                confidence=0.7,
                prefetch_type='temporal'
            )
            requests.append(request)
        
        return requests


class VoyagerPrefetcher(nn.Module):
    """
    Voyager-style hierarchical neural network for memory access prediction
    Uses page-aware offset embedding and attention mechanisms
    """
    
    def __init__(self, page_size: int = 4096, offset_bits: int = 12, 
                 embedding_dim: int = 64, hidden_dim: int = 128):
        super().__init__()
        
        self.page_size = page_size
        self.offset_bits = offset_bits
        self.embedding_dim = embedding_dim
        
        # Page embedding
        self.page_embedding = nn.Embedding(2**20, embedding_dim)  # 1M pages
        
        # Offset experts (multiple small networks for different offset patterns)
        self.num_experts = 8
        self.offset_experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(offset_bits, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, embedding_dim)
            ) for _ in range(self.num_experts)
        ])
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(embedding_dim, num_heads=4)
        
        # Prediction head
        self.predictor = nn.Sequential(
            nn.Linear(embedding_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, offset_bits)  # Predict offset
        )
        
        # History encoder
        self.history_encoder = nn.LSTM(embedding_dim, hidden_dim, batch_first=True)
    
    def forward(self, addresses: torch.Tensor, history: torch.Tensor) -> torch.Tensor:
        """
        Forward pass for address prediction
        
        Args:
            addresses: Current addresses [batch_size, seq_len]
            history: Historical access patterns [batch_size, history_len, features]
        
        Returns:
            Predicted next addresses [batch_size, offset_bits]
        """
        batch_size, seq_len = addresses.shape
        
        # Split addresses into pages and offsets
        pages = addresses >> self.offset_bits
        offsets = addresses & ((1 << self.offset_bits) - 1)
        
        # Page embeddings
        page_emb = self.page_embedding(pages)  # [batch_size, seq_len, embedding_dim]
        
        # Offset expert processing
        offset_features = []
        for expert in self.offset_experts:
            # Convert offsets to one-hot or normalized representation
            offset_norm = offsets.float() / (2**self.offset_bits)
            offset_feat = expert(offset_norm.unsqueeze(-1))  # [batch_size, seq_len, embedding_dim]
            offset_features.append(offset_feat)
        
        # Attention over offset experts
        offset_stack = torch.stack(offset_features, dim=2)  # [batch_size, seq_len, num_experts, embedding_dim]
        offset_stack = offset_stack.view(batch_size * seq_len, self.num_experts, self.embedding_dim)
        
        # Self-attention over experts
        attended_offset, _ = self.attention(
            offset_stack, offset_stack, offset_stack
        )  # [batch_size * seq_len, num_experts, embedding_dim]
        
        # Global pooling over experts
        attended_offset = attended_offset.mean(dim=1)  # [batch_size * seq_len, embedding_dim]
        attended_offset = attended_offset.view(batch_size, seq_len, self.embedding_dim)
        
        # Combine page and offset features
        combined_features = page_emb + attended_offset  # [batch_size, seq_len, embedding_dim]
        
        # Process with history
        if history is not None and history.size(1) > 0:
            history_out, _ = self.history_encoder(history)
            # Use last hidden state
            history_context = history_out[:, -1, :]  # [batch_size, hidden_dim]
            
            # Expand to match sequence length
            history_context = history_context.unsqueeze(1).expand(-1, seq_len, -1)
            
            # Combine with current features (project history to embedding_dim)
            if not hasattr(self, 'history_proj'):
                self.history_proj = nn.Linear(history_context.size(-1), self.embedding_dim).to(history_context.device)
            combined_features = combined_features + self.history_proj(history_context)
        
        # Predict next offset
        predictions = self.predictor(combined_features)  # [batch_size, seq_len, offset_bits]
        
        # Return prediction for last timestep
        return predictions[:, -1, :]  # [batch_size, offset_bits]


class NPUVectorRunahead:
    """
    NPU Vector Runahead (NVR) system
    Combines multiple detection mechanisms for comprehensive prefetching
    """
    
    def __init__(self, enable_ml_prefetch: bool = True):
        # Initialize detectors
        self.stride_detector = StrideDetector()
        self.sparse_detector = SparseChainDetector()
        self.loop_detector = LoopBoundaryDetector()
        
        # ML-based prefetcher
        self.enable_ml_prefetch = enable_ml_prefetch
        if enable_ml_prefetch:
            self.ml_prefetcher = VoyagerPrefetcher()
            self.ml_optimizer = torch.optim.Adam(self.ml_prefetcher.parameters(), lr=0.001)
            self.access_history = deque(maxlen=1000)
        
        # Prefetch management
        self.active_prefetches = {}  # address -> PrefetchRequest
        self.prefetch_accuracy = defaultdict(float)
        self.prefetch_stats = {
            'total_requests': 0,
            'successful_prefetches': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
    
    def process_memory_access(self, access: MemoryAccess) -> List[PrefetchRequest]:
        """Process memory access and generate prefetch requests"""
        all_requests = []
        
        # Record access in all detectors
        self.stride_detector.record_access(access)
        
        # Check for indirect access patterns
        if access.access_type in ['read', 'write']:
            # Simplified: assume some accesses are indirect
            if access.address % 8 == 0:  # Pointer-aligned access
                self.sparse_detector.record_indirect_access(
                    access.address, access.address + 64, access
                )
        
        # Loop detection
        if self.loop_detector.detect_loop_entry(access):
            logger.debug(f"Loop entry detected at {access.address}")
        
        loop_exit = self.loop_detector.detect_loop_exit(access)
        if loop_exit:
            logger.debug(f"Loop exit detected: {loop_exit['id']}")
        
        # Generate prefetch requests
        
        # 1. Stride-based prefetching
        if access.buffer_id is not None:
            stride_request = self.stride_detector.predict_next_access(access.buffer_id)
            if stride_request:
                all_requests.append(stride_request)
        
        # 2. Sparse chain prefetching
        sparse_requests = self.sparse_detector.predict_chain_access(access.address)
        all_requests.extend(sparse_requests)
        
        # 3. Loop-based prefetching
        loop_requests = self.loop_detector.predict_loop_prefetch(access)
        all_requests.extend(loop_requests)
        
        # 4. ML-based prefetching
        if self.enable_ml_prefetch:
            ml_requests = self._ml_predict_prefetch(access)
            all_requests.extend(ml_requests)
        
        # Filter and prioritize requests
        filtered_requests = self._filter_prefetch_requests(all_requests)
        
        # Update statistics
        self.prefetch_stats['total_requests'] += len(filtered_requests)
        
        return filtered_requests
    
    def _ml_predict_prefetch(self, access: MemoryAccess) -> List[PrefetchRequest]:
        """Use ML model for prefetch prediction"""
        if not hasattr(self, 'ml_prefetcher'):
            return []
        
        # Add to history
        self.access_history.append(access)
        
        if len(self.access_history) < 10:
            return []
        
        try:
            # Prepare input data
            recent_accesses = list(self.access_history)[-10:]
            addresses = torch.tensor([a.address for a in recent_accesses]).unsqueeze(0)
            
            # Simple history features (could be enhanced)
            history_features = torch.tensor([
                [a.size, a.timestamp % 1000, hash(a.access_type) % 100] 
                for a in recent_accesses
            ]).unsqueeze(0).float()
            
            # Predict next offset
            with torch.no_grad():
                predicted_offset = self.ml_prefetcher(addresses, history_features)
                predicted_offset = torch.sigmoid(predicted_offset) * (2**12)  # Scale to offset range
            
            # Convert to prefetch request
            current_page = access.address >> 12
            predicted_address = (current_page << 12) + int(predicted_offset[0].sum().item())
            
            request = PrefetchRequest(
                address=predicted_address,
                size=64,
                priority=0.5,
                predicted_access_time=access.timestamp + 0.001,
                confidence=0.6,
                prefetch_type='ml_predicted'
            )
            
            return [request]
            
        except Exception as e:
            # 降低日志级别，避免输出过多警告
            logger.debug(f"ML prefetch prediction failed: {e}")
            return []
    
    def _filter_prefetch_requests(self, requests: List[PrefetchRequest]) -> List[PrefetchRequest]:
        """Filter and prioritize prefetch requests"""
        if not requests:
            return []
        
        # Remove duplicates
        unique_requests = {}
        for req in requests:
            key = (req.address, req.size)
            if key not in unique_requests or req.priority > unique_requests[key].priority:
                unique_requests[key] = req
        
        filtered = list(unique_requests.values())
        
        # Sort by priority
        filtered.sort(key=lambda x: x.priority, reverse=True)
        
        # Limit number of requests
        max_requests = 8
        return filtered[:max_requests]
    
    def update_prefetch_accuracy(self, address: int, was_hit: bool):
        """Update prefetch accuracy statistics"""
        if was_hit:
            self.prefetch_stats['successful_prefetches'] += 1
            self.prefetch_stats['cache_hits'] += 1
        else:
            self.prefetch_stats['cache_misses'] += 1
        
        # Update per-address accuracy
        if address in self.prefetch_accuracy:
            current_accuracy = self.prefetch_accuracy[address]
            self.prefetch_accuracy[address] = 0.9 * current_accuracy + 0.1 * (1.0 if was_hit else 0.0)
        else:
            self.prefetch_accuracy[address] = 1.0 if was_hit else 0.0
    
    def get_prefetch_statistics(self) -> Dict[str, Any]:
        """Get prefetch performance statistics"""
        total_requests = self.prefetch_stats['total_requests']
        successful = self.prefetch_stats['successful_prefetches']
        
        accuracy = successful / max(total_requests, 1)
        hit_rate = self.prefetch_stats['cache_hits'] / max(
            self.prefetch_stats['cache_hits'] + self.prefetch_stats['cache_misses'], 1
        )
        
        return {
            'total_prefetch_requests': total_requests,
            'successful_prefetches': successful,
            'prefetch_accuracy': accuracy,
            'cache_hit_rate': hit_rate,
            'average_address_accuracy': np.mean(list(self.prefetch_accuracy.values())) if self.prefetch_accuracy else 0.0,
            'active_prefetches': len(self.active_prefetches)
        }


class DMACoordinatedPrefetcher:
    """
    DMA-coordinated prefetcher for hardware-accelerated data movement
    Implements priority-based scheduling and locality optimization
    """

    def __init__(self, num_dma_channels: int = 4, buffer_size: int = 1024*1024):
        self.num_dma_channels = num_dma_channels
        self.buffer_size = buffer_size

        # DMA channel management
        self.dma_channels = [{'busy': False, 'current_request': None}
                           for _ in range(num_dma_channels)]
        self.request_queue = deque()
        self.completed_transfers = []

        # Locality tracking
        self.locality_scores = defaultdict(float)
        self.access_patterns = defaultdict(list)

        # Performance metrics
        self.transfer_stats = {
            'total_transfers': 0,
            'total_bytes': 0,
            'average_latency': 0.0,
            'channel_utilization': [0.0] * num_dma_channels
        }

    def schedule_prefetch(self, requests: List[PrefetchRequest]) -> List[int]:
        """Schedule prefetch requests across DMA channels"""
        if not requests:
            return []

        # Sort requests by priority and locality
        sorted_requests = self._prioritize_requests(requests)

        scheduled_channels = []

        for request in sorted_requests:
            # Find available DMA channel
            channel_id = self._find_available_channel()

            if channel_id is not None:
                self._issue_dma_request(channel_id, request)
                scheduled_channels.append(channel_id)
            else:
                # Queue for later
                self.request_queue.append(request)

        return scheduled_channels

    def _prioritize_requests(self, requests: List[PrefetchRequest]) -> List[PrefetchRequest]:
        """Prioritize requests based on priority and locality"""
        def request_score(req: PrefetchRequest) -> float:
            # Base priority
            score = req.priority

            # Locality bonus
            locality_score = self._calculate_locality_score(req.address)
            score += locality_score * 0.3

            # Confidence bonus
            score += req.confidence * 0.2

            # Size penalty (prefer smaller transfers for better parallelism)
            size_penalty = min(0.2, req.size / 1024.0)
            score -= size_penalty

            return score

        return sorted(requests, key=request_score, reverse=True)

    def _calculate_locality_score(self, address: int) -> float:
        """Calculate locality score for address"""
        if address in self.locality_scores:
            return self.locality_scores[address]

        # Calculate based on recent access patterns
        page_addr = address >> 12  # 4KB pages

        # Check recent accesses to same page
        recent_accesses = 0
        current_time = time.time()

        for pattern_addr, access_times in self.access_patterns.items():
            pattern_page = pattern_addr >> 12
            if pattern_page == page_addr:
                # Count recent accesses (within last second)
                recent_accesses += sum(1 for t in access_times
                                     if current_time - t < 1.0)

        # Normalize score
        locality_score = min(1.0, recent_accesses / 10.0)
        self.locality_scores[address] = locality_score

        return locality_score

    def _find_available_channel(self) -> Optional[int]:
        """Find available DMA channel"""
        for i, channel in enumerate(self.dma_channels):
            if not channel['busy']:
                return i
        return None

    def _issue_dma_request(self, channel_id: int, request: PrefetchRequest):
        """Issue DMA request on specified channel"""
        channel = self.dma_channels[channel_id]

        # Mark channel as busy
        channel['busy'] = True
        channel['current_request'] = request

        # Simulate DMA transfer (in real implementation, this would be hardware call)
        transfer_time = self._estimate_transfer_time(request.size)

        # Schedule completion
        threading.Timer(transfer_time, self._complete_dma_transfer,
                       args=[channel_id]).start()

        # Update statistics
        self.transfer_stats['total_transfers'] += 1
        self.transfer_stats['total_bytes'] += request.size

        logger.debug(f"DMA transfer started on channel {channel_id}: "
                    f"addr={request.address:x}, size={request.size}")

    def _estimate_transfer_time(self, size: int) -> float:
        """Estimate DMA transfer time"""
        # Simplified model: base latency + bandwidth-limited transfer
        base_latency = 0.001  # 1ms base latency
        bandwidth = 1024 * 1024 * 1024  # 1 GB/s bandwidth

        transfer_time = base_latency + (size / bandwidth)
        return transfer_time

    def _complete_dma_transfer(self, channel_id: int):
        """Complete DMA transfer and free channel"""
        channel = self.dma_channels[channel_id]
        request = channel['current_request']

        if request:
            # Record completion
            completion_time = time.time()
            self.completed_transfers.append({
                'request': request,
                'channel_id': channel_id,
                'completion_time': completion_time
            })

            # Update access patterns
            self.access_patterns[request.address].append(completion_time)

            # Maintain pattern history
            if len(self.access_patterns[request.address]) > 10:
                self.access_patterns[request.address] = \
                    self.access_patterns[request.address][-5:]

        # Free channel
        channel['busy'] = False
        channel['current_request'] = None

        # Process queued requests
        if self.request_queue:
            next_request = self.request_queue.popleft()
            self._issue_dma_request(channel_id, next_request)

        logger.debug(f"DMA transfer completed on channel {channel_id}")

    def get_channel_utilization(self) -> List[float]:
        """Get current channel utilization"""
        utilization = []
        for i, channel in enumerate(self.dma_channels):
            if channel['busy']:
                utilization.append(1.0)
            else:
                utilization.append(0.0)

        return utilization

    def get_transfer_statistics(self) -> Dict[str, Any]:
        """Get DMA transfer statistics"""
        if self.completed_transfers:
            # Calculate average latency
            latencies = []
            for transfer in self.completed_transfers[-100:]:  # Last 100 transfers
                request = transfer['request']
                latency = transfer['completion_time'] - request.predicted_access_time
                latencies.append(latency)

            avg_latency = np.mean(latencies) if latencies else 0.0
        else:
            avg_latency = 0.0

        return {
            'total_transfers': self.transfer_stats['total_transfers'],
            'total_bytes_transferred': self.transfer_stats['total_bytes'],
            'average_latency': avg_latency,
            'current_utilization': self.get_channel_utilization(),
            'queue_length': len(self.request_queue),
            'completed_transfers': len(self.completed_transfers)
        }


class MemoryLatencyHider:
    """
    Memory latency hiding through operator fusion and graph optimization
    Integrates with TVM-style compilation for NPU scheduling
    """

    def __init__(self, fusion_threshold: int = 3):
        self.fusion_threshold = fusion_threshold
        self.fusion_opportunities = []
        self.memory_access_graph = defaultdict(list)
        self.operator_costs = {}

    def analyze_fusion_opportunities(self, nodes: Dict, edges: List[Tuple]) -> List[Dict]:
        """Analyze operator fusion opportunities for latency hiding"""
        fusion_groups = []

        # Build operator dependency graph
        op_graph = defaultdict(list)
        for start, end in edges:
            if (start in nodes and end in nodes and
                nodes[start]['op'] not in ['ALLOC', 'FREE'] and
                nodes[end]['op'] not in ['ALLOC', 'FREE']):
                op_graph[start].append(end)

        # Find fusable operator chains
        visited = set()

        for node_id in nodes:
            if node_id not in visited and nodes[node_id]['op'] not in ['ALLOC', 'FREE']:
                chain = self._find_fusable_chain(node_id, op_graph, nodes, visited)

                if len(chain) >= self.fusion_threshold:
                    fusion_group = {
                        'nodes': chain,
                        'estimated_speedup': self._estimate_fusion_speedup(chain, nodes),
                        'memory_reduction': self._estimate_memory_reduction(chain, nodes),
                        'fusion_type': self._classify_fusion_type(chain, nodes)
                    }
                    fusion_groups.append(fusion_group)

        return fusion_groups

    def _find_fusable_chain(self, start_node: int, op_graph: Dict,
                           nodes: Dict, visited: Set[int]) -> List[int]:
        """Find chain of fusable operators"""
        chain = []
        current = start_node

        while current is not None and current not in visited:
            visited.add(current)
            chain.append(current)

            # Find next fusable node
            next_node = None
            successors = op_graph.get(current, [])

            for succ in successors:
                if (succ not in visited and
                    self._can_fuse_operators(nodes[current], nodes[succ])):
                    next_node = succ
                    break

            current = next_node

        return chain

    def _can_fuse_operators(self, op1: Dict, op2: Dict) -> bool:
        """Check if two operators can be fused"""
        # Simple fusion rules
        fusable_ops = {'CONV', 'RELU', 'BATCH_NORM', 'ADD', 'MUL'}

        if op1['op'] in fusable_ops and op2['op'] in fusable_ops:
            # Check if they use compatible data types and sizes
            if (op1.get('type') == op2.get('type') and
                abs(op1.get('size', 0) - op2.get('size', 0)) < 1000):
                return True

        return False

    def _estimate_fusion_speedup(self, chain: List[int], nodes: Dict) -> float:
        """Estimate speedup from operator fusion"""
        if len(chain) < 2:
            return 1.0

        # Calculate individual execution time
        individual_time = sum(nodes[nid].get('cycles', 0) for nid in chain)

        # Estimate fused execution time (assume 20% overhead reduction)
        fusion_overhead_reduction = 0.2
        fused_time = individual_time * (1 - fusion_overhead_reduction)

        # Add memory access savings
        memory_savings = len(chain) - 1  # Intermediate results don't need memory access
        memory_access_cost = 10  # cycles per memory access
        fused_time -= memory_savings * memory_access_cost

        speedup = individual_time / max(fused_time, 1)
        return min(speedup, 3.0)  # Cap at 3x speedup

    def _estimate_memory_reduction(self, chain: List[int], nodes: Dict) -> int:
        """Estimate memory reduction from fusion"""
        # Intermediate buffers can be eliminated
        intermediate_memory = 0

        for i in range(1, len(chain) - 1):  # Skip first and last
            node = nodes[chain[i]]
            if 'size' in node:
                intermediate_memory += node['size']

        return intermediate_memory

    def _classify_fusion_type(self, chain: List[int], nodes: Dict) -> str:
        """Classify the type of fusion"""
        ops = [nodes[nid]['op'] for nid in chain]

        if all(op in ['CONV', 'RELU', 'BATCH_NORM'] for op in ops):
            return 'conv_activation_fusion'
        elif all(op in ['ADD', 'MUL', 'SUB'] for op in ops):
            return 'elementwise_fusion'
        elif 'CONV' in ops and 'ADD' in ops:
            return 'conv_residual_fusion'
        else:
            return 'generic_fusion'

    def apply_latency_hiding(self, schedule: List[int], nodes: Dict,
                           edges: List[Tuple]) -> List[int]:
        """Apply latency hiding optimizations to schedule"""
        # Find fusion opportunities
        fusion_groups = self.analyze_fusion_opportunities(nodes, edges)

        if not fusion_groups:
            return schedule

        # Apply best fusion opportunities
        optimized_schedule = schedule.copy()

        for group in sorted(fusion_groups, key=lambda x: x['estimated_speedup'], reverse=True):
            if group['estimated_speedup'] > 1.2:  # Only apply if significant speedup
                optimized_schedule = self._apply_fusion_group(
                    optimized_schedule, group, nodes
                )

        return optimized_schedule

    def _apply_fusion_group(self, schedule: List[int], group: Dict,
                           nodes: Dict) -> List[int]:
        """Apply fusion group to schedule"""
        fusion_nodes = group['nodes']

        # Find positions of fusion nodes in schedule
        positions = []
        for node_id in fusion_nodes:
            if node_id in schedule:
                positions.append(schedule.index(node_id))

        if not positions:
            return schedule

        # Remove individual nodes
        new_schedule = [nid for nid in schedule if nid not in fusion_nodes]

        # Insert fused group at earliest position
        insert_pos = min(positions)
        for i, node_id in enumerate(fusion_nodes):
            new_schedule.insert(insert_pos + i, node_id)

        return new_schedule
