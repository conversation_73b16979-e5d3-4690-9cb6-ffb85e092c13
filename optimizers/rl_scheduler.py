"""
Reinforcement Learning NPU Scheduler
Implements Graph Neural Network with PPO for intelligent task scheduling
Based on Decima and Wheatley frameworks
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, global_mean_pool
from torch_geometric.data import Data, Batch
import gymnasium as gym
from gymnasium import spaces
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.callbacks import BaseCallback
from typing import Dict, List, Tuple, Optional, Any
import networkx as nx
from collections import defaultdict
import logging
import os

# GPU Memory Optimization for 46G VRAM
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False
if torch.cuda.is_available():
    # Enable memory growth and optimization for large VRAM
    torch.cuda.empty_cache()
    # Set memory fraction to use most of 46G VRAM efficiently
    torch.cuda.set_per_process_memory_fraction(0.95)  # Use 95% of 46G = ~43.7G

logger = logging.getLogger(__name__)

class NPUSchedulingEnv(gym.Env):
    """
    NPU Scheduling Environment for Reinforcement Learning
    Models scheduling as a Markov Decision Process
    """
    
    def __init__(self, nodes: Dict, edges: List[Tuple], max_steps: int = 1000):
        super().__init__()
        
        self.nodes = nodes
        self.edges = edges
        self.max_steps = max_steps
        self.current_step = 0
        
        # Build graph structure
        self.graph = self._build_networkx_graph()
        self.node_list = list(nodes.keys())
        self.n_nodes = len(self.node_list)
        
        # Action space: select next node to schedule
        self.action_space = spaces.Discrete(self.n_nodes)
        
        # Observation space: graph features + system state
        node_features = 8  # cycles, size, type_encoding, etc.
        system_features = 4  # current_memory, scheduled_count, etc.
        self.observation_space = spaces.Dict({
            'node_features': spaces.Box(
                low=-np.inf, high=np.inf, 
                shape=(self.n_nodes, node_features), dtype=np.float32
            ),
            'edge_index': spaces.Box(
                low=0, high=self.n_nodes-1,
                shape=(2, len(edges)), dtype=np.int64
            ),
            'system_state': spaces.Box(
                low=-np.inf, high=np.inf,
                shape=(system_features,), dtype=np.float32
            ),
            'action_mask': spaces.Box(
                low=0, high=1,
                shape=(self.n_nodes,), dtype=np.bool_
            )
        })
        
        self.reset()
    
    def _build_networkx_graph(self) -> nx.DiGraph:
        """Build NetworkX graph for analysis"""
        G = nx.DiGraph()
        for node_id, node_data in self.nodes.items():
            G.add_node(node_id, **node_data)
        for start, end in self.edges:
            G.add_edge(start, end)
        return G
    
    def reset(self, seed=None, options=None):
        """Reset environment to initial state"""
        super().reset(seed=seed)
        
        self.current_step = 0
        self.scheduled_nodes = set()
        self.schedule = []
        self.current_memory = 0
        self.max_memory_used = 0
        self.buffer_sizes = {}
        
        # Calculate initial node priorities
        self._compute_node_priorities()
        
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
    
    def step(self, action: int):
        """Execute one step in the environment"""
        node_id = self.node_list[action]
        
        # Check if action is valid
        if not self._is_action_valid(node_id):
            # Invalid action penalty
            reward = -10.0
            observation = self._get_observation()
            info = self._get_info()
            return observation, reward, False, False, info
        
        # Execute action
        self._schedule_node(node_id)
        
        # Calculate reward
        reward = self._calculate_reward(node_id)
        
        # Check if done
        done = len(self.scheduled_nodes) == self.n_nodes or self.current_step >= self.max_steps
        truncated = self.current_step >= self.max_steps
        
        self.current_step += 1
        
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, reward, done, truncated, info
    
    def _is_action_valid(self, node_id: int) -> bool:
        """Check if scheduling this node is valid"""
        if node_id in self.scheduled_nodes:
            return False
        
        # Check dependencies
        node = self.nodes[node_id]
        for pred in node.get('predecessors', []):
            if pred not in self.scheduled_nodes:
                return False
        
        return True
    
    def _schedule_node(self, node_id: int):
        """Schedule a node and update system state"""
        self.scheduled_nodes.add(node_id)
        self.schedule.append(node_id)
        
        node = self.nodes[node_id]
        
        # Update memory usage
        if node['op'] == 'ALLOC':
            buf_id = node['buf_id']
            size = node['size']
            self.buffer_sizes[buf_id] = size
            self.current_memory += size
            self.max_memory_used = max(self.max_memory_used, self.current_memory)
        elif node['op'] == 'FREE':
            buf_id = node['buf_id']
            if buf_id in self.buffer_sizes:
                size = self.buffer_sizes[buf_id]
                self.current_memory -= size
                del self.buffer_sizes[buf_id]
    
    def _calculate_reward(self, node_id: int) -> float:
        """Calculate reward for scheduling this node"""
        node = self.nodes[node_id]
        
        # Base reward components
        latency_reward = -node.get('cycles', 0) * 0.01  # Minimize execution time
        
        # Memory efficiency reward
        memory_pressure = self.current_memory / 10000.0  # Normalize
        memory_reward = -memory_pressure * 0.1
        
        # Scheduling efficiency reward
        progress_reward = 1.0  # Base reward for making progress
        
        # Priority-based reward
        priority_reward = node.get('upward_rank', 0) * 0.001
        
        # FREE operation bonus
        if node['op'] == 'FREE':
            free_bonus = 2.0  # Encourage freeing memory
        else:
            free_bonus = 0.0
        
        total_reward = (latency_reward + memory_reward + 
                       progress_reward + priority_reward + free_bonus)
        
        return total_reward
    
    def _get_observation(self) -> Dict:
        """Get current observation"""
        # Node features
        node_features = np.zeros((self.n_nodes, 8), dtype=np.float32)
        
        for i, node_id in enumerate(self.node_list):
            node = self.nodes[node_id]
            
            # Basic features
            node_features[i, 0] = node.get('cycles', 0) / 100.0  # Normalized cycles
            node_features[i, 1] = node.get('size', 0) / 1000.0   # Normalized size
            node_features[i, 2] = 1.0 if node['op'] == 'ALLOC' else 0.0
            node_features[i, 3] = 1.0 if node['op'] == 'FREE' else 0.0
            node_features[i, 4] = 1.0 if node_id in self.scheduled_nodes else 0.0
            node_features[i, 5] = node.get('upward_rank', 0) / 1000.0
            node_features[i, 6] = node.get('downward_rank', 0) / 1000.0
            node_features[i, 7] = len(node.get('predecessors', [])) / 10.0
        
        # Edge index for PyTorch Geometric
        edge_index = np.array(self.edges, dtype=np.int64).T
        
        # System state
        system_state = np.array([
            self.current_memory / 10000.0,  # Normalized current memory
            len(self.scheduled_nodes) / self.n_nodes,  # Progress
            self.max_memory_used / 10000.0,  # Peak memory
            self.current_step / self.max_steps  # Time progress
        ], dtype=np.float32)
        
        # Action mask (valid actions)
        action_mask = np.array([
            self._is_action_valid(node_id) for node_id in self.node_list
        ], dtype=bool)
        
        return {
            'node_features': node_features,
            'edge_index': edge_index,
            'system_state': system_state,
            'action_mask': action_mask
        }
    
    def _get_info(self) -> Dict:
        """Get additional info"""
        return {
            'current_memory': self.current_memory,
            'max_memory': self.max_memory_used,
            'scheduled_count': len(self.scheduled_nodes),
            'schedule': self.schedule.copy()
        }
    
    def _compute_node_priorities(self):
        """Compute node priorities using graph analysis"""
        # This would be called from the main scheduler
        # For now, we'll use simple heuristics
        for node_id, node in self.nodes.items():
            if 'upward_rank' not in node:
                node['upward_rank'] = node.get('cycles', 0)
            if 'downward_rank' not in node:
                node['downward_rank'] = 0


class GraphNeuralNetwork(nn.Module):
    """
    Graph Neural Network for NPU scheduling
    Processes DAG structure and node features
    """
    
    def __init__(self, node_features: int = 8, hidden_dim: int = 512,
                 num_layers: int = 8, output_dim: int = 256):
        super().__init__()

        # Optimized for 46G VRAM - much larger networks
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # Enhanced GCN layers with larger capacity
        self.convs = nn.ModuleList()
        self.convs.append(GCNConv(node_features, hidden_dim))

        for _ in range(num_layers - 2):
            self.convs.append(GCNConv(hidden_dim, hidden_dim))

        self.convs.append(GCNConv(hidden_dim, output_dim))

        # Enhanced batch normalization
        self.batch_norms = nn.ModuleList()
        for _ in range(num_layers - 1):
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))

        # Layer normalization for better training stability
        self.layer_norms = nn.ModuleList()
        for _ in range(num_layers):
            self.layer_norms.append(nn.LayerNorm(hidden_dim if _ < num_layers - 1 else output_dim))

        # Attention mechanism for better feature learning
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, batch_first=True)

        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x, edge_index, batch=None):
        """Forward pass through GNN"""
        # Apply GCN layers
        for i, conv in enumerate(self.convs[:-1]):
            x = conv(x, edge_index)
            x = self.batch_norms[i](x)
            x = F.relu(x)
            x = self.dropout(x)
        
        # Final layer
        x = self.convs[-1](x, edge_index)
        
        # Global pooling if batch is provided
        if batch is not None:
            x = global_mean_pool(x, batch)
        
        return x


class NPUPolicyNetwork(nn.Module):
    """
    Policy network combining GNN with system state processing
    """
    
    def __init__(self, node_features: int = 8, system_features: int = 4,
                 gnn_hidden: int = 512, mlp_hidden: int = 1024):
        super().__init__()

        # Enhanced Graph neural network for 46G VRAM
        self.gnn = GraphNeuralNetwork(
            node_features=node_features,
            hidden_dim=gnn_hidden,
            num_layers=8,
            output_dim=256
        )

        # Enhanced system state processor
        self.system_mlp = nn.Sequential(
            nn.Linear(system_features, mlp_hidden),
            nn.LayerNorm(mlp_hidden),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(mlp_hidden, mlp_hidden // 2),
            nn.LayerNorm(mlp_hidden // 2),
            nn.ReLU(),
            nn.Linear(mlp_hidden // 2, 256)
        )

        # Enhanced combined processor with residual connections
        self.combined_mlp = nn.Sequential(
            nn.Linear(512, mlp_hidden),  # 256 + 256
            nn.LayerNorm(mlp_hidden),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(mlp_hidden, mlp_hidden),
            nn.LayerNorm(mlp_hidden),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(mlp_hidden, mlp_hidden // 2),
            nn.LayerNorm(mlp_hidden // 2),
            nn.ReLU(),
            nn.Linear(mlp_hidden // 2, 1)  # Output logits for each node
        )
    
    def forward(self, observation):
        """Forward pass through policy network"""
        node_features = observation['node_features']
        edge_index = observation['edge_index']
        system_state = observation['system_state']
        action_mask = observation['action_mask']
        
        # Process graph
        gnn_output = self.gnn(node_features, edge_index)  # [n_nodes, 32]
        
        # Process system state
        system_output = self.system_mlp(system_state)  # [32]
        
        # Combine features for each node
        batch_size = node_features.shape[0]
        system_expanded = system_output.unsqueeze(0).expand(batch_size, -1)  # [n_nodes, 32]
        combined_features = torch.cat([gnn_output, system_expanded], dim=1)  # [n_nodes, 64]
        
        # Get logits
        logits = self.combined_mlp(combined_features).squeeze(-1)  # [n_nodes]
        
        # Apply action mask
        logits = logits.masked_fill(~action_mask, float('-inf'))
        
        return logits


class RLSchedulingCallback(BaseCallback):
    """Callback for monitoring RL training progress"""

    def __init__(self, verbose=0):
        super().__init__(verbose)
        self.episode_rewards = []
        self.episode_lengths = []
        self.best_reward = float('-inf')

    def _on_step(self) -> bool:
        # Log training metrics
        if len(self.locals.get('infos', [])) > 0:
            for info in self.locals['infos']:
                if 'episode' in info:
                    episode_reward = info['episode']['r']
                    episode_length = info['episode']['l']

                    self.episode_rewards.append(episode_reward)
                    self.episode_lengths.append(episode_length)

                    if episode_reward > self.best_reward:
                        self.best_reward = episode_reward
                        if self.verbose > 0:
                            print(f"New best reward: {self.best_reward:.2f}")

        return True


class RLNPUScheduler:
    """
    Reinforcement Learning NPU Scheduler
    Integrates with the main OptimizedNPUScheduler
    """

    def __init__(self, nodes: Dict, edges: List[Tuple],
                 model_path: Optional[str] = None):
        self.nodes = nodes
        self.edges = edges
        self.model_path = model_path

        # Create environment
        self.env = NPUSchedulingEnv(nodes, edges)

        # Initialize or load model
        if model_path and os.path.exists(model_path):
            self.model = PPO.load(model_path, env=self.env)
            logger.info(f"Loaded RL model from {model_path}")
        else:
            self.model = None
            logger.info("No pre-trained model found, will use heuristic fallback")

    def train_model(self, total_timesteps: int = 100000,
                   save_path: str = "npu_scheduler_model"):
        """Train the RL model"""
        logger.info("Starting RL model training...")

        # Create vectorized environment for training - optimized for 46G VRAM
        vec_env = make_vec_env(
            lambda: NPUSchedulingEnv(self.nodes, self.edges),
            n_envs=16  # Increased parallel environments for better GPU utilization
        )

        # Initialize PPO model - optimized for large VRAM
        self.model = PPO(
            "MultiInputPolicy",
            vec_env,
            learning_rate=1e-4,  # Lower LR for more stable training with larger networks
            n_steps=4096,  # Larger rollout buffer for better sample efficiency
            batch_size=512,  # Much larger batch size to utilize 46G VRAM
            n_epochs=20,  # More epochs for better learning
            gamma=0.99,
            gae_lambda=0.95,
            clip_range=0.2,
            ent_coef=0.01,
            vf_coef=0.5,
            max_grad_norm=0.5,
            verbose=1,
            tensorboard_log="./tensorboard_logs/",
            device="cuda",  # Explicitly use CUDA
            policy_kwargs={
                "net_arch": [1024, 1024, 512],  # Much larger policy networks
                "activation_fn": torch.nn.ReLU,
                "ortho_init": False,
                "normalize_images": False,
                "optimizer_class": torch.optim.AdamW,  # Better optimizer
                "optimizer_kwargs": {
                    "eps": 1e-5,
                    "weight_decay": 1e-4
                }
            }
        )

        # Training callback
        callback = RLSchedulingCallback(verbose=1)

        # Train the model with enhanced progress tracking
        from tqdm import tqdm
        logger.info(f"Starting RL training for {total_timesteps} timesteps...")

        try:
            self.model.learn(
                total_timesteps=total_timesteps,
                callback=callback,
                progress_bar=True
            )
        except Exception as e:
            if "tqdm" in str(e) or "rich" in str(e):
                logger.warning("Progress bar dependencies not available, training without progress bar")
                self.model.learn(
                    total_timesteps=total_timesteps,
                    callback=callback,
                    progress_bar=False
                )
            else:
                raise e

        # Save the model
        self.model.save(save_path)
        logger.info(f"Model saved to {save_path}")

        return callback.episode_rewards, callback.episode_lengths

    def generate_schedule(self, deterministic: bool = True) -> List[int]:
        """Generate schedule using trained RL model"""
        if self.model is None:
            logger.warning("No trained model available, using fallback heuristic")
            return self._heuristic_fallback()

        obs, _ = self.env.reset()
        max_steps = len(self.nodes) * 2  # Safety limit to prevent infinite loops

        # Add progress bar for RL scheduling
        from tqdm import tqdm
        with tqdm(total=len(self.nodes), desc="RL Scheduling", unit="nodes") as pbar:
            for step in range(max_steps):
                action, _ = self.model.predict(obs, deterministic=deterministic)
                obs, reward, done, truncated, info = self.env.step(action)

                # Update progress bar
                current_scheduled = info.get('scheduled_count', 0)
                pbar.n = current_scheduled
                pbar.refresh()

                if done or truncated:
                    break

                if current_scheduled >= len(self.nodes):
                    break

        return info.get('schedule', self._heuristic_fallback())

    def _heuristic_fallback(self) -> List[int]:
        """Fallback heuristic when RL model is not available"""
        # Simple topological sort with priority
        in_degree = defaultdict(int)
        for start, end in self.edges:
            in_degree[end] += 1

        available = [node_id for node_id in self.nodes.keys()
                    if in_degree[node_id] == 0]
        schedule = []

        while available:
            # Sort by priority (FREE > small ALLOC > others)
            available.sort(key=lambda x: self._get_priority(x))
            node_id = available.pop(0)
            schedule.append(node_id)

            # Update available nodes
            for start, end in self.edges:
                if start == node_id:
                    in_degree[end] -= 1
                    if in_degree[end] == 0:
                        available.append(end)

        return schedule

    def _get_priority(self, node_id: int) -> float:
        """Get priority for heuristic fallback"""
        node = self.nodes[node_id]
        if node['op'] == 'FREE':
            return -1e9
        elif node['op'] == 'ALLOC':
            return node.get('size', 0)
        else:
            return -node.get('upward_rank', 0)


def create_curriculum_environments(base_nodes: Dict, base_edges: List[Tuple],
                                 difficulty_levels: int = 5) -> List[NPUSchedulingEnv]:
    """
    Create curriculum learning environments with increasing difficulty
    """
    environments = []

    for level in range(difficulty_levels):
        # Scale complexity based on level
        scale_factor = 0.2 + (level * 0.2)  # 0.2 to 1.0

        # Sample subset of nodes for lower levels
        if level < difficulty_levels - 1:
            n_nodes = max(10, int(len(base_nodes) * scale_factor))
            sampled_node_ids = list(base_nodes.keys())[:n_nodes]

            # Create subset
            subset_nodes = {nid: base_nodes[nid] for nid in sampled_node_ids}
            subset_edges = [(s, e) for s, e in base_edges
                           if s in sampled_node_ids and e in sampled_node_ids]
        else:
            # Full complexity for final level
            subset_nodes = base_nodes
            subset_edges = base_edges

        env = NPUSchedulingEnv(subset_nodes, subset_edges)
        environments.append(env)

    return environments


def train_with_curriculum(nodes: Dict, edges: List[Tuple],
                         total_timesteps: int = 500000,
                         save_path: str = "curriculum_npu_model") -> PPO:
    """
    Train RL model using curriculum learning
    """
    logger.info("Starting curriculum learning...")

    # Create curriculum environments
    envs = create_curriculum_environments(nodes, edges)

    model = None
    timesteps_per_level = total_timesteps // len(envs)

    for level, env in enumerate(envs):
        logger.info(f"Training level {level + 1}/{len(envs)} "
                   f"({len(env.nodes)} nodes)")

        # Create vectorized environment
        vec_env = make_vec_env(lambda: env, n_envs=4)

        if model is None:
            # Initialize model for first level
            model = PPO(
                "MultiInputPolicy",
                vec_env,
                learning_rate=3e-4,
                verbose=1
            )
        else:
            # Transfer to new environment
            model.set_env(vec_env)

        # Train on current level
        callback = RLSchedulingCallback(verbose=1)
        try:
            model.learn(
                total_timesteps=timesteps_per_level,
                callback=callback,
                reset_num_timesteps=False
            )
        except Exception as e:
            if "tqdm" in str(e) or "rich" in str(e):
                logger.warning("Progress bar dependencies not available, training without progress bar")
                model.learn(
                    total_timesteps=timesteps_per_level,
                    callback=callback,
                    reset_num_timesteps=False
                )
            else:
                raise e

        # Save intermediate model
        model.save(f"{save_path}_level_{level}")

    # Save final model
    model.save(save_path)
    logger.info(f"Curriculum training completed. Model saved to {save_path}")

    return model
