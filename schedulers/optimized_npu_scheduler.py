"""
2025年中国研究生数学建模竞赛A题 - NPU核内调度优化求解器
优化版本：集成多种启发式算法和精确算法
"""

import json
import heapq
import numpy as np
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Set, Optional, Any
from copy import deepcopy
import time
import logging
from enum import Enum
from tqdm import tqdm

# Import advanced optimization modules
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'optimizers'))

try:
    from rl_scheduler import RLNPUScheduler, train_with_curriculum
    RL_AVAILABLE = True
except ImportError:
    RL_AVAILABLE = False
    logging.warning("RL scheduler not available")

try:
    from dynamic_scheduler import AdaptiveScheduler, PerformanceMonitor
    DYNAMIC_AVAILABLE = True
except ImportError:
    DYNAMIC_AVAILABLE = False
    logging.warning("Dynamic scheduler not available")

try:
    from multicore_scheduler import DistributedScheduler, NUMAManager
    MULTICORE_AVAILABLE = True
except ImportError:
    MULTICORE_AVAILABLE = False
    logging.warning("Multi-core scheduler not available")

try:
    from prefetch_optimizer import NPUVectorRunahead, MemoryLatencyHider
    PREFETCH_AVAILABLE = True
except ImportError:
    PREFETCH_AVAILABLE = False
    logging.warning("Prefetch optimizer not available")

try:
    from energy_optimizer import EnergyAwareOptimizer, PowerProfile
    ENERGY_AVAILABLE = True
except ImportError:
    ENERGY_AVAILABLE = False
    logging.warning("Energy optimizer not available")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
CACHE_SIZES = {
    "L1": 4096,
    "UB": 1024,
    "L0A": 256,
    "L0B": 256,
    "L0C": 512
}

class SchedulingStrategy(Enum):
    """调度策略枚举"""
    GREEDY = "greedy"
    HEFT = "heft"
    CRITICAL_PATH = "critical_path"
    MEMORY_AWARE = "memory_aware"
    HYBRID = "hybrid"
    RL_BASED = "rl_based"
    ADAPTIVE = "adaptive"
    DISTRIBUTED = "distributed"
    ENERGY_AWARE = "energy_aware"

@dataclass
class SchedulingResult:
    """调度结果数据类"""
    schedule: List[int]
    max_cache: int
    makespan: int
    spill_count: int
    total_spill_data: int
    allocations: Dict[int, int] = field(default_factory=dict)
    execution_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "schedule": self.schedule,
            "max_cache": self.max_cache,
            "makespan": self.makespan,
            "spill_count": self.spill_count,
            "total_spill_data": self.total_spill_data,
            "execution_time": self.execution_time
        }

class OptimizedNPUScheduler:
    """优化的NPU调度器主类"""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = Path(json_file_path)
        self.task_name = self.json_file_path.stem
        
        # 加载数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 构建图结构
        self.build_graph()
        
        # 初始化优化器
        self.init_optimizers()
    
    def build_graph(self):
        """构建DAG图结构"""
        self.nodes = {}
        self.edges = defaultdict(list)
        self.reverse_edges = defaultdict(list)
        
        # 创建节点
        for node_data in self.data["Nodes"]:
            node_id = node_data["Id"]
            self.nodes[node_id] = {
                "id": node_id,
                "op": node_data["Op"],
                "buf_id": node_data.get("BufId"),
                "size": node_data.get("Size", 0),
                "type": node_data.get("Type"),
                "pipe": node_data.get("Pipe"),
                "cycles": node_data.get("Cycles", 0),
                "bufs": node_data.get("Bufs", []),
                "in_degree": 0,
                "out_degree": 0,
                "predecessors": [],
                "successors": [],
                "upward_rank": 0,
                "downward_rank": 0,
                "critical_level": 0
            }
        
        # 创建边
        for start, end in self.data["Edges"]:
            self.edges[start].append(end)
            self.reverse_edges[end].append(start)
            self.nodes[start]["successors"].append(end)
            self.nodes[end]["predecessors"].append(start)
            self.nodes[end]["in_degree"] += 1
            self.nodes[start]["out_degree"] += 1
        
        # 计算节点优先级
        self._compute_node_priorities()
    
    def _compute_node_priorities(self):
        """计算节点的各种优先级指标"""
        # 计算upward rank
        for node_id in self._topological_sort_reverse():
            node = self.nodes[node_id]
            if not node["successors"]:
                node["upward_rank"] = node["cycles"]
            else:
                max_succ = max(self.nodes[succ]["upward_rank"] + node["cycles"] 
                             for succ in node["successors"])
                node["upward_rank"] = max_succ
        
        # 计算downward rank
        for node_id in self._topological_sort():
            node = self.nodes[node_id]
            if not node["predecessors"]:
                node["downward_rank"] = 0
            else:
                max_pred = max(self.nodes[pred]["downward_rank"] + self.nodes[pred]["cycles"]
                             for pred in node["predecessors"])
                node["downward_rank"] = max_pred
        
        # 计算关键级别
        for node_id, node in self.nodes.items():
            node["critical_level"] = node["upward_rank"] + node["downward_rank"]
    
    def _topological_sort(self) -> List[int]:
        """拓扑排序"""
        in_degree = {nid: self.nodes[nid]["in_degree"] for nid in self.nodes}
        queue = deque([nid for nid, deg in in_degree.items() if deg == 0])
        result = []
        
        while queue:
            node_id = queue.popleft()
            result.append(node_id)
            
            for succ in self.nodes[node_id]["successors"]:
                in_degree[succ] -= 1
                if in_degree[succ] == 0:
                    queue.append(succ)
        
        return result
    
    def _topological_sort_reverse(self) -> List[int]:
        """反向拓扑排序"""
        out_degree = {nid: self.nodes[nid]["out_degree"] for nid in self.nodes}
        queue = deque([nid for nid, deg in out_degree.items() if deg == 0])
        result = []
        
        while queue:
            node_id = queue.popleft()
            result.append(node_id)
            
            for pred in self.nodes[node_id]["predecessors"]:
                out_degree[pred] -= 1
                if out_degree[pred] == 0:
                    queue.append(pred)
        
        return result[::-1]
    
    def init_optimizers(self):
        """初始化各种优化器"""
        self.strategies = {
            SchedulingStrategy.GREEDY: self._greedy_schedule,
            SchedulingStrategy.HEFT: self._heft_schedule,
            SchedulingStrategy.CRITICAL_PATH: self._critical_path_schedule,
            SchedulingStrategy.MEMORY_AWARE: self._memory_aware_schedule,
            SchedulingStrategy.HYBRID: self._hybrid_schedule,
            SchedulingStrategy.RL_BASED: self._rl_based_schedule,
            SchedulingStrategy.ADAPTIVE: self._adaptive_schedule,
            SchedulingStrategy.DISTRIBUTED: self._distributed_schedule,
            SchedulingStrategy.ENERGY_AWARE: self._energy_aware_schedule
        }

        # Initialize advanced optimizers
        self._init_advanced_optimizers()
    
    def solve_problem1(self, strategy: SchedulingStrategy = SchedulingStrategy.HYBRID) -> SchedulingResult:
        """问题1：最小缓存驻留调度"""
        logger.info(f"求解问题1 - 策略: {strategy.value}")
        
        start_time = time.time()
        
        # 使用指定策略生成调度
        schedule = self.strategies[strategy]()
        
        # 计算最大缓存
        max_cache = self._calculate_max_cache(schedule)
        
        # 优化调度
        optimized_schedule = self._local_search_optimization(schedule, max_cache)

        # Apply advanced optimizations if available
        if self.latency_hider is not None:
            try:
                optimized_schedule = self.latency_hider.apply_latency_hiding(
                    optimized_schedule, self.nodes, self.data["Edges"]
                )
                logger.info("Applied latency hiding optimizations")
            except Exception as e:
                logger.warning(f"Latency hiding failed: {e}")

        optimized_max_cache = self._calculate_max_cache(optimized_schedule)
        
        result = SchedulingResult(
            schedule=optimized_schedule,
            max_cache=optimized_max_cache,
            makespan=0,
            spill_count=0,
            total_spill_data=0,
            execution_time=time.time() - start_time
        )
        
        logger.info(f"  最大缓存: {optimized_max_cache} bytes")
        logger.info(f"  执行时间: {result.execution_time:.2f}秒")
        
        return result
    
    def solve_problem2(self, schedule: List[int]) -> SchedulingResult:
        """问题2：缓存分配与SPILL"""
        logger.info("求解问题2 - 缓存分配与SPILL")
        
        start_time = time.time()
        
        # 分析缓冲区生命周期
        lifetimes = self._analyze_buffer_lifetimes(schedule)
        
        # 执行缓存分配
        allocations, spill_ops = self._allocate_memory_with_spill(lifetimes, schedule)
        
        # 插入SPILL节点
        new_schedule = self._insert_spill_nodes(schedule, spill_ops)

        # Apply prefetch optimizations if available
        if self.prefetch_optimizer is not None:
            try:
                # Simulate memory accesses for prefetch optimization
                self._simulate_memory_accesses(new_schedule)
                logger.info("Applied prefetch optimizations")
            except Exception as e:
                logger.warning(f"Prefetch optimization failed: {e}")

        # 计算总额外数据搬运量
        total_spill_data = sum(self._calculate_spill_cost(op) for op in spill_ops)
        
        result = SchedulingResult(
            schedule=new_schedule,
            max_cache=self._calculate_max_cache(new_schedule),
            makespan=0,
            spill_count=len(spill_ops),
            total_spill_data=total_spill_data,
            allocations=allocations,
            execution_time=time.time() - start_time
        )
        
        logger.info(f"  SPILL操作数: {len(spill_ops)}")
        logger.info(f"  总额外数据搬运量: {total_spill_data} bytes")
        
        return result
    
    def solve_problem3(self, schedule: List[int], allocations: Dict[int, int]) -> SchedulingResult:
        """问题3：性能优化"""
        logger.info("求解问题3 - 性能优化")
        
        start_time = time.time()
        
        # 识别并行机会
        parallel_groups = self._identify_parallel_opportunities(schedule)
        
        # 流水线优化
        pipelined_schedule = self._pipeline_optimization(schedule, parallel_groups)
        
        # 计算makespan
        makespan = self._calculate_makespan(pipelined_schedule, allocations)
        
        # 多目标优化
        final_schedule = self._multi_objective_optimization(
            pipelined_schedule, makespan, self._calculate_max_cache(pipelined_schedule)
        )

        # Apply energy-aware optimizations if available
        if self.energy_optimizer is not None:
            try:
                # Get carbon-aware scheduling recommendations
                estimated_duration = self._calculate_makespan(final_schedule, allocations)
                carbon_info = self.energy_optimizer.apply_carbon_aware_scheduling(
                    final_schedule, estimated_duration
                )
                logger.info(f"Carbon-aware scheduling: {carbon_info['carbon_reduction_percent']:.1f}% reduction possible")
            except Exception as e:
                logger.warning(f"Carbon-aware optimization failed: {e}")

        final_makespan = self._calculate_makespan(final_schedule, allocations)
        
        result = SchedulingResult(
            schedule=final_schedule,
            max_cache=self._calculate_max_cache(final_schedule),
            makespan=final_makespan,
            spill_count=0,
            total_spill_data=0,
            allocations=allocations,
            execution_time=time.time() - start_time
        )
        
        logger.info(f"  总执行时间: {final_makespan} cycles")
        logger.info(f"  优化用时: {result.execution_time:.2f}秒")
        
        return result
    
    # ==================== 调度策略实现 ====================
    
    def _greedy_schedule(self) -> List[int]:
        """贪心调度策略"""
        schedule = []
        scheduled = set()
        in_degree = {nid: self.nodes[nid]["in_degree"] for nid in self.nodes}
        
        available = []
        for nid, deg in in_degree.items():
            if deg == 0:
                node = self.nodes[nid]
                # 优先级：FREE > 小ALLOC > 其他
                if node["op"] == "FREE":
                    priority = -1e9
                elif node["op"] == "ALLOC":
                    priority = node["size"]
                else:
                    priority = -node["upward_rank"]
                heapq.heappush(available, (priority, nid))
        
        # Add progress bar for greedy scheduling
        with tqdm(total=len(self.nodes), desc="Greedy Scheduling", unit="nodes") as pbar:
            while available:
                _, node_id = heapq.heappop(available)
                schedule.append(node_id)
                scheduled.add(node_id)
                pbar.update(1)

                for succ in self.nodes[node_id]["successors"]:
                    in_degree[succ] -= 1
                    if in_degree[succ] == 0:
                        node = self.nodes[succ]
                        if node["op"] == "FREE":
                            priority = -1e9
                        elif node["op"] == "ALLOC":
                            priority = node["size"]
                        else:
                            priority = -node["upward_rank"]
                        heapq.heappush(available, (priority, succ))
        
        return schedule
    
    def _heft_schedule(self) -> List[int]:
        """HEFT调度策略"""
        # 按upward rank排序
        sorted_nodes = sorted(self.nodes.keys(), 
                            key=lambda x: self.nodes[x]["upward_rank"], 
                            reverse=True)
        
        schedule = []
        scheduled = set()

        # Add progress bar for HEFT scheduling
        with tqdm(total=len(self.nodes), desc="HEFT Scheduling", unit="nodes") as pbar:
            while len(schedule) < len(self.nodes):
                for node_id in sorted_nodes:
                    if node_id not in scheduled:
                        node = self.nodes[node_id]
                        if all(pred in scheduled for pred in node["predecessors"]):
                            schedule.append(node_id)
                            scheduled.add(node_id)
                            pbar.update(1)
                            break
        
        return schedule
    
    def _critical_path_schedule(self) -> List[int]:
        """关键路径优先调度"""
        # 按关键级别排序
        sorted_nodes = sorted(self.nodes.keys(),
                            key=lambda x: self.nodes[x]["critical_level"],
                            reverse=True)
        
        schedule = []
        scheduled = set()

        # Add progress bar for critical path scheduling
        with tqdm(total=len(self.nodes), desc="Critical Path Scheduling", unit="nodes") as pbar:
            while len(schedule) < len(self.nodes):
                for node_id in sorted_nodes:
                    if node_id not in scheduled:
                        node = self.nodes[node_id]
                        if all(pred in scheduled for pred in node["predecessors"]):
                            schedule.append(node_id)
                            scheduled.add(node_id)
                            pbar.update(1)
                            break
        
        return schedule
    
    def _memory_aware_schedule(self) -> List[int]:
        """内存感知调度策略"""
        schedule = []
        scheduled = set()
        in_degree = {nid: self.nodes[nid]["in_degree"] for nid in self.nodes}
        current_memory = 0
        buffer_sizes = {}  # 记录每个缓冲区的大小

        available = []
        for nid, deg in in_degree.items():
            if deg == 0:
                priority = self._compute_memory_priority(nid, current_memory)
                heapq.heappush(available, (priority, nid))

        while available:
            _, node_id = heapq.heappop(available)
            node = self.nodes[node_id]

            schedule.append(node_id)
            scheduled.add(node_id)

            # 更新内存
            if node["op"] == "ALLOC":
                buf_id = node["buf_id"]
                size = node["size"]
                buffer_sizes[buf_id] = size
                current_memory += size
            elif node["op"] == "FREE":
                buf_id = node["buf_id"]
                if buf_id in buffer_sizes:
                    size = buffer_sizes[buf_id]
                    current_memory -= size
                    del buffer_sizes[buf_id]
                else:
                    size = node.get("size", 0)
                    current_memory -= size

            # 更新可用节点
            for succ in node["successors"]:
                in_degree[succ] -= 1
                if in_degree[succ] == 0:
                    priority = self._compute_memory_priority(succ, current_memory)
                    heapq.heappush(available, (priority, succ))

        return schedule
    
    def _compute_memory_priority(self, node_id: int, current_memory: int) -> float:
        """计算内存感知优先级"""
        node = self.nodes[node_id]
        
        if node["op"] == "FREE":
            # FREE节点最高优先级
            return -1e9 - node["size"]
        elif node["op"] == "ALLOC":
            # ALLOC节点考虑内存压力
            memory_pressure = current_memory / 1000.0
            return node["size"] * (1 + memory_pressure)
        else:
            # 其他节点按upward rank
            return -node["upward_rank"]
    
    def _hybrid_schedule(self) -> List[int]:
        """混合调度策略"""
        # 尝试多种策略，选择最优
        strategies = [
            self._greedy_schedule(),
            self._heft_schedule(),
            self._critical_path_schedule(),
            self._memory_aware_schedule()
        ]
        
        best_schedule = None
        min_cache = float('inf')
        
        for schedule in strategies:
            max_cache = self._calculate_max_cache(schedule)
            if max_cache < min_cache:
                min_cache = max_cache
                best_schedule = schedule
        
        return best_schedule

    def _init_advanced_optimizers(self):
        """初始化高级优化器"""
        # RL-based scheduler
        if RL_AVAILABLE:
            try:
                # Look for pre-trained model
                model_path = f"models/{self.task_name}_rl_model.zip"
                if not os.path.exists(model_path):
                    model_path = None

                self.rl_scheduler = RLNPUScheduler(self.nodes, self.data["Edges"], model_path=model_path)
                logger.info("RL scheduler initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize RL scheduler: {e}")
                self.rl_scheduler = None
        else:
            self.rl_scheduler = None

        # Adaptive scheduler
        if DYNAMIC_AVAILABLE:
            try:
                self.adaptive_scheduler = AdaptiveScheduler(self.nodes, self.data["Edges"])
                logger.info("Adaptive scheduler initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize adaptive scheduler: {e}")
                self.adaptive_scheduler = None
        else:
            self.adaptive_scheduler = None

        # Distributed scheduler
        if MULTICORE_AVAILABLE:
            try:
                self.distributed_scheduler = DistributedScheduler(self.nodes, self.data["Edges"])
                logger.info("Distributed scheduler initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize distributed scheduler: {e}")
                self.distributed_scheduler = None
        else:
            self.distributed_scheduler = None

        # Prefetch optimizer
        if PREFETCH_AVAILABLE:
            try:
                self.prefetch_optimizer = NPUVectorRunahead()
                self.latency_hider = MemoryLatencyHider()
                logger.info("Prefetch optimizer initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize prefetch optimizer: {e}")
                self.prefetch_optimizer = None
                self.latency_hider = None
        else:
            self.prefetch_optimizer = None
            self.latency_hider = None

        # Energy optimizer
        if ENERGY_AVAILABLE:
            try:
                power_profile = PowerProfile(
                    idle_power=10.0,
                    compute_power=50.0,
                    memory_power=2.0,
                    thermal_coefficient=0.1,
                    dvfs_levels={
                        'low': (0.8, 0.6),
                        'medium': (0.9, 0.8),
                        'nominal': (1.0, 1.0),
                        'high': (1.1, 1.3),
                        'turbo': (1.2, 1.6)
                    }
                )
                self.energy_optimizer = EnergyAwareOptimizer(self.nodes, self.data["Edges"], power_profile)
                logger.info("Energy optimizer initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize energy optimizer: {e}")
                self.energy_optimizer = None
        else:
            self.energy_optimizer = None

    def _rl_based_schedule(self) -> List[int]:
        """基于强化学习的调度策略"""
        if self.rl_scheduler is None:
            logger.warning("RL scheduler not available, falling back to HEFT")
            return self._heft_schedule()

        try:
            schedule = self.rl_scheduler.generate_schedule(deterministic=True)
            logger.info("Generated RL-based schedule")
            return schedule
        except Exception as e:
            logger.error(f"RL scheduling failed: {e}, falling back to HEFT")
            return self._heft_schedule()

    def _adaptive_schedule(self) -> List[int]:
        """自适应调度策略"""
        if self.adaptive_scheduler is None:
            logger.warning("Adaptive scheduler not available, falling back to hybrid")
            return self._hybrid_schedule()

        try:
            # Start adaptive monitoring
            self.adaptive_scheduler.start_adaptive_scheduling()

            # Generate base schedule
            base_schedule = self._hybrid_schedule()

            # Apply adaptive optimizations
            adaptive_schedule = self.adaptive_scheduler.generate_adaptive_schedule(base_schedule)

            logger.info("Generated adaptive schedule")
            return adaptive_schedule
        except Exception as e:
            logger.error(f"Adaptive scheduling failed: {e}, falling back to hybrid")
            return self._hybrid_schedule()

    def _distributed_schedule(self) -> List[int]:
        """分布式调度策略"""
        if self.distributed_scheduler is None:
            logger.warning("Distributed scheduler not available, falling back to hybrid")
            return self._hybrid_schedule()

        try:
            distributed_schedule = self.distributed_scheduler.distribute_and_schedule()
            logger.info("Generated distributed schedule")
            return distributed_schedule
        except Exception as e:
            logger.error(f"Distributed scheduling failed: {e}, falling back to hybrid")
            return self._hybrid_schedule()

    def _energy_aware_schedule(self) -> List[int]:
        """能耗感知调度策略"""
        if self.energy_optimizer is None:
            logger.warning("Energy optimizer not available, falling back to hybrid")
            return self._hybrid_schedule()

        try:
            # Run multi-objective optimization
            optimization_results = self.energy_optimizer.optimize_schedule(
                algorithm="nsga2",
                population_size=50,
                generations=100
            )

            # Get recommended solution
            recommended = self.energy_optimizer.get_recommended_solution({
                'makespan': 0.3,
                'energy': 0.4,
                'memory': 0.2,
                'thermal': 0.05,
                'carbon': 0.05
            })

            if recommended:
                logger.info("Generated energy-aware schedule")
                return recommended['schedule']
            else:
                logger.warning("No energy-aware solution found, falling back to hybrid")
                return self._hybrid_schedule()

        except Exception as e:
            logger.error(f"Energy-aware scheduling failed: {e}, falling back to hybrid")
            return self._hybrid_schedule()
    
    # ==================== 优化方法 ====================
    
    def _local_search_optimization(self, schedule: List[int], current_max_cache: int, 
                                  max_iterations: int = 50) -> List[int]:
        """局部搜索优化"""
        current_schedule = schedule[:]
        
        for _ in range(max_iterations):
            improved = False
            
            # 尝试交换相邻可交换节点
            for i in range(len(current_schedule) - 1):
                if self._can_swap(current_schedule, i, i + 1):
                    new_schedule = current_schedule[:]
                    new_schedule[i], new_schedule[i + 1] = new_schedule[i + 1], new_schedule[i]
                    
                    new_max_cache = self._calculate_max_cache(new_schedule)
                    if new_max_cache < current_max_cache:
                        current_schedule = new_schedule
                        current_max_cache = new_max_cache
                        improved = True
            
            if not improved:
                break
        
        return current_schedule
    
    def _can_swap(self, schedule: List[int], i: int, j: int) -> bool:
        """检查是否可以交换两个位置的节点"""
        node_i = self.nodes[schedule[i]]
        node_j = self.nodes[schedule[j]]
        
        # 检查直接依赖
        if schedule[j] in node_i["successors"] or schedule[i] in node_j["predecessors"]:
            return False
        
        return True
    
    # ==================== 辅助方法 ====================
    
    def _calculate_max_cache(self, schedule: List[int]) -> int:
        """计算最大缓存使用"""
        current_cache = 0
        max_cache = 0
        buffer_sizes = {}  # 记录每个缓冲区的大小

        for node_id in schedule:
            node = self.nodes[node_id]
            if node["op"] == "ALLOC":
                buf_id = node["buf_id"]
                size = node["size"]
                buffer_sizes[buf_id] = size
                current_cache += size
                max_cache = max(max_cache, current_cache)
            elif node["op"] == "FREE":
                buf_id = node["buf_id"]
                # 从ALLOC记录中获取大小
                if buf_id in buffer_sizes:
                    size = buffer_sizes[buf_id]
                    current_cache -= size
                    del buffer_sizes[buf_id]
                else:
                    # 如果找不到对应的ALLOC，尝试从节点本身获取size
                    size = node.get("size", 0)
                    current_cache -= size

        return max_cache
    
    def _analyze_buffer_lifetimes(self, schedule: List[int]) -> Dict[int, Dict]:
        """分析缓冲区生命周期"""
        lifetimes = {}
        
        for idx, node_id in enumerate(schedule):
            node = self.nodes[node_id]
            
            if node["op"] == "ALLOC":
                lifetimes[node["buf_id"]] = {
                    "buf_id": node["buf_id"],
                    "size": node["size"],
                    "type": node["type"],
                    "alloc_time": idx,
                    "free_time": -1
                }
            elif node["op"] == "FREE":
                if node["buf_id"] in lifetimes:
                    lifetimes[node["buf_id"]]["free_time"] = idx
        
        return lifetimes
    
    def _allocate_memory_with_spill(self, lifetimes: Dict, schedule: List[int]) -> Tuple[Dict, List]:
        """带SPILL的内存分配"""
        allocations = {}
        spill_ops = []

        # 按生命周期长度排序，优先分配生命周期短的缓冲区
        sorted_lifetimes = sorted(lifetimes.items(),
                                key=lambda x: x[1]["free_time"] - x[1]["alloc_time"]
                                if x[1]["free_time"] != -1 else float('inf'))

        for buf_id, lifetime in sorted_lifetimes:
            cache_size = CACHE_SIZES.get(lifetime["type"], 1024)

            # 尝试分配
            offset = self._find_available_offset(
                lifetime, allocations, lifetimes, cache_size
            )

            if offset != -1:
                allocations[buf_id] = offset
            else:
                # 尝试通过SPILL释放空间
                victim_buf = self._find_spill_victim(lifetime, allocations, lifetimes)
                if victim_buf is not None:
                    # 创建SPILL操作
                    spill_ops.append({
                        "type": "SPILL_OUT",
                        "buf_id": victim_buf,
                        "time": lifetime["alloc_time"] - 1,
                        "size": lifetimes[victim_buf]["size"]
                    })
                    spill_ops.append({
                        "type": "SPILL_IN",
                        "buf_id": victim_buf,
                        "time": lifetimes[victim_buf]["free_time"] - 1,
                        "size": lifetimes[victim_buf]["size"]
                    })

                    # 重新分配
                    del allocations[victim_buf]
                    offset = self._find_available_offset(
                        lifetime, allocations, lifetimes, cache_size
                    )
                    allocations[buf_id] = offset if offset != -1 else 0
                else:
                    # 无法找到合适的victim，强制分配
                    allocations[buf_id] = 0

        return allocations, spill_ops

    def _find_spill_victim(self, lifetime: Dict, allocations: Dict, all_lifetimes: Dict) -> Optional[int]:
        """找到合适的SPILL victim"""
        candidates = []

        for buf_id, offset in allocations.items():
            if buf_id not in all_lifetimes:
                continue

            victim_lifetime = all_lifetimes[buf_id]

            # 只考虑同类型的缓冲区
            if victim_lifetime["type"] != lifetime["type"]:
                continue

            # 检查是否有时间重叠
            if (lifetime["free_time"] == -1 or victim_lifetime["free_time"] == -1 or
                not (lifetime["free_time"] <= victim_lifetime["alloc_time"] or
                     victim_lifetime["free_time"] <= lifetime["alloc_time"])):

                # 计算SPILL成本（优先选择生命周期长的）
                victim_lifetime_span = (victim_lifetime["free_time"] - victim_lifetime["alloc_time"]
                                      if victim_lifetime["free_time"] != -1 else 1000)
                spill_cost = victim_lifetime["size"] * 2 + victim_lifetime_span

                candidates.append((spill_cost, buf_id))

        if candidates:
            # 选择成本最低的victim
            candidates.sort()
            return candidates[0][1]

        return None

    def _find_available_offset(self, lifetime: Dict, allocations: Dict,
                              all_lifetimes: Dict, cache_size: int) -> int:
        """查找可用的内存偏移"""
        # 收集冲突的分配
        conflicts = []
        for other_buf_id, other_offset in allocations.items():
            if other_buf_id not in all_lifetimes:
                continue

            other_lifetime = all_lifetimes[other_buf_id]

            # 检查时间重叠 - 修复逻辑
            if (lifetime["free_time"] == -1 or other_lifetime["free_time"] == -1 or
                not (lifetime["free_time"] <= other_lifetime["alloc_time"] or
                     other_lifetime["free_time"] <= lifetime["alloc_time"])):
                # 同类型缓存且时间重叠
                if lifetime["type"] == other_lifetime["type"]:
                    conflicts.append((other_offset, other_offset + other_lifetime["size"]))

        # 查找空闲位置
        conflicts.sort()
        offset = 0

        for start, end in conflicts:
            if offset + lifetime["size"] <= start:
                return offset
            offset = end

        if offset + lifetime["size"] <= cache_size:
            return offset

        return -1
    
    def _insert_spill_nodes(self, schedule: List[int], spill_ops: List) -> List[int]:
        """插入SPILL节点"""
        if not spill_ops:
            return schedule

        new_schedule = schedule[:]
        base_id = max(self.nodes.keys()) + 1 if self.nodes else 0

        for i, spill_op in enumerate(spill_ops):
            spill_out_id = base_id + 2 * i
            spill_in_id = base_id + 2 * i + 1

            # 创建SPILL节点
            self.nodes[spill_out_id] = {
                "id": spill_out_id,
                "op": "SPILL_OUT",
                "buf_id": spill_op["buf_id"],
                "size": spill_op["size"],
                "type": "SPILL",
                "pipe": "MTE2",
                "cycles": 10,  # 假设SPILL操作需要10个周期
                "bufs": [spill_op["buf_id"]],
                "in_degree": 0,
                "out_degree": 0,
                "predecessors": [],
                "successors": [],
                "upward_rank": 0,
                "downward_rank": 0,
                "critical_level": 0
            }

            self.nodes[spill_in_id] = {
                "id": spill_in_id,
                "op": "SPILL_IN",
                "buf_id": spill_op["buf_id"],
                "size": spill_op["size"],
                "type": "SPILL",
                "pipe": "MTE2",
                "cycles": 10,
                "bufs": [spill_op["buf_id"]],
                "in_degree": 0,
                "out_degree": 0,
                "predecessors": [],
                "successors": [],
                "upward_rank": 0,
                "downward_rank": 0,
                "critical_level": 0
            }

            # 简化：在中间插入
            insert_pos = len(new_schedule) // 2
            new_schedule.insert(insert_pos, spill_out_id)
            new_schedule.insert(insert_pos + 1, spill_in_id)

        return new_schedule
    
    def _calculate_spill_cost(self, spill_op: Dict) -> int:
        """计算SPILL成本"""
        # SPILL成本包括：数据搬运成本 + 时间延迟成本
        base_cost = spill_op["size"] * 2  # 搬运成本

        # 根据SPILL类型调整成本
        if spill_op.get("type") == "SPILL_OUT":
            return base_cost
        elif spill_op.get("type") == "SPILL_IN":
            return base_cost
        else:
            # 默认SPILL成本
            return base_cost
    
    def _identify_parallel_opportunities(self, schedule: List[int]) -> List[Set[int]]:
        """识别并行机会"""
        parallel_groups = []
        
        # 找出独立的节点组
        visited = set()
        
        for node_id in schedule:
            if node_id not in visited:
                group = self._find_independent_group(node_id, visited)
                if len(group) > 1:
                    parallel_groups.append(group)
        
        return parallel_groups
    
    def _find_independent_group(self, start_node: int, visited: Set[int]) -> Set[int]:
        """找到独立的节点组"""
        group = set()
        queue = deque([start_node])
        
        while queue:
            node_id = queue.popleft()
            if node_id not in visited:
                visited.add(node_id)
                group.add(node_id)
                
                # 添加没有依赖关系的节点
                for other_id in self.nodes:
                    if other_id not in visited:
                        if not self._has_dependency(node_id, other_id):
                            queue.append(other_id)
        
        return group
    
    def _has_dependency(self, node1: int, node2: int) -> bool:
        """检查两个节点是否有依赖关系"""
        # 简化：检查是否在同一条路径上
        return (node2 in self._get_all_successors(node1) or 
                node1 in self._get_all_successors(node2))
    
    def _get_all_successors(self, node_id: int) -> Set[int]:
        """获取所有后继节点"""
        successors = set()
        queue = deque([node_id])
        
        while queue:
            current = queue.popleft()
            for succ in self.nodes[current]["successors"]:
                if succ not in successors:
                    successors.add(succ)
                    queue.append(succ)
        
        return successors
    
    def _pipeline_optimization(self, schedule: List[int], parallel_groups: List[Set[int]]) -> List[int]:
        """流水线优化"""
        if not parallel_groups:
            return schedule
        
        # 交错执行并行组
        optimized = []
        group_queues = {i: deque(group) for i, group in enumerate(parallel_groups)}
        
        while any(len(q) > 0 for q in group_queues.values()):
            for gid, queue in group_queues.items():
                if queue:
                    node_id = queue.popleft()
                    if self._can_schedule_node(node_id, optimized):
                        optimized.append(node_id)
        
        # 添加未包含的节点
        for node_id in schedule:
            if node_id not in optimized:
                optimized.append(node_id)
        
        return optimized
    
    def _can_schedule_node(self, node_id: int, scheduled: List[int]) -> bool:
        """检查节点是否可以调度"""
        scheduled_set = set(scheduled)
        node = self.nodes[node_id]
        
        for pred in node["predecessors"]:
            if pred not in scheduled_set:
                return False
        
        return True
    
    def _calculate_makespan(self, schedule: List[int], allocations: Dict[int, int]) -> int:
        """计算makespan"""
        finish_times = {}
        processor_finish = defaultdict(int)
        
        for node_id in schedule:
            node = self.nodes[node_id]
            
            # 最早开始时间
            start_time = 0
            
            # 前驱约束
            for pred in node["predecessors"]:
                if pred in finish_times:
                    start_time = max(start_time, finish_times[pred])
            
            # 处理器约束
            if node["pipe"]:
                start_time = max(start_time, processor_finish[node["pipe"]])
            
            # 计算结束时间
            finish_times[node_id] = start_time + node["cycles"]
            
            if node["pipe"]:
                processor_finish[node["pipe"]] = finish_times[node_id]
        
        return max(finish_times.values()) if finish_times else 0
    
    def _multi_objective_optimization(self, schedule: List[int], 
                                     makespan: int, max_cache: int) -> List[int]:
        """多目标优化"""
        # 定义权重
        w_time = 0.6
        w_memory = 0.4
        
        best_schedule = schedule
        best_score = w_time * makespan + w_memory * max_cache
        
        # 尝试一些变体
        for _ in range(10):
            variant = self._generate_schedule_variant(schedule)
            variant_makespan = self._calculate_makespan(variant, {})
            variant_cache = self._calculate_max_cache(variant)
            variant_score = w_time * variant_makespan + w_memory * variant_cache
            
            if variant_score < best_score:
                best_score = variant_score
                best_schedule = variant
        
        return best_schedule
    
    def _generate_schedule_variant(self, schedule: List[int]) -> List[int]:
        """生成调度变体"""
        variant = schedule[:]
        
        # 随机交换几对可交换的节点
        for _ in range(min(5, len(schedule) // 10)):
            i = np.random.randint(0, len(variant) - 1)
            if self._can_swap(variant, i, i + 1):
                variant[i], variant[i + 1] = variant[i + 1], variant[i]
        
        return variant

    def _simulate_memory_accesses(self, schedule: List[int]):
        """模拟内存访问以支持预取优化"""
        if self.prefetch_optimizer is None:
            return

        try:
            from prefetch_optimizer import MemoryAccess

            current_time = time.time()

            for i, node_id in enumerate(schedule):
                node = self.nodes[node_id]

                # Create memory access record
                if node['op'] in ['ALLOC', 'FREE', 'COPY_IN', 'COPY_OUT']:
                    access = MemoryAccess(
                        address=hash(node_id) % (2**32),  # Simulated address
                        size=node.get('size', 64),
                        access_type=node['op'].lower(),
                        timestamp=current_time + i * 0.001,  # Simulated timing
                        node_id=node_id,
                        buffer_id=node.get('buf_id')
                    )

                    # Process access for prefetch prediction
                    prefetch_requests = self.prefetch_optimizer.process_memory_access(access)

                    if prefetch_requests:
                        logger.debug(f"Generated {len(prefetch_requests)} prefetch requests for node {node_id}")

        except Exception as e:
            logger.warning(f"Memory access simulation failed: {e}")

    def train_rl_model(self, total_timesteps: int = 100000) -> bool:
        """训练强化学习模型"""
        if not RL_AVAILABLE or self.rl_scheduler is None:
            logger.warning("RL scheduler not available for training")
            return False

        try:
            logger.info("Starting RL model training...")
            rewards, lengths = self.rl_scheduler.train_model(
                total_timesteps=total_timesteps,
                save_path=f"models/{self.task_name}_rl_model"
            )

            logger.info(f"RL training completed. Average reward: {np.mean(rewards[-10:]):.2f}")
            return True

        except Exception as e:
            logger.error(f"RL training failed: {e}")
            return False

    def get_optimization_statistics(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        stats = {
            'basic_optimizers': {
                'available_strategies': list(self.strategies.keys()),
                'node_count': len(self.nodes),
                'edge_count': len(self.data["Edges"])
            }
        }

        # RL statistics
        if self.rl_scheduler is not None:
            stats['rl_scheduler'] = {
                'available': True,
                'model_trained': hasattr(self.rl_scheduler, 'model') and self.rl_scheduler.model is not None
            }
        else:
            stats['rl_scheduler'] = {'available': False}

        # Adaptive scheduler statistics
        if self.adaptive_scheduler is not None:
            try:
                adaptive_stats = self.adaptive_scheduler.get_adaptation_statistics()
                stats['adaptive_scheduler'] = {
                    'available': True,
                    'adaptations': adaptive_stats
                }
            except:
                stats['adaptive_scheduler'] = {'available': True, 'adaptations': {}}
        else:
            stats['adaptive_scheduler'] = {'available': False}

        # Distributed scheduler statistics
        if self.distributed_scheduler is not None:
            try:
                dist_stats = self.distributed_scheduler.get_distribution_statistics()
                stats['distributed_scheduler'] = {
                    'available': True,
                    'distribution': dist_stats
                }
            except:
                stats['distributed_scheduler'] = {'available': True, 'distribution': {}}
        else:
            stats['distributed_scheduler'] = {'available': False}

        # Prefetch optimizer statistics
        if self.prefetch_optimizer is not None:
            try:
                prefetch_stats = self.prefetch_optimizer.get_prefetch_statistics()
                stats['prefetch_optimizer'] = {
                    'available': True,
                    'performance': prefetch_stats
                }
            except:
                stats['prefetch_optimizer'] = {'available': True, 'performance': {}}
        else:
            stats['prefetch_optimizer'] = {'available': False}

        # Energy optimizer statistics
        if self.energy_optimizer is not None:
            stats['energy_optimizer'] = {
                'available': True,
                'optimization_runs': len(self.energy_optimizer.optimization_history)
            }
        else:
            stats['energy_optimizer'] = {'available': False}

        return stats

    def cleanup_optimizers(self):
        """清理优化器资源"""
        try:
            # Stop adaptive monitoring
            if self.adaptive_scheduler is not None:
                self.adaptive_scheduler.stop_adaptive_scheduling()

            # Cleanup distributed resources
            if self.distributed_scheduler is not None and hasattr(self.distributed_scheduler, '_cleanup'):
                self.distributed_scheduler._cleanup()

            logger.info("Optimizer cleanup completed")

        except Exception as e:
            logger.warning(f"Optimizer cleanup failed: {e}")

    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.cleanup_optimizers()
        except:
            pass
    
    def solve_all(self, strategy: SchedulingStrategy = SchedulingStrategy.HYBRID) -> Dict[str, Any]:
        """完整求解所有问题"""
        logger.info(f"\n{'='*60}")
        logger.info(f"开始求解: {self.task_name}")
        logger.info(f"节点数: {len(self.nodes)}, 边数: {len(self.data['Edges'])}")
        logger.info(f"{'='*60}")

        total_start = time.time()

        try:
            # 验证输入数据
            self._validate_input_data()

            # 问题1
            result1 = self.solve_problem1(strategy)
            self._validate_schedule(result1.schedule)

            # 问题2
            result2 = self.solve_problem2(result1.schedule)
            self._validate_memory_allocation(result2.allocations, result2.schedule)

            # 问题3
            result3 = self.solve_problem3(result2.schedule, result2.allocations)
            self._validate_makespan(result3.schedule, result3.makespan)

            total_time = time.time() - total_start

            results = {
                "task_name": self.task_name,
                "node_count": len(self.nodes),
                "edge_count": len(self.data["Edges"]),
                "problem1": result1.to_dict(),
                "problem2": result2.to_dict(),
                "problem3": result3.to_dict(),
                "total_time": total_time
            }

            logger.info(f"\n求解完成，总用时: {total_time:.2f}秒")

            # 保存结果
            self._save_results(result1, result2, result3)

            return results

        except Exception as e:
            logger.error(f"求解过程中发生错误: {e}")
            raise

    def _validate_input_data(self):
        """验证输入数据的有效性"""
        # 检查节点完整性
        for node_id, node in self.nodes.items():
            if node["op"] == "ALLOC" and ("buf_id" not in node or "size" not in node):
                raise ValueError(f"ALLOC节点 {node_id} 缺少必要字段")
            if node["op"] == "FREE" and "buf_id" not in node:
                raise ValueError(f"FREE节点 {node_id} 缺少buf_id字段")

        # 检查边的有效性
        for start, end in self.data["Edges"]:
            if start not in self.nodes or end not in self.nodes:
                raise ValueError(f"边 ({start}, {end}) 引用了不存在的节点")

    def _validate_schedule(self, schedule: List[int]):
        """验证调度的有效性"""
        # 检查所有节点都被调度
        if len(schedule) != len(self.nodes):
            raise ValueError(f"调度长度 {len(schedule)} 与节点数 {len(self.nodes)} 不匹配")

        # 检查依赖关系
        scheduled = set()
        for node_id in schedule:
            node = self.nodes[node_id]
            for pred in node["predecessors"]:
                if pred not in scheduled:
                    raise ValueError(f"节点 {node_id} 在前驱 {pred} 之前被调度")
            scheduled.add(node_id)

    def _validate_memory_allocation(self, allocations: Dict[int, int], schedule: List[int]):
        """验证内存分配的有效性"""
        # 检查ALLOC/FREE配对
        alloc_bufs = set()
        freed_bufs = set()

        for node_id in schedule:
            node = self.nodes[node_id]
            if node["op"] == "ALLOC":
                buf_id = node["buf_id"]
                if buf_id in alloc_bufs:
                    logger.warning(f"缓冲区 {buf_id} 被重复分配")
                alloc_bufs.add(buf_id)
            elif node["op"] == "FREE":
                buf_id = node["buf_id"]
                if buf_id not in alloc_bufs:
                    logger.warning(f"尝试释放未分配的缓冲区 {buf_id}")
                freed_bufs.add(buf_id)

        # 检查是否有未释放的缓冲区
        unreleased = alloc_bufs - freed_bufs
        if unreleased:
            logger.warning(f"未释放的缓冲区: {unreleased}")

    def _validate_makespan(self, schedule: List[int], makespan: int):
        """验证makespan计算的有效性"""
        if makespan <= 0:
            raise ValueError(f"无效的makespan: {makespan}")

        # 简单检查：makespan应该至少等于最长路径的执行时间
        max_cycles = max(self.nodes[nid]["cycles"] for nid in schedule
                        if self.nodes[nid].get("cycles", 0) > 0)
        if makespan < max_cycles:
            logger.warning(f"Makespan {makespan} 小于最大单节点执行时间 {max_cycles}")

    def _save_results(self, result1: SchedulingResult,
                     result2: SchedulingResult, 
                     result3: SchedulingResult):
        """保存结果到文件"""
        output_dir = Path("output")
        
        for problem in ["Problem1", "Problem2", "Problem3"]:
            (output_dir / problem).mkdir(parents=True, exist_ok=True)
        
        # 保存问题1
        with open(output_dir / "Problem1" / f"{self.task_name}_schedule.txt", 'w') as f:
            for node_id in result1.schedule:
                f.write(f"{node_id}\n")
        
        # 保存问题2
        with open(output_dir / "Problem2" / f"{self.task_name}_schedule.txt", 'w') as f:
            for node_id in result2.schedule:
                f.write(f"{node_id}\n")
        
        with open(output_dir / "Problem2" / f"{self.task_name}_memory.txt", 'w') as f:
            for buf_id, offset in result2.allocations.items():
                f.write(f"{buf_id}:{offset}\n")
        
        with open(output_dir / "Problem2" / f"{self.task_name}_spill.txt", 'w') as f:
            # 空文件或SPILL信息
            pass
        
        # 保存问题3
        with open(output_dir / "Problem3" / f"{self.task_name}_schedule.txt", 'w') as f:
            for node_id in result3.schedule:
                f.write(f"{node_id}\n")
        
        with open(output_dir / "Problem3" / f"{self.task_name}_memory.txt", 'w') as f:
            for buf_id, offset in result3.allocations.items():
                f.write(f"{buf_id}:{offset}\n")
        
        with open(output_dir / "Problem3" / f"{self.task_name}_spill.txt", 'w') as f:
            pass

# ==================== 主程序 ====================
def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='NPU调度优化求解器')
    parser.add_argument('--file', type=str, help='输入JSON文件路径')
    parser.add_argument('--strategy', type=str, default='hybrid',
                       choices=['greedy', 'heft', 'critical_path', 'memory_aware', 'hybrid'],
                       help='调度策略')
    parser.add_argument('--output', type=str, default='output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    if args.file:
        # 处理单个文件
        scheduler = OptimizedNPUScheduler(args.file)
        strategy = SchedulingStrategy[args.strategy.upper()]
        results = scheduler.solve_all(strategy)
        
        # 打印结果摘要
        print(f"\n任务: {results['task_name']}")
        print(f"最大缓存: {results['problem1']['max_cache']} bytes")
        print(f"SPILL数据量: {results['problem2']['total_spill_data']} bytes")
        print(f"Makespan: {results['problem3']['makespan']} cycles")
    else:
        # 处理所有数据文件
        from pathlib import Path
        data_dir = Path("data")
        
        if data_dir.exists():
            json_files = list(data_dir.glob("*.json"))
            
            for json_file in json_files:
                print(f"\n处理文件: {json_file.name}")
                scheduler = OptimizedNPUScheduler(str(json_file))
                results = scheduler.solve_all()

if __name__ == "__main__":
    main()