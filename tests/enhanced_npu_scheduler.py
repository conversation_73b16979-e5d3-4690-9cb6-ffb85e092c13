"""
增强版NPU调度器 - 集成机器学习、动态调度、多核扩展、预取优化和能耗优化
2025年中国研究生数学建模竞赛A题优化实现
"""

import json
import heapq
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Set, Optional, Any
from copy import deepcopy
import time
import logging
from enum import Enum
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

# 尝试导入可选依赖
try:
    from torch_geometric.nn import GCNConv, global_mean_pool
    from torch_geometric.data import Data, Batch
    HAS_TORCH_GEOMETRIC = True
except ImportError:
    HAS_TORCH_GEOMETRIC = False
    print("Warning: torch_geometric not installed, GNN features disabled")

try:
    from pymoo.algorithms.moo.nsga2 import NSGA2
    from pymoo.core.problem import Problem
    from pymoo.optimize import minimize
    HAS_PYMOO = True
except ImportError:
    HAS_PYMOO = False
    print("Warning: pymoo not installed, multi-objective optimization disabled")

# ==================== 配置和常量 ====================
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

CACHE_SIZES = {
    "L1": 4096,
    "UB": 1024,
    "L0A": 256,
    "L0B": 256,
    "L0C": 512
}

# 能耗模型参数
ENERGY_MODEL = {
    "ALLOC": 0.1,      # mJ
    "FREE": 0.05,      # mJ
    "COPY_IN": 1.0,    # mJ
    "COPY_OUT": 1.0,   # mJ
    "COMPUTE": 2.0,    # mJ per cycle
    "IDLE": 0.01,      # mJ per cycle
    "SPILL": 1.5       # mJ
}

class SchedulingStrategy(Enum):
    """调度策略枚举"""
    GREEDY = "greedy"
    HEFT = "heft"
    CRITICAL_PATH = "critical_path"
    MEMORY_AWARE = "memory_aware"
    RL_ENHANCED = "rl_enhanced"
    MULTI_OBJECTIVE = "multi_objective"
    HYBRID = "hybrid"

# ==================== 强化学习模块 ====================
class GraphNeuralNetwork(nn.Module):
    """用于DAG表示学习的图神经网络"""
    def __init__(self, node_features=8, hidden_dim=64, output_dim=32):
        super().__init__()
        if HAS_TORCH_GEOMETRIC:
            self.conv1 = GCNConv(node_features, hidden_dim)
            self.conv2 = GCNConv(hidden_dim, hidden_dim)
            self.conv3 = GCNConv(hidden_dim, output_dim)
        else:
            # 简单的MLP替代
            self.fc1 = nn.Linear(node_features, hidden_dim)
            self.fc2 = nn.Linear(hidden_dim, hidden_dim)
            self.fc3 = nn.Linear(hidden_dim, output_dim)
        
        self.dropout = nn.Dropout(0.2)
    
    def forward(self, x, edge_index=None, batch=None):
        if HAS_TORCH_GEOMETRIC and edge_index is not None:
            x = F.relu(self.conv1(x, edge_index))
            x = self.dropout(x)
            x = F.relu(self.conv2(x, edge_index))
            x = self.dropout(x)
            x = self.conv3(x, edge_index)
            if batch is not None:
                x = global_mean_pool(x, batch)
        else:
            x = F.relu(self.fc1(x))
            x = self.dropout(x)
            x = F.relu(self.fc2(x))
            x = self.dropout(x)
            x = self.fc3(x)
        return x

class RLSchedulingAgent:
    """基于强化学习的调度智能体"""
    def __init__(self, state_dim=32, action_dim=128, learning_rate=3e-4):
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # Actor-Critic网络
        self.actor = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, action_dim),
            nn.Softmax(dim=-1)
        )
        
        self.critic = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )
        
        self.gnn = GraphNeuralNetwork()
        
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=learning_rate)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=learning_rate)
        self.gnn_optimizer = torch.optim.Adam(self.gnn.parameters(), lr=learning_rate)
        
        self.memory = []
        self.gamma = 0.99
        self.epsilon = 0.2  # PPO clip parameter
    
    def get_action(self, state, dag_features=None):
        """基于当前状态选择动作"""
        with torch.no_grad():
            if dag_features is not None:
                state = self.gnn(dag_features)
            
            state = torch.FloatTensor(state).unsqueeze(0)
            probs = self.actor(state)
            action_dist = torch.distributions.Categorical(probs)
            action = action_dist.sample()
            
        return action.item(), action_dist.log_prob(action).item()
    
    def compute_reward(self, metrics):
        """计算复合奖励"""
        # 多目标奖励函数
        latency_reward = -metrics.get('makespan', 0) / 1000.0
        memory_reward = -metrics.get('max_cache', 0) / 10000.0
        energy_reward = -metrics.get('energy', 0) / 100.0
        utilization_reward = metrics.get('utilization', 0)
        
        # 加权组合
        total_reward = (0.4 * latency_reward + 
                       0.2 * memory_reward + 
                       0.2 * energy_reward + 
                       0.2 * utilization_reward)
        return total_reward
    
    def update(self, batch_size=32):
        """PPO更新"""
        if len(self.memory) < batch_size:
            return
        
        # 采样批次
        batch = np.random.choice(self.memory, batch_size, replace=False)
        states, actions, rewards, next_states, dones = zip(*batch)
        
        states = torch.FloatTensor(states)
        actions = torch.LongTensor(actions)
        rewards = torch.FloatTensor(rewards)
        next_states = torch.FloatTensor(next_states)
        dones = torch.FloatTensor(dones)
        
        # 计算优势
        values = self.critic(states).squeeze()
        next_values = self.critic(next_states).squeeze()
        advantages = rewards + self.gamma * next_values * (1 - dones) - values
        
        # PPO更新
        for _ in range(10):  # PPO epochs
            action_probs = self.actor(states)
            dist = torch.distributions.Categorical(action_probs)
            new_log_probs = dist.log_prob(actions)
            
            # 计算比率
            old_log_probs = torch.FloatTensor([b[4] for b in batch])
            ratio = torch.exp(new_log_probs - old_log_probs)
            
            # Clipped objective
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1 - self.epsilon, 1 + self.epsilon) * advantages
            actor_loss = -torch.min(surr1, surr2).mean()
            
            # 更新Actor
            self.actor_optimizer.zero_grad()
            actor_loss.backward()
            self.actor_optimizer.step()
        
        # 更新Critic
        critic_loss = F.mse_loss(values, rewards + self.gamma * next_values * (1 - dones))
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()

# ==================== 动态调度模块 ====================
class NPUPerformanceMonitor:
    """NPU性能监控器"""
    def __init__(self, sampling_interval_ms=10):
        self.sampling_interval = sampling_interval_ms / 1000.0
        self.metrics_queue = queue.Queue(maxsize=1000)
        self.running = False
        self.monitor_thread = None
        
        # 性能指标
        self.current_metrics = {
            'utilization': 0.0,
            'memory_usage': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'thermal_state': 25.0,  # 温度
            'power_consumption': 0.0,
            'task_throughput': 0.0,
            'queue_depth': 0
        }
        
        self.history = defaultdict(list)
    
    def start(self):
        """启动监控"""
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.start()
    
    def stop(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            # 模拟采集性能数据
            metrics = self._collect_metrics()
            self.metrics_queue.put(metrics)
            
            # 更新历史
            for key, value in metrics.items():
                self.history[key].append(value)
                # 保持最近1000个采样
                if len(self.history[key]) > 1000:
                    self.history[key].pop(0)
            
            time.sleep(self.sampling_interval)
    
    def _collect_metrics(self):
        """收集性能指标（模拟）"""
        # 实际实现中应该读取硬件计数器
        metrics = {
            'timestamp': time.time(),
            'utilization': np.random.uniform(0.5, 0.95),
            'memory_usage': np.random.randint(1000, 4000),
            'cache_hits': np.random.randint(800, 1000),
            'cache_misses': np.random.randint(0, 200),
            'thermal_state': self.current_metrics['thermal_state'] + np.random.uniform(-1, 1),
            'power_consumption': np.random.uniform(50, 150),
            'task_throughput': np.random.uniform(10, 100),
            'queue_depth': np.random.randint(0, 50)
        }
        
        self.current_metrics.update(metrics)
        return metrics
    
    def get_metrics(self):
        """获取最新指标"""
        return self.current_metrics.copy()
    
    def get_prediction(self, horizon=10):
        """预测未来性能趋势"""
        predictions = {}
        
        for key, history in self.history.items():
            if len(history) > 10:
                # 简单的线性外推
                recent = history[-10:]
                trend = np.polyfit(range(10), recent, 1)[0]
                predictions[key] = recent[-1] + trend * horizon
        
        return predictions

class FeedbackController:
    """反馈控制器"""
    def __init__(self, kp=0.5, ki=0.1, kd=0.2):
        self.kp = kp  # 比例增益
        self.ki = ki  # 积分增益
        self.kd = kd  # 微分增益
        
        self.error_integral = 0
        self.last_error = 0
        
        self.target_metrics = {
            'utilization': 0.85,
            'memory_usage': 3000,
            'thermal_state': 70.0,
            'power_consumption': 100.0
        }
    
    def adjust(self, current_metrics):
        """基于当前指标调整调度参数"""
        adjustments = {}
        
        # PID控制
        for key in ['utilization', 'memory_usage', 'thermal_state', 'power_consumption']:
            if key in current_metrics and key in self.target_metrics:
                error = self.target_metrics[key] - current_metrics[key]
                
                # PID计算
                p_term = self.kp * error
                self.error_integral += error
                i_term = self.ki * self.error_integral
                d_term = self.kd * (error - self.last_error)
                
                adjustment = p_term + i_term + d_term
                adjustments[key] = adjustment
                
                self.last_error = error
        
        # 转换为调度参数
        schedule_params = {
            'priority_boost': adjustments.get('utilization', 0) * 10,
            'memory_threshold': max(1000, min(4000, 3000 + adjustments.get('memory_usage', 0))),
            'thermal_throttle': adjustments.get('thermal_state', 0) < 0,
            'power_cap': max(50, min(200, 100 + adjustments.get('power_consumption', 0)))
        }
        
        return schedule_params

class WorkStealingScheduler:
    """工作窃取调度器"""
    def __init__(self, num_cores=4):
        self.num_cores = num_cores
        self.task_queues = [deque() for _ in range(num_cores)]
        self.core_loads = [0] * num_cores
        self.stealing_enabled = True
        self.load_threshold = 0.3  # 负载差异阈值
    
    def assign_task(self, task, task_info):
        """分配任务到核心"""
        # 找到负载最低的核心
        min_load_core = np.argmin(self.core_loads)
        
        self.task_queues[min_load_core].append((task, task_info))
        self.core_loads[min_load_core] += task_info.get('cycles', 1)
        
        return min_load_core
    
    def steal_work(self, thief_core):
        """工作窃取"""
        if not self.stealing_enabled:
            return None
        
        # 找到负载最高的核心
        victim_core = np.argmax(self.core_loads)
        
        # 检查是否值得窃取
        load_diff = self.core_loads[victim_core] - self.core_loads[thief_core]
        if load_diff < self.load_threshold * self.core_loads[victim_core]:
            return None
        
        # 从victim队列尾部窃取任务
        victim_queue = self.task_queues[victim_core]
        if len(victim_queue) > 1:
            stolen_task = victim_queue.pop()
            task_cycles = stolen_task[1].get('cycles', 1)
            
            # 更新负载
            self.core_loads[victim_core] -= task_cycles
            self.core_loads[thief_core] += task_cycles
            
            return stolen_task
        
        return None
    
    def balance_load(self):
        """全局负载均衡"""
        avg_load = sum(self.core_loads) / self.num_cores
        
        for i in range(self.num_cores):
            if self.core_loads[i] < avg_load * 0.5:
                # 该核心负载过低，尝试窃取
                stolen = self.steal_work(i)
                if stolen:
                    self.task_queues[i].append(stolen)

# ==================== 多核扩展模块 ====================
class MultiCoreNPUScheduler:
    """多核NPU调度器"""
    def __init__(self, num_cores=4, enable_numa=True):
        self.num_cores = num_cores
        self.enable_numa = enable_numa
        
        # 核心分配
        self.core_assignments = {}
        self.core_queues = [deque() for _ in range(num_cores)]
        
        # NUMA拓扑（简化模型）
        self.numa_nodes = 2  # 假设2个NUMA节点
        self.core_to_numa = [i // (num_cores // self.numa_nodes) for i in range(num_cores)]
        
        # 通信成本矩阵
        self.comm_cost = self._build_comm_cost_matrix()
        
        # 工作窃取
        self.work_stealer = WorkStealingScheduler(num_cores)
    
    def _build_comm_cost_matrix(self):
        """构建通信成本矩阵"""
        matrix = np.ones((self.num_cores, self.num_cores))
        
        for i in range(self.num_cores):
            for j in range(self.num_cores):
                if i == j:
                    matrix[i][j] = 0
                elif self.core_to_numa[i] == self.core_to_numa[j]:
                    matrix[i][j] = 1  # 同NUMA节点
                else:
                    matrix[i][j] = 3  # 跨NUMA节点
        
        return matrix
    
    def partition_dag(self, dag_nodes, dag_edges):
        """DAG分区算法"""
        # 简化的METIS风格分区
        num_nodes = len(dag_nodes)
        partition = {}
        
        # 计算每个节点的权重（计算量）
        node_weights = {}
        for node_id, node in dag_nodes.items():
            weight = node.get('cycles', 1)
            if node['op'] in ['MATMUL', 'CONV2D']:
                weight *= 10  # 重计算操作
            node_weights[node_id] = weight
        
        # 贪心分区
        sorted_nodes = sorted(node_weights.items(), key=lambda x: x[1], reverse=True)
        core_loads = [0] * self.num_cores
        
        for node_id, weight in sorted_nodes:
            # 考虑通信成本
            min_cost = float('inf')
            best_core = 0
            
            for core in range(self.num_cores):
                cost = core_loads[core] + weight
                
                # 加入通信成本
                for pred_id in self._get_predecessors(node_id, dag_edges):
                    if pred_id in partition:
                        pred_core = partition[pred_id]
                        cost += self.comm_cost[pred_core][core] * 10
                
                if cost < min_cost:
                    min_cost = cost
                    best_core = core
            
            partition[node_id] = best_core
            core_loads[best_core] += weight
        
        return partition
    
    def _get_predecessors(self, node_id, edges):
        """获取前驱节点"""
        preds = []
        for start, end in edges:
            if end == node_id:
                preds.append(start)
        return preds
    
    def schedule_with_numa(self, tasks, data_locations):
        """NUMA感知调度"""
        schedule = {}
        
        for task_id, task in tasks.items():
            # 确定数据所在NUMA节点
            data_numa = self._find_data_numa_node(task, data_locations)
            
            # 选择同一NUMA节点的核心
            candidate_cores = [i for i in range(self.num_cores) 
                             if self.core_to_numa[i] == data_numa]
            
            # 选择负载最低的核心
            if candidate_cores:
                core_loads = [(c, len(self.core_queues[c])) for c in candidate_cores]
                best_core = min(core_loads, key=lambda x: x[1])[0]
            else:
                best_core = 0
            
            schedule[task_id] = best_core
            self.core_queues[best_core].append(task_id)
        
        return schedule
    
    def _find_data_numa_node(self, task, data_locations):
        """查找数据所在NUMA节点"""
        # 简化实现
        if task.get('buf_id') in data_locations:
            return data_locations[task['buf_id']] % self.numa_nodes
        return 0

# ==================== 预取优化模块 ====================
class PrefetchPredictor:
    """数据预取预测器"""
    def __init__(self, cache_line_size=64, prefetch_distance=4):
        self.cache_line_size = cache_line_size
        self.prefetch_distance = prefetch_distance
        
        # 步进检测器
        self.stride_detector = StrideDetector()
        
        # 访问历史
        self.access_history = deque(maxlen=1000)
        
        # 预取队列
        self.prefetch_queue = []
        
        # ML预测模型（简化）
        self.ml_predictor = self._build_ml_predictor()
    
    def _build_ml_predictor(self):
        """构建ML预测模型"""
        return nn.Sequential(
            nn.Linear(16, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 8),
            nn.Sigmoid()
        )
    
    def predict_access(self, current_addr, context):
        """预测下一个访问地址"""
        predictions = []
        
        # 步进预测
        stride = self.stride_detector.detect_stride(current_addr)
        if stride != 0:
            for i in range(1, self.prefetch_distance + 1):
                predictions.append(current_addr + stride * i)
        
        # 基于历史的预测
        if len(self.access_history) > 10:
            # 寻找模式
            pattern = self._find_pattern(current_addr)
            if pattern:
                predictions.extend(pattern)
        
        # ML预测（如果有足够数据）
        if len(self.access_history) > 100:
            ml_preds = self._ml_predict(current_addr, context)
            predictions.extend(ml_preds)
        
        # 记录访问
        self.access_history.append(current_addr)
        
        return predictions[:self.prefetch_distance]
    
    def _find_pattern(self, addr):
        """查找访问模式"""
        # 简化的模式匹配
        recent = list(self.access_history)[-20:]
        
        for i in range(len(recent) - 1):
            if recent[i] == addr:
                # 找到匹配，返回后续地址
                return recent[i+1:min(i+1+self.prefetch_distance, len(recent))]
        
        return None
    
    def _ml_predict(self, addr, context):
        """使用ML模型预测"""
        # 构建特征
        features = self._extract_features(addr, context)
        features_tensor = torch.FloatTensor(features).unsqueeze(0)
        
        # 预测
        with torch.no_grad():
            predictions = self.ml_predictor(features_tensor)
        
        # 转换为地址
        pred_addrs = []
        base_addr = addr & ~(self.cache_line_size - 1)
        for i, prob in enumerate(predictions[0]):
            if prob > 0.5:
                pred_addrs.append(base_addr + i * self.cache_line_size)
        
        return pred_addrs
    
    def _extract_features(self, addr, context):
        """提取特征"""
        features = []
        
        # 地址特征
        features.append((addr >> 6) & 0xFF)  # 页内偏移
        features.append((addr >> 14) & 0xFF) # 页号
        
        # 历史特征
        recent_addrs = list(self.access_history)[-10:]
        for i in range(10):
            if i < len(recent_addrs):
                features.append((recent_addrs[i] >> 6) & 0xFF)
            else:
                features.append(0)
        
        # 上下文特征
        features.extend(context.get('node_features', [0, 0, 0, 0])[:4])
        
        return features

class StrideDetector:
    """步进检测器"""
    def __init__(self, history_size=8):
        self.history = deque(maxlen=history_size)
        self.strides = deque(maxlen=history_size-1)
        self.confidence = 0
    
    def detect_stride(self, addr):
        """检测步进模式"""
        if len(self.history) > 0:
            stride = addr - self.history[-1]
            self.strides.append(stride)
            
            # 检查步进是否稳定
            if len(self.strides) >= 3:
                recent_strides = list(self.strides)[-3:]
                if all(s == recent_strides[0] for s in recent_strides):
                    self.confidence = min(1.0, self.confidence + 0.1)
                    self.history.append(addr)
                    return recent_strides[0]
                else:
                    self.confidence = max(0, self.confidence - 0.1)
        
        self.history.append(addr)
        return 0

class DataPrefetcher:
    """数据预取器"""
    def __init__(self, num_channels=4):
        self.num_channels = num_channels
        self.channels = [deque() for _ in range(num_channels)]
        self.predictor = PrefetchPredictor()
        
        # DMA模拟
        self.dma_busy = [False] * num_channels
        self.prefetch_buffer = {}
    
    def schedule_prefetch(self, task_schedule, dag_nodes):
        """调度预取操作"""
        prefetch_schedule = []
        
        for idx, task_id in enumerate(task_schedule):
            task = dag_nodes[task_id]
            
            # 预测数据访问
            if 'bufs' in task:
                for buf_id in task['bufs']:
                    # 预测访问地址
                    predicted_addrs = self.predictor.predict_access(
                        buf_id * 1000,  # 模拟地址
                        {'node_features': [task.get('cycles', 0), len(task['bufs'])]}
                    )
                    
                    # 调度预取
                    for addr in predicted_addrs:
                        channel = self._select_channel(addr)
                        prefetch_time = max(0, idx - self.predictor.prefetch_distance)
                        prefetch_schedule.append({
                            'time': prefetch_time,
                            'addr': addr,
                            'channel': channel,
                            'task_id': task_id
                        })
        
        return prefetch_schedule
    
    def _select_channel(self, addr):
        """选择DMA通道"""
        # 基于地址的哈希选择
        return addr % self.num_channels
    
    def insert_prefetch_nodes(self, schedule, prefetch_ops):
        """插入预取节点"""
        if not prefetch_ops:
            return schedule
        
        # 按时间排序预取操作
        prefetch_ops.sort(key=lambda x: x['time'])
        
        new_schedule = []
        prefetch_idx = 0
        
        for i, task_id in enumerate(schedule):
            # 插入该时间点的预取操作
            while prefetch_idx < len(prefetch_ops) and prefetch_ops[prefetch_idx]['time'] <= i:
                prefetch_node = {
                    'id': f"prefetch_{prefetch_idx}",
                    'op': 'PREFETCH',
                    'addr': prefetch_ops[prefetch_idx]['addr'],
                    'channel': prefetch_ops[prefetch_idx]['channel']
                }
                new_schedule.append(prefetch_node)
                prefetch_idx += 1
            
            new_schedule.append(task_id)
        
        return new_schedule

# ==================== 能耗优化模块 ====================
class EnergyModel:
    """能耗模型"""
    def __init__(self):
        self.op_energy = ENERGY_MODEL.copy()
        
        # DVFS状态
        self.voltage_levels = [0.6, 0.7, 0.8, 0.9, 1.0]  # V
        self.frequency_levels = [500, 750, 1000, 1250, 1500]  # MHz
        self.current_dvfs_level = 2  # 默认中间档位
    
    def calculate_energy(self, task, dvfs_level=None):
        """计算任务能耗"""
        if dvfs_level is None:
            dvfs_level = self.current_dvfs_level
        
        voltage = self.voltage_levels[dvfs_level]
        frequency = self.frequency_levels[dvfs_level]
        
        # 动态能耗: P = C * V^2 * f
        base_energy = self.op_energy.get(task['op'], 1.0)
        dynamic_energy = base_energy * (voltage ** 2) * (frequency / 1000.0)
        
        # 静态能耗
        static_energy = task.get('cycles', 1) * 0.01
        
        return dynamic_energy + static_energy
    
    def predict_thermal(self, power_history, ambient_temp=25):
        """预测温度"""
        # 简化的热模型
        if not power_history:
            return ambient_temp
        
        avg_power = np.mean(power_history[-10:]) if len(power_history) > 10 else np.mean(power_history)
        thermal_resistance = 0.5  # K/W
        temperature = ambient_temp + avg_power * thermal_resistance
        
        return temperature
    
    def select_dvfs_level(self, workload, thermal_state, performance_target):
        """选择DVFS级别"""
        # 基于负载、温度和性能目标选择
        if thermal_state > 80:
            # 温度过高，降频
            return max(0, self.current_dvfs_level - 1)
        elif workload > 0.9 and performance_target > 0.8:
            # 高负载且需要高性能
            return min(4, self.current_dvfs_level + 1)
        elif workload < 0.3:
            # 低负载，降频省电
            return max(0, self.current_dvfs_level - 1)
        else:
            return self.current_dvfs_level

if HAS_PYMOO:
    class NPUSchedulingProblem(Problem):
        """多目标优化问题定义"""
        def __init__(self, scheduler, dag_nodes, dag_edges):
            self.scheduler = scheduler
            self.dag_nodes = dag_nodes
            self.dag_edges = dag_edges
            
            n_tasks = len(dag_nodes)
            super().__init__(
                n_var=n_tasks,
                n_obj=3,  # 时间、内存、能耗
                n_constr=1,
                xl=0,
                xu=n_tasks-1,
                vtype=int
            )
        
        def _evaluate(self, x, out, *args, **kwargs):
            """评估函数"""
            n_pop = x.shape[0]
            objectives = np.zeros((n_pop, 3))
            constraints = np.zeros((n_pop, 1))
            
            for i in range(n_pop):
                schedule = x[i].tolist()
                
                # 检查合法性
                if len(set(schedule)) != len(schedule):
                    # 有重复，惩罚
                    objectives[i] = [1e6, 1e6, 1e6]
                    constraints[i] = 1e6
                else:
                    # 计算目标
                    makespan = self._calculate_makespan(schedule)
                    max_cache = self._calculate_max_cache(schedule)
                    energy = self._calculate_energy(schedule)
                    
                    objectives[i, 0] = makespan
                    objectives[i, 1] = max_cache
                    objectives[i, 2] = energy
                    
                    # 约束：温度限制
                    thermal = self._calculate_thermal(schedule)
                    constraints[i] = max(0, thermal - 85)  # 最高85度
            
            out["F"] = objectives
            out["G"] = constraints
        
        def _calculate_makespan(self, schedule):
            """计算执行时间"""
            finish_time = 0
            for task_id in schedule:
                task = self.dag_nodes[task_id]
                finish_time += task.get('cycles', 1)
            return finish_time
        
        def _calculate_max_cache(self, schedule):
            """计算最大缓存"""
            current_cache = 0
            max_cache = 0
            
            for task_id in schedule:
                task = self.dag_nodes[task_id]
                if task['op'] == 'ALLOC':
                    current_cache += task.get('size', 0)
                elif task['op'] == 'FREE':
                    current_cache -= task.get('size', 0)
                max_cache = max(max_cache, current_cache)
            
            return max_cache
        
        def _calculate_energy(self, schedule):
            """计算能耗"""
            energy_model = EnergyModel()
            total_energy = 0
            
            for task_id in schedule:
                task = self.dag_nodes[task_id]
                total_energy += energy_model.calculate_energy(task)
            
            return total_energy
        
        def _calculate_thermal(self, schedule):
            """计算温度"""
            energy_model = EnergyModel()
            power_history = []
            
            for task_id in schedule:
                task = self.dag_nodes[task_id]
                power = energy_model.calculate_energy(task) / max(task.get('cycles', 1), 1)
                power_history.append(power)
            
            return energy_model.predict_thermal(power_history)

# ==================== 主调度器类 ====================
class EnhancedNPUScheduler:
    """增强版NPU调度器"""
    def __init__(self, json_file_path: str, num_cores: int = 4):
        self.json_file_path = Path(json_file_path)
        self.task_name = self.json_file_path.stem
        self.num_cores = num_cores
        
        # 加载数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 构建图结构
        self.build_graph()
        
        # 初始化各个组件
        self.rl_agent = RLSchedulingAgent()
        self.monitor = NPUPerformanceMonitor()
        self.controller = FeedbackController()
        self.multi_core_scheduler = MultiCoreNPUScheduler(num_cores)
        self.prefetcher = DataPrefetcher()
        self.energy_model = EnergyModel()
        
        # 启动监控
        self.monitor.start()
    
    def build_graph(self):
        """构建DAG图结构"""
        self.nodes = {}
        self.edges = []
        
        for node_data in self.data["Nodes"]:
            node_id = node_data["Id"]
            self.nodes[node_id] = node_data
        
        self.edges = self.data["Edges"]
    
    def solve_with_rl(self):
        """使用强化学习求解"""
        # 构建状态表示
        state = self._build_state_representation()
        
        # 获取动作（调度决策）
        schedule = []
        scheduled = set()
        
        while len(scheduled) < len(self.nodes):
            # 获取可调度节点
            ready_nodes = self._get_ready_nodes(scheduled)
            
            if ready_nodes:
                # RL选择
                action, log_prob = self.rl_agent.get_action(state)
                selected_node = ready_nodes[action % len(ready_nodes)]
                
                schedule.append(selected_node)
                scheduled.add(selected_node)
                
                # 更新状态
                state = self._update_state(state, selected_node)
        
        return schedule
    
    def solve_with_dynamic_adaptation(self):
        """动态自适应求解"""
        # 初始调度
        initial_schedule = self._generate_initial_schedule()
        
        # 动态调整
        adapted_schedule = []
        
        for task_id in initial_schedule:
            # 获取当前性能指标
            current_metrics = self.monitor.get_metrics()
            
            # 反馈控制调整
            params = self.controller.adjust(current_metrics)
            
            # 根据参数调整任务
            if params['thermal_throttle']:
                # 降低频率
                self.energy_model.current_dvfs_level = max(0, self.energy_model.current_dvfs_level - 1)
            
            # 预测性调整
            predictions = self.monitor.get_prediction()
            if predictions.get('memory_usage', 0) > params['memory_threshold']:
                # 插入SPILL节点
                adapted_schedule.append({'id': f'spill_{task_id}', 'op': 'SPILL'})
            
            adapted_schedule.append(task_id)
            
            # 工作窃取均衡
            if len(adapted_schedule) % 10 == 0:
                self.multi_core_scheduler.work_stealer.balance_load()
        
        return adapted_schedule
    
    def solve_multi_core(self):
        """多核求解"""
        # DAG分区
        partition = self.multi_core_scheduler.partition_dag(self.nodes, self.edges)
        
        # 每个核心独立调度
        core_schedules = [[] for _ in range(self.num_cores)]
        
        for node_id, core_id in partition.items():
            core_schedules[core_id].append(node_id)
        
        # 合并调度（考虑依赖）
        global_schedule = self._merge_core_schedules(core_schedules)
        
        return global_schedule
    
    def solve_with_prefetch(self):
        """带预取优化的求解"""
        # 基础调度
        base_schedule = self._generate_initial_schedule()
        
        # 生成预取调度
        prefetch_ops = self.prefetcher.schedule_prefetch(base_schedule, self.nodes)
        
        # 插入预取节点
        optimized_schedule = self.prefetcher.insert_prefetch_nodes(base_schedule, prefetch_ops)
        
        return optimized_schedule
    
    def solve_multi_objective(self):
        """多目标优化求解"""
        if not HAS_PYMOO:
            logger.warning("Pymoo not installed, falling back to single objective")
            return self._generate_initial_schedule()
        
        # 定义问题
        problem = NPUSchedulingProblem(self, self.nodes, self.edges)
        
        # 配置算法
        algorithm = NSGA2(
            pop_size=100,
            n_offsprings=20,
            eliminate_duplicates=True
        )
        
        # 求解
        res = minimize(
            problem,
            algorithm,
            termination=('n_gen', 50),
            seed=42,
            verbose=False
        )
        
        # 选择最优解（可以根据偏好选择）
        if res.X is not None:
            # 选择makespan最小的解
            best_idx = np.argmin(res.F[:, 0])
            best_schedule = res.X[best_idx].tolist()
            
            # 修正调度顺序
            return self._fix_schedule_order(best_schedule)
        
        return self._generate_initial_schedule()
    
    def solve_hybrid(self):
        """混合策略求解"""
        strategies = {
            'rl': self.solve_with_rl(),
            'dynamic': self.solve_with_dynamic_adaptation(),
            'multi_core': self.solve_multi_core(),
            'prefetch': self.solve_with_prefetch(),
            'multi_obj': self.solve_multi_objective()
        }
        
        # 评估每种策略
        best_schedule = None
        best_score = float('inf')
        
        for name, schedule in strategies.items():
            score = self._evaluate_schedule(schedule)
            logger.info(f"Strategy {name} score: {score}")
            
            if score < best_score:
                best_score = score
                best_schedule = schedule
        
        return best_schedule
    
    # ==================== 辅助方法 ====================
    def _build_state_representation(self):
        """构建状态表示"""
        # 简化的状态表示
        state = []
        
        # 图特征
        state.append(len(self.nodes))
        state.append(len(self.edges))
        
        # 资源状态
        metrics = self.monitor.get_metrics()
        state.append(metrics['utilization'])
        state.append(metrics['memory_usage'] / 4096.0)
        state.append(metrics['thermal_state'] / 100.0)
        
        # 填充到固定维度
        while len(state) < 32:
            state.append(0)
        
        return np.array(state[:32])
    
    def _update_state(self, state, selected_node):
        """更新状态"""
        # 简单更新
        new_state = state.copy()
        new_state[0] -= 1  # 剩余节点数
        return new_state
    
    def _get_ready_nodes(self, scheduled):
        """获取就绪节点"""
        ready = []
        
        for node_id in self.nodes:
            if node_id not in scheduled:
                # 检查依赖
                all_deps_scheduled = True
                for start, end in self.edges:
                    if end == node_id and start not in scheduled:
                        all_deps_scheduled = False
                        break
                
                if all_deps_scheduled:
                    ready.append(node_id)
        
        return ready
    
    def _generate_initial_schedule(self):
        """生成初始调度"""
        # 简单的拓扑排序
        in_degree = defaultdict(int)
        for start, end in self.edges:
            in_degree[end] += 1
        
        queue = deque([n for n in self.nodes if in_degree[n] == 0])
        schedule = []
        
        while queue:
            node = queue.popleft()
            schedule.append(node)
            
            for start, end in self.edges:
                if start == node:
                    in_degree[end] -= 1
                    if in_degree[end] == 0:
                        queue.append(end)
        
        return schedule
    
    def _merge_core_schedules(self, core_schedules):
        """合并多核调度"""
        # 简单的轮询合并
        merged = []
        max_len = max(len(s) for s in core_schedules)
        
        for i in range(max_len):
            for core_id in range(self.num_cores):
                if i < len(core_schedules[core_id]):
                    merged.append(core_schedules[core_id][i])
        
        return merged
    
    def _fix_schedule_order(self, schedule):
        """修正调度顺序以满足依赖"""
        # 确保依赖关系
        fixed = []
        scheduled = set()
        remaining = set(schedule)
        
        while remaining:
            for task_id in list(remaining):
                # 检查依赖
                can_schedule = True
                for start, end in self.edges:
                    if end == task_id and start not in scheduled:
                        can_schedule = False
                        break
                
                if can_schedule:
                    fixed.append(task_id)
                    scheduled.add(task_id)
                    remaining.remove(task_id)
                    break
        
        return fixed
    
    def _evaluate_schedule(self, schedule):
        """评估调度质量"""
        if not schedule:
            return float('inf')
        
        # 计算多个指标
        makespan = 0
        max_cache = 0
        current_cache = 0
        energy = 0
        
        for task_id in schedule:
            if isinstance(task_id, dict):
                continue  # 跳过插入的节点
            
            if task_id not in self.nodes:
                continue
                
            task = self.nodes[task_id]
            
            # 时间
            makespan += task.get('cycles', 1)
            
            # 内存
            if task.get('op') == 'ALLOC':
                current_cache += task.get('size', 0)
            elif task.get('op') == 'FREE':
                current_cache -= task.get('size', 0)
            max_cache = max(max_cache, current_cache)
            
            # 能耗
            energy += self.energy_model.calculate_energy(task)
        
        # 综合评分
        score = makespan / 1000.0 + max_cache / 10000.0 + energy / 100.0
        return score
    
    def cleanup(self):
        """清理资源"""
        self.monitor.stop()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='增强版NPU调度优化器')
    parser.add_argument('--file', type=str, help='输入JSON文件路径')
    parser.add_argument('--strategy', type=str, default='hybrid',
                       choices=['rl', 'dynamic', 'multi_core', 'prefetch', 
                               'multi_obj', 'hybrid'],
                       help='调度策略')
    parser.add_argument('--cores', type=int, default=4, help='NPU核心数')
    parser.add_argument('--output', type=str, default='output', help='输出目录')
    
    args = parser.parse_args()
    
    if args.file:
        scheduler = EnhancedNPUScheduler(args.file, args.cores)
        
        # 根据策略求解
        if args.strategy == 'rl':
            schedule = scheduler.solve_with_rl()
        elif args.strategy == 'dynamic':
            schedule = scheduler.solve_with_dynamic_adaptation()
        elif args.strategy == 'multi_core':
            schedule = scheduler.solve_multi_core()
        elif args.strategy == 'prefetch':
            schedule = scheduler.solve_with_prefetch()
        elif args.strategy == 'multi_obj':
            schedule = scheduler.solve_multi_objective()
        else:
            schedule = scheduler.solve_hybrid()
        
        # 输出结果
        print(f"调度长度: {len(schedule)}")
        print(f"评分: {scheduler._evaluate_schedule(schedule):.4f}")
        
        # 保存结果
        output_path = Path(args.output) / f"{scheduler.task_name}_enhanced.txt"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            for task_id in schedule:
                if isinstance(task_id, dict):
                    f.write(f"{task_id['id']}\n")
                else:
                    f.write(f"{task_id}\n")
        
        print(f"结果已保存到: {output_path}")
        
        # 清理
        scheduler.cleanup()

if __name__ == "__main__":
    main()