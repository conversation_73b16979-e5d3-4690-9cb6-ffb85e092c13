#!/usr/bin/env python
"""
性能测试脚本 - 测试不同规模数据的处理能力
"""

import time
import json
from pathlib import Path
from optimized_npu_scheduler import OptimizedNPUScheduler, SchedulingStrategy

def test_file_performance(json_file_path: str, max_time: int = 300):
    """测试单个文件的性能"""
    print(f"\n测试文件: {json_file_path}")
    
    try:
        # 加载数据并检查规模
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        node_count = len(data["Nodes"])
        edge_count = len(data["Edges"])
        
        print(f"  节点数: {node_count}")
        print(f"  边数: {edge_count}")
        
        # 如果数据规模过大，跳过
        if node_count > 1000:
            print(f"  跳过: 数据规模过大 (节点数 > 1000)")
            return None
        
        # 创建调度器并测试
        start_time = time.time()
        scheduler = OptimizedNPUScheduler(json_file_path)
        init_time = time.time() - start_time
        
        print(f"  初始化时间: {init_time:.2f}秒")
        
        if init_time > max_time:
            print(f"  跳过: 初始化时间过长")
            return None
        
        # 测试问题1
        start_time = time.time()
        result1 = scheduler.solve_problem1(SchedulingStrategy.GREEDY)  # 使用更快的贪心策略
        problem1_time = time.time() - start_time
        
        print(f"  问题1求解时间: {problem1_time:.2f}秒")
        print(f"  最大缓存: {result1.max_cache} bytes")
        
        if problem1_time > max_time:
            print(f"  跳过后续问题: 问题1耗时过长")
            return {
                "file": Path(json_file_path).stem,
                "node_count": node_count,
                "edge_count": edge_count,
                "init_time": init_time,
                "problem1_time": problem1_time,
                "max_cache": result1.max_cache,
                "status": "problem1_only"
            }
        
        # 测试问题2
        start_time = time.time()
        result2 = scheduler.solve_problem2(result1.schedule)
        problem2_time = time.time() - start_time
        
        print(f"  问题2求解时间: {problem2_time:.2f}秒")
        print(f"  SPILL数据量: {result2.total_spill_data} bytes")
        
        # 测试问题3
        start_time = time.time()
        result3 = scheduler.solve_problem3(result2.schedule, result2.allocations)
        problem3_time = time.time() - start_time
        
        print(f"  问题3求解时间: {problem3_time:.2f}秒")
        print(f"  Makespan: {result3.makespan} cycles")
        
        total_time = init_time + problem1_time + problem2_time + problem3_time
        print(f"  总时间: {total_time:.2f}秒")
        
        return {
            "file": Path(json_file_path).stem,
            "node_count": node_count,
            "edge_count": edge_count,
            "init_time": init_time,
            "problem1_time": problem1_time,
            "problem2_time": problem2_time,
            "problem3_time": problem3_time,
            "total_time": total_time,
            "max_cache": result1.max_cache,
            "spill_data": result2.total_spill_data,
            "makespan": result3.makespan,
            "status": "complete"
        }
        
    except Exception as e:
        print(f"  错误: {e}")
        return {
            "file": Path(json_file_path).stem,
            "status": "error",
            "error": str(e)
        }

def main():
    """主函数"""
    print("="*60)
    print("NPU调度器性能测试")
    print("="*60)
    
    data_dir = Path("data")
    results = []
    
    # 测试所有JSON文件
    json_files = list(data_dir.glob("*.json"))
    
    for json_file in json_files:
        result = test_file_performance(str(json_file))
        if result:
            results.append(result)
    
    # 输出汇总结果
    print("\n" + "="*60)
    print("性能测试汇总")
    print("="*60)
    
    successful_tests = [r for r in results if r.get("status") in ["complete", "problem1_only"]]
    failed_tests = [r for r in results if r.get("status") == "error"]
    
    print(f"成功测试: {len(successful_tests)}")
    print(f"失败测试: {len(failed_tests)}")
    
    if successful_tests:
        print("\n成功案例:")
        for result in successful_tests:
            print(f"  {result['file']}: {result['node_count']}节点, "
                  f"{result.get('total_time', result.get('problem1_time', 0)):.2f}秒")
    
    if failed_tests:
        print("\n失败案例:")
        for result in failed_tests:
            print(f"  {result['file']}: {result['error']}")
    
    # 保存结果
    output_file = Path("performance_test_results.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n详细结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
