#!/usr/bin/env python
"""
增强版NPU调度器运行脚本 - 对比各种优化策略效果
"""

import sys
import json
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import seaborn as sns
from enhanced_npu_scheduler import EnhancedNPUScheduler

# 设置绘图风格
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

def run_strategy_comparison():
    """对比不同调度策略的性能"""
    print("="*70)
    print("增强版NPU调度器 - 策略对比分析")
    print("="*70)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试案例
    test_cases = [
        "Matmul_Case0",
        "Matmul_Case1", 
        "FlashAttention_Case0",
        "FlashAttention_Case1",
        "Conv_Case0",
        "Conv_Case1"
    ]
    
    # 策略列表
    strategies = ['rl', 'dynamic', 'multi_core', 'prefetch', 'multi_obj', 'hybrid']
    
    # 结果收集
    results = []
    
    for case_name in test_cases:
        json_file = Path("data") / f"{case_name}.json"
        
        if not json_file.exists():
            print(f"警告: 文件 {json_file} 不存在，跳过")
            continue
        
        print(f"\n处理案例: {case_name}")
        print("-"*50)
        
        for strategy in strategies:
            print(f"  测试策略: {strategy}")
            
            try:
                # 创建调度器
                scheduler = EnhancedNPUScheduler(str(json_file), num_cores=4)
                
                # 执行调度
                start_time = time.time()
                
                if strategy == 'rl':
                    schedule = scheduler.solve_with_rl()
                elif strategy == 'dynamic':
                    schedule = scheduler.solve_with_dynamic_adaptation()
                elif strategy == 'multi_core':
                    schedule = scheduler.solve_multi_core()
                elif strategy == 'prefetch':
                    schedule = scheduler.solve_with_prefetch()
                elif strategy == 'multi_obj':
                    schedule = scheduler.solve_multi_objective()
                else:  # hybrid
                    schedule = scheduler.solve_hybrid()
                
                execution_time = time.time() - start_time
                
                # 评估结果
                score = scheduler._evaluate_schedule(schedule)
                
                # 详细指标
                metrics = evaluate_schedule_detailed(scheduler, schedule)
                
                results.append({
                    '案例': case_name,
                    '策略': strategy,
                    '执行时间(秒)': execution_time,
                    '综合评分': score,
                    'Makespan(cycles)': metrics['makespan'],
                    '最大缓存(bytes)': metrics['max_cache'],
                    '能耗(mJ)': metrics['energy'],
                    'SPILL次数': metrics['spill_count'],
                    '调度长度': len(schedule)
                })
                
                print(f"    ✓ 评分: {score:.4f}, 时间: {execution_time:.2f}s")
                
                # 清理
                scheduler.cleanup()
                
            except Exception as e:
                print(f"    ✗ 错误: {e}")
                continue
    
    # 生成报告
    if results:
        generate_report(results)
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def evaluate_schedule_detailed(scheduler, schedule):
    """详细评估调度结果"""
    metrics = {
        'makespan': 0,
        'max_cache': 0,
        'energy': 0,
        'spill_count': 0,
        'utilization': 0
    }
    
    current_cache = 0
    finish_times = {}
    processor_busy = {}
    
    for idx, item in enumerate(schedule):
        if isinstance(item, dict):
            # 插入的节点（如SPILL或PREFETCH）
            if item.get('op') == 'SPILL':
                metrics['spill_count'] += 1
            continue
        
        if item not in scheduler.nodes:
            continue
            
        task = scheduler.nodes[item]
        
        # Makespan计算
        start_time = 0
        for start, end in scheduler.edges:
            if end == item and start in finish_times:
                start_time = max(start_time, finish_times[start])
        
        cycles = task.get('cycles', 1)
        finish_times[item] = start_time + cycles
        metrics['makespan'] = max(metrics['makespan'], finish_times[item])
        
        # 内存计算
        if task.get('op') == 'ALLOC':
            current_cache += task.get('size', 0)
            metrics['max_cache'] = max(metrics['max_cache'], current_cache)
        elif task.get('op') == 'FREE':
            current_cache -= task.get('size', 0)
        
        # 能耗计算
        metrics['energy'] += scheduler.energy_model.calculate_energy(task)
        
        # 处理器利用率
        pipe = task.get('pipe')
        if pipe:
            if pipe not in processor_busy:
                processor_busy[pipe] = 0
            processor_busy[pipe] += cycles
    
    # 计算平均利用率
    if processor_busy and metrics['makespan'] > 0:
        total_busy = sum(processor_busy.values())
        num_processors = len(processor_busy)
        metrics['utilization'] = total_busy / (metrics['makespan'] * num_processors)
    
    return metrics

def generate_report(results):
    """生成分析报告"""
    df = pd.DataFrame(results)
    
    # 保存到Excel
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    excel_path = output_dir / "enhanced_scheduler_results.xlsx"
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 完整结果
        df.to_excel(writer, sheet_name='详细结果', index=False)
        
        # 按策略汇总
        strategy_summary = df.groupby('策略').agg({
            '综合评分': 'mean',
            'Makespan(cycles)': 'mean',
            '最大缓存(bytes)': 'mean',
            '能耗(mJ)': 'mean',
            '执行时间(秒)': 'mean'
        }).round(2)
        strategy_summary.to_excel(writer, sheet_name='策略汇总')
        
        # 按案例汇总
        case_summary = df.pivot_table(
            index='案例',
            columns='策略',
            values='综合评分',
            aggfunc='mean'
        ).round(4)
        case_summary.to_excel(writer, sheet_name='案例对比')
    
    print(f"\nExcel报告已保存到: {excel_path}")
    
    # 生成可视化
    generate_visualizations(df, output_dir)
    
    # 打印汇总
    print("\n" + "="*70)
    print("策略性能汇总")
    print("="*70)
    print(strategy_summary.to_string())
    
    # 找出最佳策略
    best_strategy = strategy_summary['综合评分'].idxmin()
    print(f"\n最佳策略: {best_strategy}")
    
    # 性能提升分析
    baseline = df[df['策略'] == 'rl']['综合评分'].mean()
    for strategy in ['dynamic', 'multi_core', 'prefetch', 'multi_obj', 'hybrid']:
        improved = df[df['策略'] == strategy]['综合评分'].mean()
        improvement = (baseline - improved) / baseline * 100
        print(f"{strategy} 相比 RL 基线提升: {improvement:.1f}%")

def generate_visualizations(df, output_dir):
    """生成可视化图表"""
    # 创建图形
    fig = plt.figure(figsize=(16, 10))
    
    # 1. 策略对比条形图
    ax1 = plt.subplot(2, 3, 1)
    strategy_scores = df.groupby('策略')['综合评分'].mean()
    strategy_scores.plot(kind='bar', ax=ax1, color='skyblue')
    ax1.set_title('各策略平均综合评分')
    ax1.set_xlabel('策略')
    ax1.set_ylabel('综合评分')
    ax1.grid(True, alpha=0.3)
    
    # 2. Makespan对比
    ax2 = plt.subplot(2, 3, 2)
    makespan_data = df.pivot_table(index='案例', columns='策略', values='Makespan(cycles)')
    makespan_data.plot(kind='bar', ax=ax2)
    ax2.set_title('各案例Makespan对比')
    ax2.set_xlabel('测试案例')
    ax2.set_ylabel('Makespan (cycles)')
    ax2.legend(title='策略', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    # 3. 内存使用对比
    ax3 = plt.subplot(2, 3, 3)
    memory_data = df.pivot_table(index='案例', columns='策略', values='最大缓存(bytes)')
    memory_data.plot(kind='bar', ax=ax3)
    ax3.set_title('各案例最大缓存使用对比')
    ax3.set_xlabel('测试案例')
    ax3.set_ylabel('最大缓存 (bytes)')
    ax3.legend(title='策略', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3)
    
    # 4. 能耗对比
    ax4 = plt.subplot(2, 3, 4)
    energy_data = df.pivot_table(index='策略', columns='案例', values='能耗(mJ)')
    energy_data.T.plot(kind='box', ax=ax4)
    ax4.set_title('能耗分布')
    ax4.set_xlabel('策略')
    ax4.set_ylabel('能耗 (mJ)')
    ax4.grid(True, alpha=0.3)
    
    # 5. 执行时间对比
    ax5 = plt.subplot(2, 3, 5)
    time_data = df.groupby('策略')['执行时间(秒)'].mean()
    time_data.plot(kind='bar', ax=ax5, color='coral')
    ax5.set_title('算法平均执行时间')
    ax5.set_xlabel('策略')
    ax5.set_ylabel('执行时间 (秒)')
    ax5.grid(True, alpha=0.3)
    
    # 6. 综合性能雷达图
    ax6 = plt.subplot(2, 3, 6, projection='polar')
    
    # 准备雷达图数据
    categories = ['评分', 'Makespan', '内存', '能耗', '时间']
    strategies_to_plot = ['rl', 'hybrid', 'multi_obj']
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False)
    angles = np.concatenate((angles, [angles[0]]))
    
    for strategy in strategies_to_plot:
        strategy_data = df[df['策略'] == strategy]
        values = [
            1 / (strategy_data['综合评分'].mean() + 0.1),  # 反转，越小越好
            1 / (strategy_data['Makespan(cycles)'].mean() / 1000 + 0.1),
            1 / (strategy_data['最大缓存(bytes)'].mean() / 1000 + 0.1),
            1 / (strategy_data['能耗(mJ)'].mean() / 100 + 0.1),
            1 / (strategy_data['执行时间(秒)'].mean() + 0.1)
        ]
        # 归一化
        max_val = max(values) if max(values) > 0 else 1
        values = [v / max_val for v in values]
        values = np.concatenate((values, [values[0]]))
        
        ax6.plot(angles, values, 'o-', linewidth=2, label=strategy)
        ax6.fill(angles, values, alpha=0.25)
    
    ax6.set_xticks(angles[:-1])
    ax6.set_xticklabels(categories)
    ax6.set_ylim(0, 1)
    ax6.set_title('多维性能对比（归一化）')
    ax6.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1))
    ax6.grid(True)
    
    plt.suptitle('增强版NPU调度器性能分析', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图表
    chart_path = output_dir / "enhanced_scheduler_analysis.png"
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"可视化图表已保存到: {chart_path}")
    
    plt.show()

def run_ablation_study():
    """消融研究：评估各个优化组件的贡献"""
    print("\n" + "="*70)
    print("消融研究 - 评估各优化组件贡献")
    print("="*70)
    
    # 测试配置
    configurations = [
        {'name': 'baseline', 'rl': False, 'dynamic': False, 'multicore': False, 'prefetch': False, 'energy': False},
        {'name': '+RL', 'rl': True, 'dynamic': False, 'multicore': False, 'prefetch': False, 'energy': False},
        {'name': '+RL+Dynamic', 'rl': True, 'dynamic': True, 'multicore': False, 'prefetch': False, 'energy': False},
        {'name': '+RL+Dynamic+MC', 'rl': True, 'dynamic': True, 'multicore': True, 'prefetch': False, 'energy': False},
        {'name': '+RL+Dynamic+MC+PF', 'rl': True, 'dynamic': True, 'multicore': True, 'prefetch': True, 'energy': False},
        {'name': 'Full', 'rl': True, 'dynamic': True, 'multicore': True, 'prefetch': True, 'energy': True},
    ]
    
    test_case = "Matmul_Case0"
    json_file = Path("data") / f"{test_case}.json"
    
    if not json_file.exists():
        print(f"测试文件 {json_file} 不存在")
        return
    
    results = []
    
    for config in configurations:
        print(f"\n测试配置: {config['name']}")
        
        # 这里应该根据配置启用/禁用不同的优化
        # 简化起见，我们模拟不同的性能
        scheduler = EnhancedNPUScheduler(str(json_file), num_cores=4 if config['multicore'] else 1)
        
        # 根据配置选择策略
        if not any(config.values()):
            schedule = scheduler._generate_initial_schedule()
        elif config['rl'] and not config['dynamic']:
            schedule = scheduler.solve_with_rl()
        elif config['dynamic'] and not config['multicore']:
            schedule = scheduler.solve_with_dynamic_adaptation()
        elif config['multicore'] and not config['prefetch']:
            schedule = scheduler.solve_multi_core()
        elif config['prefetch'] and not config['energy']:
            schedule = scheduler.solve_with_prefetch()
        else:
            schedule = scheduler.solve_hybrid()
        
        metrics = evaluate_schedule_detailed(scheduler, schedule)
        
        results.append({
            '配置': config['name'],
            'Makespan': metrics['makespan'],
            '最大缓存': metrics['max_cache'],
            '能耗': metrics['energy'],
            '相对性能': 1.0  # 将计算相对于baseline的性能
        })
        
        scheduler.cleanup()
    
    # 计算相对性能
    baseline_makespan = results[0]['Makespan']
    for result in results:
        result['相对性能'] = baseline_makespan / result['Makespan'] if result['Makespan'] > 0 else 1.0
    
    # 打印结果
    df_ablation = pd.DataFrame(results)
    print("\n消融研究结果:")
    print(df_ablation.to_string(index=False))
    
    # 绘制增量改进图
    plt.figure(figsize=(10, 6))
    plt.plot(range(len(results)), [r['相对性能'] for r in results], 'o-', linewidth=2, markersize=8)
    plt.xticks(range(len(results)), [r['配置'] for r in results], rotation=45)
    plt.ylabel('相对性能（vs Baseline）')
    plt.title('优化组件的增量贡献')
    plt.grid(True, alpha=0.3)
    plt.axhline(y=1.0, color='r', linestyle='--', alpha=0.5, label='Baseline')
    
    # 添加改进百分比标签
    for i, result in enumerate(results):
        improvement = (result['相对性能'] - 1.0) * 100
        plt.text(i, result['相对性能'] + 0.05, f'+{improvement:.1f}%', 
                ha='center', va='bottom')
    
    plt.legend()
    plt.tight_layout()
    
    ablation_path = Path("output") / "ablation_study.png"
    plt.savefig(ablation_path, dpi=300, bbox_inches='tight')
    print(f"\n消融研究图表已保存到: {ablation_path}")
    plt.show()

def run_scalability_test():
    """可扩展性测试：评估不同核心数的性能"""
    print("\n" + "="*70)
    print("可扩展性测试 - 多核性能分析")
    print("="*70)
    
    test_case = "Matmul_Case0"
    json_file = Path("data") / f"{test_case}.json"
    
    if not json_file.exists():
        print(f"测试文件 {json_file} 不存在")
        return
    
    core_counts = [1, 2, 4, 8, 16]
    results = []
    
    for num_cores in core_counts:
        print(f"\n测试 {num_cores} 核配置...")
        
        scheduler = EnhancedNPUScheduler(str(json_file), num_cores=num_cores)
        
        start_time = time.time()
        schedule = scheduler.solve_multi_core()
        execution_time = time.time() - start_time
        
        metrics = evaluate_schedule_detailed(scheduler, schedule)
        
        # 计算加速比
        if num_cores == 1:
            baseline_makespan = metrics['makespan']
        
        speedup = baseline_makespan / metrics['makespan'] if metrics['makespan'] > 0 else 1.0
        efficiency = speedup / num_cores
        
        results.append({
            '核心数': num_cores,
            'Makespan': metrics['makespan'],
            '执行时间': execution_time,
            '加速比': speedup,
            '并行效率': efficiency
        })
        
        scheduler.cleanup()
    
    # 打印结果
    df_scale = pd.DataFrame(results)
    print("\n可扩展性测试结果:")
    print(df_scale.to_string(index=False))
    
    # 绘制扩展性图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 加速比曲线
    ax1.plot(df_scale['核心数'], df_scale['加速比'], 'o-', label='实际加速比', linewidth=2)
    ax1.plot(df_scale['核心数'], df_scale['核心数'], 'r--', label='理想加速比', alpha=0.5)
    ax1.set_xlabel('核心数')
    ax1.set_ylabel('加速比')
    ax1.set_title('多核加速比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_xscale('log', base=2)
    
    # 并行效率曲线
    ax2.plot(df_scale['核心数'], df_scale['并行效率'] * 100, 'o-', linewidth=2, color='green')
    ax2.axhline(y=100, color='r', linestyle='--', alpha=0.5, label='理想效率')
    ax2.set_xlabel('核心数')
    ax2.set_ylabel('并行效率 (%)')
    ax2.set_title('并行效率')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_xscale('log', base=2)
    
    plt.suptitle('NPU多核可扩展性分析', fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    scale_path = Path("output") / "scalability_test.png"
    plt.savefig(scale_path, dpi=300, bbox_inches='tight')
    print(f"\n可扩展性图表已保存到: {scale_path}")
    plt.show()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='增强版NPU调度器测试')
    parser.add_argument('--mode', type=str, default='compare',
                       choices=['compare', 'ablation', 'scalability', 'all'],
                       help='运行模式')
    
    args = parser.parse_args()
    
    if args.mode == 'compare' or args.mode == 'all':
        run_strategy_comparison()
    
    if args.mode == 'ablation' or args.mode == 'all':
        run_ablation_study()
    
    if args.mode == 'scalability' or args.mode == 'all':
        run_scalability_test()
    
    print("\n" + "="*70)
    print("测试完成！")
    print("="*70)

if __name__ == "__main__":
    main()