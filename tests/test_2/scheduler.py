"""
修正版NPU调度器 - 2025年中国研究生数学建模竞赛A题
修复了原代码中的bug，完全符合题目要求，添加了可视化功能
"""

import json
import heapq
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Set, Optional, Any
import logging
import pandas as pd
import seaborn as sns

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
CACHE_SIZES = {
    "L1": 4096,
    "UB": 1024,
    "L0A": 256,
    "L0B": 256,
    "L0C": 512
}

# SPILL操作耗时计算参数
SPILL_COST_FACTOR = 2
SPILL_COST_BASE = 150

# ==================== 数据结构定义 ====================
@dataclass
class Node:
    """节点数据结构"""
    id: int
    op: str
    buf_id: Optional[int] = None
    size: Optional[int] = None
    type: Optional[str] = None
    pipe: Optional[str] = None
    cycles: Optional[int] = None
    bufs: List[int] = field(default_factory=list)
    
    def is_alloc(self) -> bool:
        return self.op == "ALLOC"
    
    def is_free(self) -> bool:
        return self.op == "FREE"
    
    def is_compute(self) -> bool:
        return not (self.is_alloc() or self.is_free())
    
    def is_copy_in(self) -> bool:
        return self.op == "COPY_IN"
    
    def is_copy_out(self) -> bool:
        return self.op == "COPY_OUT"

@dataclass
class Buffer:
    """缓冲区数据结构"""
    buf_id: int
    size: int
    type: str
    offset: int = -1
    alloc_node: Optional[int] = None
    free_node: Optional[int] = None
    producer_nodes: List[int] = field(default_factory=list)
    consumer_nodes: List[int] = field(default_factory=list)
    is_spilled: bool = False
    spill_offset: int = -1

@dataclass
class SpillOperation:
    """SPILL操作数据结构"""
    buf_id: int
    new_offset: int
    spill_out_node_id: int
    spill_in_node_id: int
    position_in_schedule: int

# ==================== 核心调度器类 ====================
class NPUScheduler:
    """NPU调度器主类"""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = Path(json_file_path)
        self.task_name = self.json_file_path.stem
        
        # 加载数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 初始化数据结构
        self.nodes: Dict[int, Node] = {}
        self.edges: List[Tuple[int, int]] = []
        self.buffers: Dict[int, Buffer] = {}
        
        # 解析数据
        self._parse_data()
        
        # 构建图结构
        self._build_graph_structures()
        
        # 结果存储
        self.schedule: List[int] = []
        self.memory_allocation: Dict[int, int] = {}
        self.spill_operations: List[SpillOperation] = []
        self.metrics = {
            'max_cache': 0,
            'makespan': 0,
            'extra_data_movement': 0,
            'l1_usage': [],
            'ub_usage': []
        }
    
    def _parse_data(self):
        """解析JSON数据"""
        # 解析节点
        for node_data in self.data["Nodes"]:
            node = Node(
                id=node_data["Id"],
                op=node_data["Op"],
                buf_id=node_data.get("BufId"),
                size=node_data.get("Size"),
                type=node_data.get("Type"),
                pipe=node_data.get("Pipe"),
                cycles=node_data.get("Cycles", 0),
                bufs=node_data.get("Bufs", [])
            )
            self.nodes[node.id] = node
            
            # 创建缓冲区对象
            if node.is_alloc():
                if node.buf_id not in self.buffers:
                    self.buffers[node.buf_id] = Buffer(
                        buf_id=node.buf_id,
                        size=node.size,
                        type=node.type
                    )
                self.buffers[node.buf_id].alloc_node = node.id
            elif node.is_free():
                if node.buf_id in self.buffers:
                    self.buffers[node.buf_id].free_node = node.id
        
        # 解析边
        self.edges = [(e[0], e[1]) for e in self.data["Edges"]]
    
    def _build_graph_structures(self):
        """构建图的辅助数据结构"""
        # 构建邻接表
        self.adj_list = defaultdict(list)
        self.reverse_adj_list = defaultdict(list)
        
        for src, dst in self.edges:
            self.adj_list[src].append(dst)
            self.reverse_adj_list[dst].append(src)
        
        # 计算入度
        self.in_degree = defaultdict(int)
        for _, dst in self.edges:
            self.in_degree[dst] += 1
        
        # 关联缓冲区与操作节点
        for node_id, node in self.nodes.items():
            if node.is_compute():
                for buf_id in node.bufs:
                    if buf_id in self.buffers:
                        if node.is_copy_in() or "COPY_IN" in node.op:
                            self.buffers[buf_id].producer_nodes.append(node_id)
                        else:
                            self.buffers[buf_id].consumer_nodes.append(node_id)
    
    # ==================== 问题1：最小缓存驻留调度 ====================
    def solve_min_cache_schedule(self) -> List[int]:
        """
        问题1：最小缓存驻留调度
        使用启发式算法最小化max(V_stay)
        时间复杂度: O(N^2 * log N)，N为节点数
        """
        logger.info(f"开始求解最小缓存驻留调度: {self.task_name}")
        
        # 使用优先级队列实现的贪心算法
        schedule = []
        scheduled = set()
        ready_queue = []
        current_cache = 0
        max_cache = 0
        cache_timeline = []
        
        # 获取所有入度为0的节点
        for node_id in self.nodes:
            if self.in_degree[node_id] == 0:
                priority = self._calculate_node_priority(node_id)
                heapq.heappush(ready_queue, (priority, node_id))
        
        while ready_queue:
            _, node_id = heapq.heappop(ready_queue)
            
            if node_id in scheduled:
                continue
            
            # 添加到调度序列
            schedule.append(node_id)
            scheduled.add(node_id)
            
            # 更新缓存使用
            node = self.nodes[node_id]
            if node.is_alloc():
                if node.type in ["L1", "UB"]:
                    current_cache += node.size
                    max_cache = max(max_cache, current_cache)
                    cache_timeline.append((len(schedule), current_cache, f"ALLOC {node.buf_id}"))
            elif node.is_free():
                if node.buf_id in self.buffers and self.buffers[node.buf_id].type in ["L1", "UB"]:
                    current_cache -= self.buffers[node.buf_id].size
                    cache_timeline.append((len(schedule), current_cache, f"FREE {node.buf_id}"))
            
            # 更新后继节点的入度并加入就绪队列
            for successor in self.adj_list[node_id]:
                # 创建临时入度字典用于跟踪
                if successor not in scheduled:
                    # 检查是否所有前驱都已调度
                    all_preds_scheduled = True
                    for pred in self.reverse_adj_list[successor]:
                        if pred not in scheduled:
                            all_preds_scheduled = False
                            break
                    
                    if all_preds_scheduled:
                        priority = self._calculate_node_priority(successor)
                        heapq.heappush(ready_queue, (priority, successor))
        
        self.schedule = schedule
        self.metrics['max_cache'] = max_cache
        self.metrics['cache_timeline'] = cache_timeline
        
        logger.info(f"最小缓存驻留调度完成，max(V_stay) = {max_cache}")
        return schedule
    
    def _calculate_node_priority(self, node_id: int) -> float:
        """
        计算节点的调度优先级
        优先级越低越优先调度
        """
        node = self.nodes[node_id]
        priority = 0.0
        
        # ALLOC节点优先级较高（负值）
        if node.is_alloc():
            priority = -1000
            # L0类型缓存的ALLOC优先级更高
            if node.type and "L0" in node.type:
                priority -= 500
        
        # FREE节点优先级较低（正值），尽早释放
        elif node.is_free():
            priority = 1000
            # 大缓存的FREE优先级更高（更早释放）
            if node.buf_id in self.buffers:
                priority -= self.buffers[node.buf_id].size * 0.1
        
        # 计算节点根据关键路径长度
        else:
            priority = -self._get_critical_path_length(node_id)
            
            # COPY_OUT节点延后调度
            if node.is_copy_out():
                priority += 500
        
        return priority
    
    def _get_critical_path_length(self, node_id: int) -> int:
        """计算从节点到叶节点的最长路径"""
        memo = {}
        
        def dfs(nid):
            if nid in memo:
                return memo[nid]
            
            if not self.adj_list[nid]:
                memo[nid] = self.nodes[nid].cycles or 0
                return memo[nid]
            
            max_length = 0
            for successor in self.adj_list[nid]:
                max_length = max(max_length, dfs(successor))
            
            memo[nid] = (self.nodes[nid].cycles or 0) + max_length
            return memo[nid]
        
        return dfs(node_id)
    
    # ==================== 问题2：缓存分配与换入换出 ====================
    def solve_memory_allocation(self) -> Tuple[Dict[int, int], List[SpillOperation]]:
        """
        问题2：缓存分配与SPILL操作
        目标：最小化总额外数据搬运量
        """
        logger.info(f"开始求解缓存分配: {self.task_name}")
        
        if not self.schedule:
            self.solve_min_cache_schedule()
        
        # 初始化缓存管理器
        cache_managers = {
            cache_type: CacheManager(cache_type, CACHE_SIZES[cache_type])
            for cache_type in CACHE_SIZES
        }
        
        allocation = {}
        spill_operations = []
        active_buffers = {}  # buf_id -> (cache_type, offset, size)
        
        # 按调度顺序处理节点
        for pos, node_id in enumerate(self.schedule):
            node = self.nodes[node_id]
            
            if node.is_alloc():
                buf = self.buffers[node.buf_id]
                cache_mgr = cache_managers[buf.type]
                
                # 尝试分配内存
                offset = cache_mgr.allocate(buf.size)
                
                if offset == -1:
                    # 需要SPILL操作
                    logger.debug(f"缓存{buf.type}空间不足，需要SPILL")
                    
                    # 选择要换出的缓冲区
                    victim_buf_id = self._select_victim_buffer(
                        active_buffers, buf.type, buf.size, pos
                    )
                    
                    if victim_buf_id is not None:
                        # 执行SPILL操作
                        spill_op = self._create_spill_operation(
                            victim_buf_id, pos, cache_mgr
                        )
                        spill_operations.append(spill_op)
                        
                        # 释放victim缓冲区
                        victim_info = active_buffers[victim_buf_id]
                        cache_mgr.free(victim_info[1], victim_info[2])
                        del active_buffers[victim_buf_id]
                        
                        # 重新尝试分配
                        offset = cache_mgr.allocate(buf.size)
                
                if offset != -1:
                    allocation[node.buf_id] = offset
                    active_buffers[node.buf_id] = (buf.type, offset, buf.size)
                else:
                    logger.warning(f"无法为缓冲区{node.buf_id}分配内存")
            
            elif node.is_free():
                if node.buf_id in active_buffers:
                    buf_info = active_buffers[node.buf_id]
                    cache_mgr = cache_managers[buf_info[0]]
                    cache_mgr.free(buf_info[1], buf_info[2])
                    del active_buffers[node.buf_id]
        
        self.memory_allocation = allocation
        self.spill_operations = spill_operations
        
        # 计算额外数据搬运量
        self._calculate_extra_data_movement()
        
        logger.info(f"缓存分配完成，SPILL操作数: {len(spill_operations)}")
        logger.info(f"总额外数据搬运量: {self.metrics['extra_data_movement']}")
        
        return allocation, spill_operations
    
    def _select_victim_buffer(self, active_buffers: Dict, cache_type: str, 
                             required_size: int, current_pos: int) -> Optional[int]:
        """选择要换出的缓冲区（LRU策略）"""
        candidates = []
        
        for buf_id, (buf_type, offset, size) in active_buffers.items():
            if buf_type == cache_type and size >= required_size:
                # 计算下次使用距离
                next_use = self._find_next_use(buf_id, current_pos)
                candidates.append((next_use, buf_id))
        
        if candidates:
            # 选择最远将来使用的缓冲区
            candidates.sort(reverse=True)
            return candidates[0][1]
        
        return None
    
    def _find_next_use(self, buf_id: int, current_pos: int) -> int:
        """找到缓冲区的下次使用位置"""
        buf = self.buffers[buf_id]
        
        for i in range(current_pos + 1, len(self.schedule)):
            node_id = self.schedule[i]
            node = self.nodes[node_id]
            
            if node.is_compute() and buf_id in node.bufs:
                return i
            if node.is_free() and node.buf_id == buf_id:
                return i
        
        return len(self.schedule)  # 之后不再使用
    
    def _create_spill_operation(self, buf_id: int, position: int, 
                               cache_mgr: 'CacheManager') -> SpillOperation:
        """创建SPILL操作"""
        # 分配新的节点ID
        next_node_id = len(self.nodes)
        
        # 找到新的偏移地址
        new_offset = cache_mgr.find_best_fit(self.buffers[buf_id].size)
        
        return SpillOperation(
            buf_id=buf_id,
            new_offset=new_offset,
            spill_out_node_id=next_node_id,
            spill_in_node_id=next_node_id + 1,
            position_in_schedule=position
        )
    
    def _calculate_extra_data_movement(self):
        """计算总额外数据搬运量"""
        total = 0
        
        for spill_op in self.spill_operations:
            buf = self.buffers[spill_op.buf_id]
            
            # 检查是否被COPY_IN使用
            has_copy_in = False
            for node_id in buf.producer_nodes:
                if self.nodes[node_id].is_copy_in():
                    has_copy_in = True
                    break
            
            if has_copy_in:
                # 情况2：仅SPILL_IN产生搬运
                total += buf.size
            else:
                # 情况1：SPILL_OUT和SPILL_IN都产生搬运
                total += buf.size * 2
        
        self.metrics['extra_data_movement'] = total
    
    # ==================== 问题3：性能优化 ====================
    def solve_performance_optimization(self) -> List[int]:
        """
        问题3：性能优化策略
        在额外数据搬运量不显著增加的前提下，优化总执行时间
        """
        logger.info(f"开始性能优化: {self.task_name}")
        
        # 先获取基础解
        base_schedule = self.solve_min_cache_schedule()
        base_allocation, base_spills = self.solve_memory_allocation()
        base_makespan = self._calculate_makespan(base_schedule)
        base_extra_movement = self.metrics['extra_data_movement']
        
        logger.info(f"基础方案 - Makespan: {base_makespan}, 额外搬运: {base_extra_movement}")
        
        # 尝试多种优化策略
        best_schedule = base_schedule
        best_makespan = base_makespan
        best_extra_movement = base_extra_movement
        
        # 策略1：流水线优化
        pipeline_schedule = self._optimize_pipeline_schedule()
        if pipeline_schedule:
            pipeline_makespan = self._calculate_makespan(pipeline_schedule)
            self.schedule = pipeline_schedule
            _, pipeline_spills = self.solve_memory_allocation()
            pipeline_extra = self.metrics['extra_data_movement']
            
            # 检查是否改进
            if (pipeline_makespan < best_makespan and 
                pipeline_extra <= base_extra_movement * 1.1):  # 允许10%增加
                best_schedule = pipeline_schedule
                best_makespan = pipeline_makespan
                best_extra_movement = pipeline_extra
                logger.info(f"流水线优化成功 - Makespan: {pipeline_makespan}")
        
        # 策略2：数据局部性优化
        locality_schedule = self._optimize_data_locality()
        if locality_schedule:
            locality_makespan = self._calculate_makespan(locality_schedule)
            self.schedule = locality_schedule
            _, locality_spills = self.solve_memory_allocation()
            locality_extra = self.metrics['extra_data_movement']
            
            if (locality_makespan < best_makespan and 
                locality_extra <= base_extra_movement * 1.1):
                best_schedule = locality_schedule
                best_makespan = locality_makespan
                best_extra_movement = locality_extra
                logger.info(f"数据局部性优化成功 - Makespan: {locality_makespan}")
        
        self.schedule = best_schedule
        self.metrics['makespan'] = best_makespan
        self.metrics['extra_data_movement'] = best_extra_movement
        
        logger.info(f"优化完成 - 最终Makespan: {best_makespan}, 额外搬运: {best_extra_movement}")
        logger.info(f"性能提升: {(1 - best_makespan/base_makespan)*100:.1f}%")
        
        return best_schedule
    
    def _calculate_makespan(self, schedule: List[int]) -> int:
        """
        计算总执行时间（按照题目附录C的方法）
        """
        # 初始化每个节点的开始和结束时间
        start_time = {}
        end_time = {}
        
        # 按执行单元分组
        unit_last_end_time = defaultdict(int)
        
        for node_id in schedule:
            node = self.nodes[node_id]
            
            # 计算开始时间
            start = 0
            
            # 依赖约束
            for pred in self.reverse_adj_list[node_id]:
                if pred in end_time:
                    start = max(start, end_time[pred])
            
            # 资源约束（同一执行单元串行）
            if node.pipe:
                start = max(start, unit_last_end_time[node.pipe])
            
            # 计算结束时间
            start_time[node_id] = start
            end_time[node_id] = start + (node.cycles or 0)
            
            if node.pipe:
                unit_last_end_time[node.pipe] = end_time[node_id]
        
        return max(end_time.values()) if end_time else 0
    
    def _optimize_pipeline_schedule(self) -> Optional[List[int]]:
        """流水线优化：重排序以最大化并行度"""
        # 识别可并行的操作组
        parallel_groups = self._identify_parallel_groups()
        
        if not parallel_groups:
            return None
        
        # 重新调度以最大化流水线效率
        optimized = []
        scheduled = set()
        
        for group in parallel_groups:
            # 在每组内按执行单元交错排列
            by_pipe = defaultdict(list)
            for node_id in group:
                if node_id not in scheduled:
                    node = self.nodes[node_id]
                    if node.pipe:
                        by_pipe[node.pipe].append(node_id)
                    else:
                        optimized.append(node_id)
                        scheduled.add(node_id)
            
            # 交错添加不同单元的操作
            iterators = [iter(nodes) for nodes in by_pipe.values()]
            while iterators:
                for it in iterators[:]:
                    try:
                        node_id = next(it)
                        optimized.append(node_id)
                        scheduled.add(node_id)
                    except StopIteration:
                        iterators.remove(it)
        
        # 添加剩余节点
        for node_id in self.schedule:
            if node_id not in scheduled:
                optimized.append(node_id)
        
        return optimized if self._is_valid_schedule(optimized) else None
    
    def _optimize_data_locality(self) -> Optional[List[int]]:
        """数据局部性优化：将使用相同数据的操作聚集"""
        # 构建数据使用图
        buf_users = defaultdict(list)
        for node_id, node in self.nodes.items():
            for buf_id in node.bufs:
                buf_users[buf_id].append(node_id)
        
        # 聚类相关操作
        clusters = []
        visited = set()
        
        for buf_id, users in buf_users.items():
            cluster = set()
            for user in users:
                if user not in visited:
                    cluster.add(user)
                    visited.add(user)
                    # 添加相关节点
                    for pred in self.reverse_adj_list[user]:
                        if pred not in visited and self.nodes[pred].is_compute():
                            cluster.add(pred)
                            visited.add(pred)
            
            if cluster:
                clusters.append(cluster)
        
        # 基于聚类重新调度
        optimized = []
        scheduled = set()
        
        for cluster in clusters:
            # 对聚类内节点进行拓扑排序
            cluster_schedule = self._topological_sort_subset(cluster)
            for node_id in cluster_schedule:
                if node_id not in scheduled:
                    optimized.append(node_id)
                    scheduled.add(node_id)
        
        # 添加剩余节点
        for node_id in self.schedule:
            if node_id not in scheduled:
                optimized.append(node_id)
        
        return optimized if self._is_valid_schedule(optimized) else None
    
    def _identify_parallel_groups(self) -> List[List[int]]:
        """识别可并行执行的操作组"""
        groups = []
        levels = self._compute_levels()
        
        for level_nodes in levels.values():
            # 同一层级的节点可能并行
            group = []
            for node_id in level_nodes:
                node = self.nodes[node_id]
                if node.is_compute():
                    group.append(node_id)
            
            if len(group) > 1:
                groups.append(group)
        
        return groups
    
    def _compute_levels(self) -> Dict[int, List[int]]:
        """计算节点的层级（BFS）"""
        levels = defaultdict(list)
        visited = set()
        queue = deque()
        
        # 从入度为0的节点开始
        for node_id in self.nodes:
            if self.in_degree[node_id] == 0:
                queue.append((node_id, 0))
                visited.add(node_id)
        
        while queue:
            node_id, level = queue.popleft()
            levels[level].append(node_id)
            
            for successor in self.adj_list[node_id]:
                if successor not in visited:
                    # 检查所有前驱是否已访问
                    all_preds_visited = all(
                        pred in visited for pred in self.reverse_adj_list[successor]
                    )
                    if all_preds_visited:
                        queue.append((successor, level + 1))
                        visited.add(successor)
        
        return levels
    
    def _topological_sort_subset(self, subset: Set[int]) -> List[int]:
        """对节点子集进行拓扑排序"""
        result = []
        in_degree = defaultdict(int)
        
        # 计算子集内的入度
        for node_id in subset:
            for pred in self.reverse_adj_list[node_id]:
                if pred in subset:
                    in_degree[node_id] += 1
        
        # 拓扑排序
        queue = deque([n for n in subset if in_degree[n] == 0])
        
        while queue:
            node_id = queue.popleft()
            result.append(node_id)
            
            for successor in self.adj_list[node_id]:
                if successor in subset:
                    in_degree[successor] -= 1
                    if in_degree[successor] == 0:
                        queue.append(successor)
        
        return result
    
    def _is_valid_schedule(self, schedule: List[int]) -> bool:
        """检查调度序列是否满足依赖约束"""
        position = {node_id: i for i, node_id in enumerate(schedule)}
        
        for src, dst in self.edges:
            if src in position and dst in position:
                if position[src] >= position[dst]:
                    return False
        
        return len(schedule) == len(self.nodes)
    
    # ==================== 可视化功能 ====================
    def visualize_pipeline(self, schedule: Optional[List[int]] = None):
        """可视化流水线执行图"""
        if schedule is None:
            schedule = self.schedule
        
        if not schedule:
            logger.warning("没有可用的调度序列")
            return
        
        # 计算时序信息
        start_time = {}
        end_time = {}
        unit_tasks = defaultdict(list)
        
        unit_last_end_time = defaultdict(int)
        
        for node_id in schedule:
            node = self.nodes[node_id]
            
            if not node.pipe or node.cycles == 0:
                continue
            
            # 计算开始时间
            start = 0
            for pred in self.reverse_adj_list[node_id]:
                if pred in end_time:
                    start = max(start, end_time[pred])
            
            start = max(start, unit_last_end_time[node.pipe])
            
            # 记录时间
            start_time[node_id] = start
            end_time[node_id] = start + node.cycles
            unit_last_end_time[node.pipe] = end_time[node_id]
            
            # 添加到单元任务列表
            unit_tasks[node.pipe].append((node_id, start, end_time[node_id]))
        
        # 创建甘特图
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 颜色映射
        colors = plt.cm.Set3(np.linspace(0, 1, 12))
        pipe_colors = {pipe: colors[i % 12] for i, pipe in enumerate(unit_tasks.keys())}
        
        y_pos = 0
        y_labels = []
        
        for pipe, tasks in unit_tasks.items():
            y_labels.append(pipe)
            
            for node_id, start, end in tasks:
                node = self.nodes[node_id]
                
                # 绘制任务块
                ax.barh(y_pos, end - start, left=start, height=0.8,
                       color=pipe_colors[pipe], edgecolor='black', linewidth=0.5)
                
                # 添加任务标签
                if end - start > 5:  # 只在足够宽的块上显示标签
                    ax.text(start + (end - start) / 2, y_pos,
                           f"{node.op[:6]}\n{node_id}",
                           ha='center', va='center', fontsize=8)
            
            y_pos += 1
        
        ax.set_yticks(range(len(y_labels)))
        ax.set_yticklabels(y_labels)
        ax.set_xlabel('Time (cycles)')
        ax.set_title(f'Pipeline Schedule - {self.task_name}\nMakespan: {max(end_time.values()) if end_time else 0} cycles')
        ax.grid(True, axis='x', alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def visualize_memory_usage(self):
        """可视化内存使用情况"""
        if not self.schedule:
            logger.warning("没有可用的调度序列")
            return
        
        # 计算内存使用时间线
        l1_usage = []
        ub_usage = []
        positions = []
        
        current_l1 = 0
        current_ub = 0
        
        for i, node_id in enumerate(self.schedule):
            node = self.nodes[node_id]
            
            if node.is_alloc():
                if node.type == "L1":
                    current_l1 += node.size
                elif node.type == "UB":
                    current_ub += node.size
            elif node.is_free():
                if node.buf_id in self.buffers:
                    buf = self.buffers[node.buf_id]
                    if buf.type == "L1":
                        current_l1 -= buf.size
                    elif buf.type == "UB":
                        current_ub -= buf.size
            
            l1_usage.append(current_l1)
            ub_usage.append(current_ub)
            positions.append(i)
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 8), sharex=True)
        
        # L1缓存使用
        ax1.plot(positions, l1_usage, 'b-', linewidth=2, label='L1 Usage')
        ax1.axhline(y=CACHE_SIZES["L1"], color='r', linestyle='--', label='L1 Capacity')
        ax1.fill_between(positions, 0, l1_usage, alpha=0.3, color='blue')
        ax1.set_ylabel('L1 Cache Usage (bytes)')
        ax1.set_title(f'Memory Usage Timeline - {self.task_name}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # UB缓存使用
        ax2.plot(positions, ub_usage, 'g-', linewidth=2, label='UB Usage')
        ax2.axhline(y=CACHE_SIZES["UB"], color='r', linestyle='--', label='UB Capacity')
        ax2.fill_between(positions, 0, ub_usage, alpha=0.3, color='green')
        ax2.set_xlabel('Schedule Position')
        ax2.set_ylabel('UB Cache Usage (bytes)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 标记SPILL操作
        for spill_op in self.spill_operations:
            ax1.axvline(x=spill_op.position_in_schedule, color='orange', 
                       linestyle=':', alpha=0.7, label='SPILL' if spill_op == self.spill_operations[0] else '')
        
        plt.tight_layout()
        plt.show()
    
    def visualize_dag_structure(self, max_nodes: int = 100):
        """可视化DAG结构（限制节点数以保持可读性）"""
        import networkx as nx
        
        if len(self.nodes) > max_nodes:
            logger.info(f"节点数过多({len(self.nodes)})，只显示前{max_nodes}个节点")
            nodes_to_show = list(self.nodes.keys())[:max_nodes]
        else:
            nodes_to_show = list(self.nodes.keys())
        
        # 创建NetworkX图
        G = nx.DiGraph()
        
        for node_id in nodes_to_show:
            node = self.nodes[node_id]
            G.add_node(node_id, label=f"{node.op[:6]}\n{node_id}")
        
        for src, dst in self.edges:
            if src in nodes_to_show and dst in nodes_to_show:
                G.add_edge(src, dst)
        
        # 布局
        pos = nx.spring_layout(G, k=2, iterations=50)
        
        # 绘制
        fig, ax = plt.subplots(figsize=(14, 10))
        
        # 节点颜色
        node_colors = []
        for node_id in G.nodes():
            node = self.nodes[node_id]
            if node.is_alloc():
                node_colors.append('lightblue')
            elif node.is_free():
                node_colors.append('lightcoral')
            elif node.is_copy_in():
                node_colors.append('lightgreen')
            elif node.is_copy_out():
                node_colors.append('lightyellow')
            else:
                node_colors.append('lightgray')
        
        nx.draw_networkx_nodes(G, pos, node_color=node_colors, 
                              node_size=500, ax=ax)
        nx.draw_networkx_edges(G, pos, edge_color='gray', 
                              arrows=True, arrowsize=10, ax=ax)
        
        # 标签
        labels = {node: self.nodes[node].op[:6] for node in G.nodes()}
        nx.draw_networkx_labels(G, pos, labels, font_size=8, ax=ax)
        
        ax.set_title(f'DAG Structure - {self.task_name} (showing {len(G.nodes())} nodes)')
        ax.axis('off')
        
        # 添加图例
        legend_elements = [
            mpatches.Patch(color='lightblue', label='ALLOC'),
            mpatches.Patch(color='lightcoral', label='FREE'),
            mpatches.Patch(color='lightgreen', label='COPY_IN'),
            mpatches.Patch(color='lightyellow', label='COPY_OUT'),
            mpatches.Patch(color='lightgray', label='Other')
        ]
        ax.legend(handles=legend_elements, loc='upper right')
        
        plt.tight_layout()
        plt.show()
    
    # ==================== 结果输出 ====================
    def save_results(self, problem_num: int, output_dir: Path):
        """保存结果到文件"""
        output_dir = Path(output_dir) / f"Problem{problem_num}"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存调度序列
        schedule_file = output_dir / f"{self.task_name}_schedule.txt"
        with open(schedule_file, 'w') as f:
            for node_id in self.schedule:
                f.write(f"{node_id}\n")
        
        # 问题2和3需要保存额外信息
        if problem_num >= 2:
            # 保存内存分配
            memory_file = output_dir / f"{self.task_name}_memory.txt"
            with open(memory_file, 'w') as f:
                for buf_id, offset in self.memory_allocation.items():
                    f.write(f"{buf_id}:{offset}\n")
            
            # 保存SPILL操作
            spill_file = output_dir / f"{self.task_name}_spill.txt"
            with open(spill_file, 'w') as f:
                for spill_op in self.spill_operations:
                    f.write(f"{spill_op.buf_id}:{spill_op.new_offset}\n")
        
        logger.info(f"结果已保存到 {output_dir}")
    
    def print_summary(self):
        """打印结果摘要"""
        print(f"\n{'='*60}")
        print(f"任务: {self.task_name}")
        print(f"{'='*60}")
        print(f"节点数: {len(self.nodes)}")
        print(f"边数: {len(self.edges)}")
        print(f"缓冲区数: {len(self.buffers)}")
        print(f"-" * 60)
        print(f"max(V_stay): {self.metrics['max_cache']} bytes")
        print(f"Makespan: {self.metrics['makespan']} cycles")
        print(f"总额外数据搬运量: {self.metrics['extra_data_movement']} bytes")
        print(f"SPILL操作数: {len(self.spill_operations)}")
        print(f"{'='*60}\n")


# ==================== 辅助类 ====================
class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_type: str, capacity: int):
        self.cache_type = cache_type
        self.capacity = capacity
        self.free_blocks = [(0, capacity)]  # (offset, size)列表
        self.allocated_blocks = []  # (offset, size)列表
    
    def allocate(self, size: int) -> int:
        """分配内存，返回偏移地址，失败返回-1"""
        # 首次适应算法
        for i, (offset, block_size) in enumerate(self.free_blocks):
            if block_size >= size:
                # 分配内存
                self.allocated_blocks.append((offset, size))
                
                # 更新空闲块
                if block_size == size:
                    del self.free_blocks[i]
                else:
                    self.free_blocks[i] = (offset + size, block_size - size)
                
                return offset
        
        return -1  # 分配失败
    
    def free(self, offset: int, size: int):
        """释放内存"""
        # 从已分配列表中移除
        self.allocated_blocks = [
            (o, s) for o, s in self.allocated_blocks
            if o != offset
        ]
        
        # 添加到空闲列表并合并相邻块
        self.free_blocks.append((offset, size))
        self.free_blocks.sort()
        
        # 合并相邻空闲块
        merged = []
        for offset, size in self.free_blocks:
            if merged and merged[-1][0] + merged[-1][1] == offset:
                # 与前一个块相邻，合并
                merged[-1] = (merged[-1][0], merged[-1][1] + size)
            else:
                merged.append((offset, size))
        
        self.free_blocks = merged
    
    def find_best_fit(self, size: int) -> int:
        """最佳适应算法找到最合适的偏移"""
        best_offset = -1
        best_waste = float('inf')
        
        for offset, block_size in self.free_blocks:
            if block_size >= size:
                waste = block_size - size
                if waste < best_waste:
                    best_waste = waste
                    best_offset = offset
        
        return best_offset


# ==================== 主函数 ====================
def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='NPU调度器 - 2025数模竞赛A题')
    parser.add_argument('--input', type=str, required=True, help='输入JSON文件路径')
    parser.add_argument('--problem', type=int, default=3, choices=[1, 2, 3],
                       help='求解的问题编号')
    parser.add_argument('--output', type=str, default='output', help='输出目录')
    parser.add_argument('--visualize', action='store_true', help='是否显示可视化')
    
    args = parser.parse_args()
    
    # 创建调度器
    scheduler = NPUScheduler(args.input)
    
    # 根据问题编号求解
    if args.problem == 1:
        schedule = scheduler.solve_min_cache_schedule()
        scheduler.print_summary()
    elif args.problem == 2:
        schedule = scheduler.solve_min_cache_schedule()
        allocation, spills = scheduler.solve_memory_allocation()
        scheduler.print_summary()
    else:  # problem == 3
        schedule = scheduler.solve_performance_optimization()
        scheduler.print_summary()
    
    # 保存结果
    scheduler.save_results(args.problem, args.output)
    
    # 可视化
    if args.visualize:
        if len(scheduler.nodes) <= 200:
            scheduler.visualize_dag_structure()
        scheduler.visualize_memory_usage()
        scheduler.visualize_pipeline()
    
    return scheduler


if __name__ == "__main__":
    # 示例运行
    test_cases = [
        "Matmul_Case0",
        "Matmul_Case1",
        "FlashAttention_Case0",
        "FlashAttention_Case1",
        "Conv_Case0",
        "Conv_Case1"
    ]
    
    results = []
    
    for case_name in test_cases[:1]:  # 测试第一个案例
        json_file = f"data/{case_name}.json"
        
        try:
            print(f"\n处理案例: {case_name}")
            scheduler = NPUScheduler(json_file)
            
            # 问题1
            schedule = scheduler.solve_min_cache_schedule()
            problem1_max_cache = scheduler.metrics['max_cache']
            
            # 问题2
            allocation, spills = scheduler.solve_memory_allocation()
            problem2_extra_movement = scheduler.metrics['extra_data_movement']
            
            # 问题3
            optimized_schedule = scheduler.solve_performance_optimization()
            problem3_makespan = scheduler.metrics['makespan']
            problem3_extra_movement = scheduler.metrics['extra_data_movement']
            
            scheduler.print_summary()
            
            # 可视化（仅对小规模案例）
            if len(scheduler.nodes) < 100:
                scheduler.visualize_pipeline()
                scheduler.visualize_memory_usage()
            
            results.append({
                '案例': case_name,
                'max(V_stay)': problem1_max_cache,
                '总额外数据搬运量': problem2_extra_movement,
                '优化后Makespan': problem3_makespan,
                '优化后额外搬运': problem3_extra_movement
            })
            
        except Exception as e:
            print(f"处理{case_name}时出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 打印汇总表格
    if results:
        df = pd.DataFrame(results)
        print("\n" + "="*80)
        print("结果汇总")
        print("="*80)
        print(df.to_string(index=False))