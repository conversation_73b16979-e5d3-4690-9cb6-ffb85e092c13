"""
2025年中国研究生数学建模竞赛A题 - 完整解决方案
NPU核内调度问题完整实现，包含可视化和所有优化策略
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import Rectangle
import seaborn as sns
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Set, Optional, Any
import heapq
import logging
import time
from tqdm import tqdm
import pandas as pd
from copy import deepcopy

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
CACHE_SIZES = {
    "L1": 4096,
    "UB": 1024,
    "L0A": 256,
    "L0B": 256,
    "L0C": 512
}

# SPILL操作耗时计算参数（根据附录D）
SPILL_COST_FACTOR = 2
SPILL_COST_BASE = 150

# ==================== 数据结构定义 ====================
@dataclass
class Node:
    """节点数据结构"""
    id: int
    op: str
    buf_id: Optional[int] = None
    size: Optional[int] = None
    type: Optional[str] = None
    pipe: Optional[str] = None
    cycles: Optional[int] = None
    bufs: List[int] = field(default_factory=list)
    
    def is_alloc(self) -> bool:
        return self.op == "ALLOC"
    
    def is_free(self) -> bool:
        return self.op == "FREE"
    
    def is_compute(self) -> bool:
        return not (self.is_alloc() or self.is_free())
    
    def is_copy_in(self) -> bool:
        return self.op == "COPY_IN"
    
    def is_copy_out(self) -> bool:
        return self.op == "COPY_OUT"
    
    def is_spill_out(self) -> bool:
        return self.op == "SPILL_OUT"
    
    def is_spill_in(self) -> bool:
        return self.op == "SPILL_IN"

@dataclass
class Buffer:
    """缓冲区数据结构"""
    buf_id: int
    size: int
    type: str
    offset: int = -1
    alloc_node: Optional[int] = None
    free_node: Optional[int] = None
    producer_nodes: List[int] = field(default_factory=list)
    consumer_nodes: List[int] = field(default_factory=list)
    copy_in_nodes: List[int] = field(default_factory=list)

@dataclass
class SpillOperation:
    """SPILL操作数据结构"""
    buf_id: int
    new_offset: int

# ==================== 缓存管理器 ====================
class CacheManager:
    """缓存管理器 - 优化的内存分配算法"""
    
    def __init__(self, cache_type: str, capacity: int):
        self.cache_type = cache_type
        self.capacity = capacity
        self.free_blocks = [(0, capacity)]  # (offset, size)列表
        self.allocated_blocks = {}  # buf_id -> (offset, size)
        
    def allocate(self, buf_id: int, size: int) -> int:
        """最佳适应算法分配内存"""
        if size > self.capacity:
            return -1
        
        best_idx = -1
        best_offset = -1
        best_waste = float('inf')
        
        for i, (offset, block_size) in enumerate(self.free_blocks):
            if block_size >= size:
                waste = block_size - size
                if waste < best_waste:
                    best_waste = waste
                    best_idx = i
                    best_offset = offset
        
        if best_idx == -1:
            return -1
        
        # 分配内存
        offset, block_size = self.free_blocks[best_idx]
        self.allocated_blocks[buf_id] = (offset, size)
        
        # 更新空闲块
        if block_size == size:
            del self.free_blocks[best_idx]
        else:
            self.free_blocks[best_idx] = (offset + size, block_size - size)
        
        return offset
    
    def free(self, buf_id: int) -> bool:
        """释放内存并合并相邻空闲块"""
        if buf_id not in self.allocated_blocks:
            return False
        
        offset, size = self.allocated_blocks[buf_id]
        del self.allocated_blocks[buf_id]
        
        # 添加到空闲列表并合并
        self.free_blocks.append((offset, size))
        self.free_blocks.sort()
        
        # 合并相邻块
        merged = []
        for offset, size in self.free_blocks:
            if merged and merged[-1][0] + merged[-1][1] == offset:
                merged[-1] = (merged[-1][0], merged[-1][1] + size)
            else:
                merged.append((offset, size))
        
        self.free_blocks = merged
        return True
    
    def get_fragmentation(self) -> float:
        """计算碎片率"""
        if not self.free_blocks:
            return 0.0
        total_free = sum(size for _, size in self.free_blocks)
        max_free = max(size for _, size in self.free_blocks)
        if total_free == 0:
            return 0.0
        return 1.0 - (max_free / total_free)

# ==================== NPU调度器主类 ====================
class CompetitionNPUScheduler:
    """竞赛版NPU调度器 - 完全符合题目要求"""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = Path(json_file_path)
        self.task_name = self.json_file_path.stem
        
        # 加载数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 初始化数据结构
        self.nodes: Dict[int, Node] = {}
        self.edges: List[Tuple[int, int]] = []
        self.buffers: Dict[int, Buffer] = {}
        
        # 解析数据
        self._parse_data()
        self._build_graph_structures()
        
        # 结果存储
        self.schedule: List[int] = []
        self.memory_allocation: Dict[int, int] = {}
        self.spill_operations: List[SpillOperation] = []
        
        # 性能指标
        self.metrics = {
            'max_v_stay': 0,
            'makespan': 0,
            'extra_data_movement': 0,
            'l1_peak': 0,
            'ub_peak': 0,
            'spill_count': 0
        }
        
    def _parse_data(self):
        """解析JSON数据"""
        # 解析节点
        for node_data in self.data["Nodes"]:
            node = Node(
                id=node_data["Id"],
                op=node_data["Op"],
                buf_id=node_data.get("BufId"),
                size=node_data.get("Size"),
                type=node_data.get("Type"),
                pipe=node_data.get("Pipe"),
                cycles=node_data.get("Cycles", 0),
                bufs=node_data.get("Bufs", [])
            )
            self.nodes[node.id] = node
            
            # 创建缓冲区对象
            if node.is_alloc():
                if node.buf_id not in self.buffers:
                    self.buffers[node.buf_id] = Buffer(
                        buf_id=node.buf_id,
                        size=node.size,
                        type=node.type
                    )
                self.buffers[node.buf_id].alloc_node = node.id
            elif node.is_free():
                if node.buf_id in self.buffers:
                    self.buffers[node.buf_id].free_node = node.id
        
        # 解析边
        self.edges = [(e[0], e[1]) for e in self.data["Edges"]]
        
    def _build_graph_structures(self):
        """构建图的辅助数据结构"""
        self.adj_list = defaultdict(list)
        self.reverse_adj_list = defaultdict(list)
        self.in_degree = defaultdict(int)
        
        for src, dst in self.edges:
            self.adj_list[src].append(dst)
            self.reverse_adj_list[dst].append(src)
            self.in_degree[dst] += 1
        
        # 关联缓冲区与操作节点
        for node_id, node in self.nodes.items():
            if node.is_compute() and node.bufs:
                for buf_id in node.bufs:
                    if buf_id in self.buffers:
                        if node.is_copy_in():
                            self.buffers[buf_id].copy_in_nodes.append(node_id)
                            self.buffers[buf_id].producer_nodes.append(node_id)
                        else:
                            self.buffers[buf_id].consumer_nodes.append(node_id)
    
    # ==================== 问题1：最小缓存驻留调度 ====================
    def solve_problem1(self) -> List[int]:
        """
        问题1：最小缓存驻留调度
        目标：最小化max(V_stay)
        时间复杂度：O(N^2 log N)
        """
        logger.info(f"[问题1] 开始求解最小缓存驻留调度: {self.task_name}")
        
        schedule = []
        scheduled = set()
        ready_queue = []
        
        # 缓存使用跟踪
        current_cache = defaultdict(int)  # type -> usage
        max_cache = defaultdict(int)
        total_max = 0
        
        # L0缓存占用状态（每个L0缓存同时只能有一个缓冲区）
        l0_occupied = {"L0A": None, "L0B": None, "L0C": None}
        
        # 计算节点优先级
        node_priorities = self._compute_node_priorities_p1()
        
        # 初始化就绪队列
        for node_id in self.nodes:
            if self.in_degree[node_id] == 0:
                priority = node_priorities[node_id]
                heapq.heappush(ready_queue, (priority, node_id))
        
        # 进度条
        pbar = tqdm(total=len(self.nodes), desc=f"问题1调度 {self.task_name}")
        
        while ready_queue:
            _, node_id = heapq.heappop(ready_queue)
            
            if node_id in scheduled:
                continue
            
            node = self.nodes[node_id]
            
            # 检查L0约束
            if node.type in ["L0A", "L0B", "L0C"]:
                if node.is_alloc():
                    if l0_occupied[node.type] is not None:
                        # L0已占用，延后调度
                        heapq.heappush(ready_queue, (node_priorities[node_id] + 10000, node_id))
                        continue
                    l0_occupied[node.type] = node.buf_id
                elif node.is_free() and node.buf_id in self.buffers:
                    buf = self.buffers[node.buf_id]
                    if buf.type in l0_occupied:
                        l0_occupied[buf.type] = None
            
            # 添加到调度序列
            schedule.append(node_id)
            scheduled.add(node_id)
            pbar.update(1)
            
            # 更新缓存使用（只统计L1和UB）
            if node.is_alloc() and node.type in ["L1", "UB"]:
                current_cache[node.type] += node.size
                max_cache[node.type] = max(max_cache[node.type], current_cache[node.type])
                total_max = max(total_max, current_cache["L1"] + current_cache["UB"])
            elif node.is_free() and node.buf_id in self.buffers:
                buf = self.buffers[node.buf_id]
                if buf.type in ["L1", "UB"]:
                    current_cache[buf.type] = max(0, current_cache[buf.type] - buf.size)
            
            # 更新后继节点
            for successor in self.adj_list[node_id]:
                if successor not in scheduled:
                    all_preds_scheduled = all(
                        pred in scheduled for pred in self.reverse_adj_list[successor]
                    )
                    if all_preds_scheduled:
                        priority = node_priorities[successor]
                        heapq.heappush(ready_queue, (priority, successor))
        
        pbar.close()
        
        self.schedule = schedule
        self.metrics['max_v_stay'] = total_max
        self.metrics['l1_peak'] = max_cache["L1"]
        self.metrics['ub_peak'] = max_cache["UB"]
        
        logger.info(f"[问题1] 完成 - max(V_stay)={total_max}")
        
        return schedule
    
    def _compute_node_priorities_p1(self) -> Dict[int, float]:
        """计算问题1的节点优先级"""
        priorities = {}
        critical_paths = self._compute_critical_paths()
        
        for node_id, node in self.nodes.items():
            priority = 0.0
            
            if node.is_alloc():
                # ALLOC节点优先调度
                priority = -3000
                if "L0" in (node.type or ""):
                    priority -= 2000  # L0的ALLOC更优先
                priority += node.size * 0.001  # 小缓冲区优先
                
            elif node.is_free():
                # FREE节点尽早调度
                priority = -1000
                buf = self.buffers.get(node.buf_id)
                if buf:
                    priority -= buf.size * 0.01  # 大缓冲区优先释放
                    
            else:
                # 计算节点按关键路径
                priority = -critical_paths.get(node_id, 0)
                if node.is_copy_in():
                    priority -= 500
                elif node.is_copy_out():
                    priority += 1000
            
            priorities[node_id] = priority
        
        return priorities
    
    def _compute_critical_paths(self) -> Dict[int, int]:
        """计算关键路径长度"""
        memo = {}
        
        def dfs(node_id):
            if node_id in memo:
                return memo[node_id]
            
            node = self.nodes[node_id]
            cycles = node.cycles or 0
            
            if not self.adj_list[node_id]:
                memo[node_id] = cycles
            else:
                max_path = max((dfs(succ) for succ in self.adj_list[node_id]), default=0)
                memo[node_id] = cycles + max_path
            
            return memo[node_id]
        
        for node_id in self.nodes:
            if node_id not in memo:
                dfs(node_id)
        
        return memo
    
    # ==================== 问题2：缓存分配与换入换出 ====================
    def solve_problem2(self, base_schedule: Optional[List[int]] = None) -> Tuple[Dict[int, int], List[SpillOperation]]:
        """
        问题2：缓存分配与SPILL操作
        目标：最小化总额外数据搬运量
        """
        logger.info(f"[问题2] 开始求解缓存分配: {self.task_name}")
        
        if base_schedule:
            self.schedule = base_schedule
        elif not self.schedule:
            self.solve_problem1()
        
        # 初始化缓存管理器
        cache_managers = {
            cache_type: CacheManager(cache_type, CACHE_SIZES[cache_type])
            for cache_type in CACHE_SIZES
        }
        
        allocation = {}
        spill_operations = []
        active_buffers = {}  # buf_id -> (type, offset, size)
        
        # 进度条
        pbar = tqdm(total=len(self.schedule), desc=f"问题2内存分配 {self.task_name}")
        
        for pos, node_id in enumerate(self.schedule):
            node = self.nodes[node_id]
            pbar.update(1)
            
            if node.is_alloc():
                buf = self.buffers[node.buf_id]
                cache_mgr = cache_managers[buf.type]
                
                # 尝试分配
                offset = cache_mgr.allocate(node.buf_id, buf.size)
                
                if offset == -1:
                    # 需要SPILL操作
                    victim = self._select_victim_buffer(
                        active_buffers, buf.type, buf.size, pos
                    )
                    
                    if victim:
                        # 创建SPILL操作
                        spill_op = SpillOperation(
                            buf_id=victim,
                            new_offset=0  # 简化：SPILL后分配到起始位置
                        )
                        spill_operations.append(spill_op)
                        
                        # 释放victim
                        cache_mgr.free(victim)
                        if victim in active_buffers:
                            del active_buffers[victim]
                        
                        # 重新尝试分配
                        offset = cache_mgr.allocate(node.buf_id, buf.size)
                
                if offset != -1:
                    allocation[node.buf_id] = offset
                    active_buffers[node.buf_id] = (buf.type, offset, buf.size)
                    
            elif node.is_free():
                if node.buf_id in active_buffers:
                    buf_type, _, _ = active_buffers[node.buf_id]
                    cache_mgr = cache_managers[buf_type]
                    cache_mgr.free(node.buf_id)
                    del active_buffers[node.buf_id]
        
        pbar.close()
        
        # 更新调度序列（插入SPILL节点）
        if spill_operations:
            self.schedule = self._insert_spill_nodes(self.schedule, spill_operations)
        
        self.memory_allocation = allocation
        self.spill_operations = spill_operations
        
        # 计算额外数据搬运量
        self._calculate_extra_data_movement()
        
        logger.info(f"[问题2] 完成 - SPILL操作数={len(spill_operations)}, "
                   f"额外数据搬运量={self.metrics['extra_data_movement']}")
        
        return allocation, spill_operations
    
    def _select_victim_buffer(self, active_buffers: Dict, cache_type: str,
                             required_size: int, current_pos: int) -> Optional[int]:
        """选择要换出的缓冲区（LRU策略）"""
        candidates = []
        
        for buf_id, (buf_type, _, size) in active_buffers.items():
            if buf_type == cache_type and size >= required_size:
                next_use = self._find_next_use(buf_id, current_pos)
                candidates.append((next_use, buf_id))
        
        if candidates:
            candidates.sort(reverse=True)
            return candidates[0][1]
        
        return None
    
    def _find_next_use(self, buf_id: int, current_pos: int) -> int:
        """找到缓冲区的下次使用位置"""
        for i in range(current_pos + 1, len(self.schedule)):
            node_id = self.schedule[i]
            node = self.nodes[node_id]
            
            if node.is_compute() and buf_id in node.bufs:
                return i
            if node.is_free() and node.buf_id == buf_id:
                return i
        
        return len(self.schedule) + 1000
    
    def _insert_spill_nodes(self, schedule: List[int], spill_ops: List[SpillOperation]) -> List[int]:
        """插入SPILL节点到调度序列"""
        new_schedule = schedule.copy()
        next_id = len(self.nodes)
        
        for spill_op in spill_ops:
            buf = self.buffers[spill_op.buf_id]
            
            # 创建SPILL_OUT节点
            spill_out_node = Node(
                id=next_id,
                op="SPILL_OUT",
                pipe="MTE3",
                cycles=self._calculate_spill_cycles(spill_op.buf_id, is_out=True),
                bufs=[spill_op.buf_id]
            )
            self.nodes[next_id] = spill_out_node
            next_id += 1
            
            # 创建SPILL_IN节点
            spill_in_node = Node(
                id=next_id,
                op="SPILL_IN",
                pipe="MTE2",
                cycles=self._calculate_spill_cycles(spill_op.buf_id, is_out=False),
                bufs=[spill_op.buf_id]
            )
            self.nodes[next_id] = spill_in_node
            next_id += 1
            
            # 简化：将SPILL节点添加到末尾
            new_schedule.append(spill_out_node.id)
            new_schedule.append(spill_in_node.id)
        
        return new_schedule
    
    def _calculate_spill_cycles(self, buf_id: int, is_out: bool) -> int:
        """计算SPILL操作的执行周期（根据附录D）"""
        buf = self.buffers[buf_id]
        has_copy_in = bool(buf.copy_in_nodes)
        
        if has_copy_in and is_out:
            # 情况2：SPILL_OUT不产生实际搬运
            return 0
        
        # 情况1或情况2的SPILL_IN
        return buf.size * SPILL_COST_FACTOR + SPILL_COST_BASE
    
    def _calculate_extra_data_movement(self):
        """计算总额外数据搬运量"""
        total = 0
        
        for spill_op in self.spill_operations:
            buf = self.buffers[spill_op.buf_id]
            has_copy_in = bool(buf.copy_in_nodes)
            
            if has_copy_in:
                # 情况2：仅SPILL_IN产生搬运
                total += buf.size
            else:
                # 情况1：SPILL_OUT和SPILL_IN都产生搬运
                total += buf.size * 2
        
        self.metrics['extra_data_movement'] = total
        self.metrics['spill_count'] = len(self.spill_operations)
    
    # ==================== 问题3：性能优化 ====================
    def solve_problem3(self) -> Tuple[List[int], Dict[int, int], List[SpillOperation]]:
        """
        问题3：性能优化策略
        目标：优化makespan，同时控制额外数据搬运量
        """
        logger.info(f"[问题3] 开始性能优化: {self.task_name}")
        
        # 获取基础方案
        base_schedule = self.solve_problem1()
        base_allocation, base_spills = self.solve_problem2(base_schedule)
        base_makespan = self._calculate_makespan(base_schedule)
        base_movement = self.metrics['extra_data_movement']
        
        logger.info(f"基础方案: Makespan={base_makespan}, 额外搬运={base_movement}")
        
        # 尝试优化策略
        best_schedule = base_schedule
        best_allocation = base_allocation
        best_spills = base_spills
        best_makespan = base_makespan
        best_movement = base_movement
        
        # 策略1：流水线优化
        pipeline_schedule = self._optimize_pipeline(base_schedule)
        if pipeline_schedule:
            self.schedule = pipeline_schedule
            p_allocation, p_spills = self.solve_problem2(pipeline_schedule)
            p_makespan = self._calculate_makespan(pipeline_schedule)
            p_movement = self.metrics['extra_data_movement']
            
            if p_makespan < best_makespan and p_movement <= base_movement * 1.2:
                best_schedule = pipeline_schedule
                best_allocation = p_allocation
                best_spills = p_spills
                best_makespan = p_makespan
                best_movement = p_movement
                logger.info(f"流水线优化成功: Makespan={p_makespan}")
        
        # 策略2：数据局部性优化
        locality_schedule = self._optimize_data_locality(base_schedule)
        if locality_schedule:
            self.schedule = locality_schedule
            l_allocation, l_spills = self.solve_problem2(locality_schedule)
            l_makespan = self._calculate_makespan(locality_schedule)
            l_movement = self.metrics['extra_data_movement']
            
            if l_makespan < best_makespan and l_movement <= base_movement * 1.2:
                best_schedule = locality_schedule
                best_allocation = l_allocation
                best_spills = l_spills
                best_makespan = l_makespan
                best_movement = l_movement
                logger.info(f"数据局部性优化成功: Makespan={l_makespan}")
        
        self.schedule = best_schedule
        self.memory_allocation = best_allocation
        self.spill_operations = best_spills
        self.metrics['makespan'] = best_makespan
        self.metrics['extra_data_movement'] = best_movement
        
        improvement = (1 - best_makespan/base_makespan) * 100 if base_makespan > 0 else 0
        logger.info(f"[问题3] 优化完成: Makespan={best_makespan} (提升{improvement:.1f}%), "
                   f"额外搬运={best_movement}")
        
        return best_schedule, best_allocation, best_spills
    
    def _calculate_makespan(self, schedule: List[int]) -> int:
        """计算总执行时间（根据附录C.1）"""
        if not schedule:
            return 0
        
        # 初始化
        start_time = {}
        end_time = {}
        unit_last_end = defaultdict(int)
        
        # 构建扩展的依赖关系（包括缓存复用依赖）
        extended_deps = defaultdict(set)
        for src, dst in self.edges:
            extended_deps[dst].add(src)
        
        # 按调度顺序计算
        for node_id in schedule:
            if node_id not in self.nodes:
                continue
            
            node = self.nodes[node_id]
            
            # 计算最早开始时间
            earliest = 0
            
            # 依赖约束
            for pred in extended_deps[node_id]:
                if pred in end_time:
                    earliest = max(earliest, end_time[pred])
            
            # 资源独占约束（同一执行单元串行）
            if node.pipe and node.cycles > 0:
                earliest = max(earliest, unit_last_end[node.pipe])
            
            # 设置时间
            start_time[node_id] = earliest
            end_time[node_id] = earliest + (node.cycles or 0)
            
            if node.pipe and node.cycles > 0:
                unit_last_end[node.pipe] = end_time[node_id]
        
        return max(end_time.values()) if end_time else 0
    
    def _optimize_pipeline(self, schedule: List[int]) -> Optional[List[int]]:
        """流水线优化：交错不同执行单元的操作"""
        # 按执行单元分组
        by_pipe = defaultdict(list)
        others = []
        
        for node_id in schedule:
            node = self.nodes[node_id]
            if node.pipe:
                by_pipe[node.pipe].append(node_id)
            else:
                others.append(node_id)
        
        # 交错排列
        optimized = []
        max_len = max((len(nodes) for nodes in by_pipe.values()), default=0)
        
        for i in range(max_len):
            for pipe in sorted(by_pipe.keys()):
                if i < len(by_pipe[pipe]):
                    optimized.append(by_pipe[pipe][i])
        
        optimized.extend(others)
        
        # 验证合法性
        if self._is_valid_schedule(optimized):
            return optimized
        
        return None
    
    def _optimize_data_locality(self, schedule: List[int]) -> Optional[List[int]]:
        """数据局部性优化：聚集使用相同缓冲区的操作"""
        # 构建缓冲区使用图
        buf_users = defaultdict(list)
        
        for node_id in schedule:
            node = self.nodes[node_id]
            if node.bufs:
                for buf_id in node.bufs:
                    buf_users[buf_id].append(node_id)
        
        # 按缓冲区聚类重排
        optimized = []
        scheduled = set()
        
        for buf_id in sorted(buf_users.keys()):
            for node_id in buf_users[buf_id]:
                if node_id not in scheduled:
                    optimized.append(node_id)
                    scheduled.add(node_id)
        
        # 添加剩余节点
        for node_id in schedule:
            if node_id not in scheduled:
                optimized.append(node_id)
        
        if self._is_valid_schedule(optimized):
            return optimized
        
        return None
    
    def _is_valid_schedule(self, schedule: List[int]) -> bool:
        """验证调度序列的合法性"""
        if len(schedule) != len(set(schedule)):
            return False
        
        position = {node_id: i for i, node_id in enumerate(schedule)}
        
        for src, dst in self.edges:
            if src in position and dst in position:
                if position[src] >= position[dst]:
                    return False
        
        return True
    
    # ==================== 可视化功能 ====================
    def visualize_pipeline(self, save_path: Optional[Path] = None):
        """可视化流水线执行图（附录C图5和图6风格）"""
        if not self.schedule:
            logger.warning("没有可用的调度序列")
            return
        
        # 计算时序信息
        time_info = self._compute_timeline()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 颜色映射
        colors = {
            'MTE1': '#FF9999',
            'MTE2': '#66B2FF',
            'MTE3': '#99FF99',
            'FIXP': '#FFFF99',
            'Cube': '#FF99CC',
            'Vector': '#CC99FF'
        }
        
        # 执行单元顺序
        pipes = ['MTE1', 'MTE2', 'MTE3', 'FIXP', 'Cube', 'Vector']
        y_pos = {pipe: i for i, pipe in enumerate(pipes)}
        
        # 绘制任务块
        for pipe, tasks in time_info.items():
            if pipe not in pipes:
                continue
                
            for node_id, start, end in tasks:
                node = self.nodes[node_id]
                
                # 绘制矩形
                rect = Rectangle(
                    (start, y_pos[pipe] - 0.4),
                    end - start,
                    0.8,
                    facecolor=colors.get(pipe, 'gray'),
                    edgecolor='black',
                    linewidth=1
                )
                ax.add_patch(rect)
                
                # 添加标签
                if end - start > 5:
                    label = f"{node.op[:6]}\n#{node_id}"
                    ax.text(start + (end - start) / 2, y_pos[pipe],
                           label, ha='center', va='center',
                           fontsize=8, fontweight='bold')
        
        # 设置坐标轴
        ax.set_ylim(-0.5, len(pipes) - 0.5)
        ax.set_yticks(range(len(pipes)))
        ax.set_yticklabels(pipes)
        ax.set_xlabel('时间 (cycles)', fontsize=11)
        ax.set_ylabel('执行单元', fontsize=11)
        ax.set_title(f'流水线执行时序图 - {self.task_name}\n'
                    f'Makespan: {self.metrics["makespan"]} cycles',
                    fontsize=13, fontweight='bold')
        ax.grid(True, axis='x', alpha=0.3)
        
        # 添加统计信息
        stats_text = (
            f"节点数: {len(self.nodes)}\n"
            f"边数: {len(self.edges)}\n"
            f"SPILL操作: {self.metrics['spill_count']}\n"
            f"额外搬运: {self.metrics['extra_data_movement']}"
        )
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
               fontsize=9, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            logger.info(f"流水线图已保存到 {save_path}")
        else:
            plt.show()
    
    def visualize_memory_usage(self, save_path: Optional[Path] = None):
        """可视化内存使用情况"""
        if not self.schedule:
            return
        
        # 计算内存使用时间线
        l1_usage = []
        ub_usage = []
        total_usage = []
        
        current_l1 = 0
        current_ub = 0
        
        for node_id in self.schedule:
            node = self.nodes[node_id]
            
            if node.is_alloc():
                if node.type == "L1":
                    current_l1 += node.size
                elif node.type == "UB":
                    current_ub += node.size
            elif node.is_free() and node.buf_id in self.buffers:
                buf = self.buffers[node.buf_id]
                if buf.type == "L1":
                    current_l1 = max(0, current_l1 - buf.size)
                elif buf.type == "UB":
                    current_ub = max(0, current_ub - buf.size)
            
            l1_usage.append(current_l1)
            ub_usage.append(current_ub)
            total_usage.append(current_l1 + current_ub)
        
        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(14, 10), sharex=True)
        
        positions = list(range(len(self.schedule)))
        
        # L1缓存使用
        ax1 = axes[0]
        ax1.plot(positions, l1_usage, 'b-', linewidth=2, label='L1使用量')
        ax1.axhline(y=CACHE_SIZES['L1'], color='r', linestyle='--', 
                   label=f'L1容量 ({CACHE_SIZES["L1"]}B)')
        ax1.fill_between(positions, 0, l1_usage, alpha=0.3, color='blue')
        ax1.set_ylabel('L1缓存 (bytes)')
        ax1.set_title(f'L1缓存使用情况 - 峰值: {max(l1_usage)}B')
        ax1.legend(loc='upper right')
        ax1.grid(True, alpha=0.3)
        
        # UB缓存使用
        ax2 = axes[1]
        ax2.plot(positions, ub_usage, 'g-', linewidth=2, label='UB使用量')
        ax2.axhline(y=CACHE_SIZES['UB'], color='r', linestyle='--',
                   label=f'UB容量 ({CACHE_SIZES["UB"]}B)')
        ax2.fill_between(positions, 0, ub_usage, alpha=0.3, color='green')
        ax2.set_ylabel('UB缓存 (bytes)')
        ax2.set_title(f'UB缓存使用情况 - 峰值: {max(ub_usage)}B')
        ax2.legend(loc='upper right')
        ax2.grid(True, alpha=0.3)
        
        # 总缓存使用（L1+UB）
        ax3 = axes[2]
        ax3.plot(positions, total_usage, 'r-', linewidth=2, label='总使用量')
        ax3.fill_between(positions, 0, total_usage, alpha=0.3, color='red')
        ax3.set_xlabel('调度位置')
        ax3.set_ylabel('总缓存 (bytes)')
        ax3.set_title(f'总缓存使用 (L1+UB) - max(V_stay): {max(total_usage)}B')
        ax3.legend(loc='upper right')
        ax3.grid(True, alpha=0.3)
        
        # 标记SPILL位置
        for i, node_id in enumerate(self.schedule):
            node = self.nodes[node_id]
            if node.is_spill_out() or node.is_spill_in():
                for ax in axes:
                    ax.axvline(x=i, color='orange', linestyle=':', 
                             alpha=0.7, linewidth=1)
        
        plt.suptitle(f'内存使用分析 - {self.task_name}', fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
        else:
            plt.show()
    
    def visualize_optimization_comparison(self, save_path: Optional[Path] = None):
        """可视化优化前后对比"""
        # 收集三个问题的指标
        metrics_data = {
            '问题': ['问题1', '问题2', '问题3'],
            'max(V_stay)': [],
            'Makespan': [],
            '额外数据搬运': [],
            'SPILL次数': []
        }
        
        # 问题1
        p1_schedule = self.solve_problem1()
        p1_makespan = self._calculate_makespan(p1_schedule)
        metrics_data['max(V_stay)'].append(self.metrics['max_v_stay'])
        metrics_data['Makespan'].append(p1_makespan)
        metrics_data['额外数据搬运'].append(0)
        metrics_data['SPILL次数'].append(0)
        
        # 问题2
        p2_allocation, p2_spills = self.solve_problem2(p1_schedule)
        p2_makespan = self._calculate_makespan(self.schedule)
        metrics_data['max(V_stay)'].append(self.metrics['max_v_stay'])
        metrics_data['Makespan'].append(p2_makespan)
        metrics_data['额外数据搬运'].append(self.metrics['extra_data_movement'])
        metrics_data['SPILL次数'].append(len(p2_spills))
        
        # 问题3
        p3_schedule, p3_allocation, p3_spills = self.solve_problem3()
        metrics_data['max(V_stay)'].append(self.metrics['max_v_stay'])
        metrics_data['Makespan'].append(self.metrics['makespan'])
        metrics_data['额外数据搬运'].append(self.metrics['extra_data_movement'])
        metrics_data['SPILL次数'].append(len(p3_spills))
        
        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # max(V_stay)对比
        ax1 = axes[0, 0]
        ax1.bar(metrics_data['问题'], metrics_data['max(V_stay)'], color=['blue', 'green', 'red'])
        ax1.set_ylabel('max(V_stay) (bytes)')
        ax1.set_title('最大缓存驻留对比')
        ax1.grid(True, alpha=0.3)
        
        # Makespan对比
        ax2 = axes[0, 1]
        ax2.bar(metrics_data['问题'], metrics_data['Makespan'], color=['blue', 'green', 'red'])
        ax2.set_ylabel('Makespan (cycles)')
        ax2.set_title('总执行时间对比')
        ax2.grid(True, alpha=0.3)
        
        # 额外数据搬运对比
        ax3 = axes[1, 0]
        ax3.bar(metrics_data['问题'], metrics_data['额外数据搬运'], color=['blue', 'green', 'red'])
        ax3.set_ylabel('额外数据搬运 (bytes)')
        ax3.set_title('额外数据搬运量对比')
        ax3.grid(True, alpha=0.3)
        
        # SPILL次数对比
        ax4 = axes[1, 1]
        ax4.bar(metrics_data['问题'], metrics_data['SPILL次数'], color=['blue', 'green', 'red'])
        ax4.set_ylabel('SPILL次数')
        ax4.set_title('SPILL操作次数对比')
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle(f'优化效果对比 - {self.task_name}', fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
        else:
            plt.show()
    
    def _compute_timeline(self) -> Dict[str, List[Tuple[int, int, int]]]:
        """计算时间线信息"""
        time_info = defaultdict(list)
        
        start_time = {}
        end_time = {}
        unit_last_end = defaultdict(int)
        
        # 构建依赖关系
        deps = defaultdict(set)
        for src, dst in self.edges:
            deps[dst].add(src)
        
        for node_id in self.schedule:
            if node_id not in self.nodes:
                continue
                
            node = self.nodes[node_id]
            
            if not node.pipe or node.cycles == 0:
                start_time[node_id] = 0
                end_time[node_id] = 0
                continue
            
            # 计算开始时间
            earliest = 0
            
            # 依赖约束
            for pred_id in deps[node_id]:
                if pred_id in end_time:
                    earliest = max(earliest, end_time[pred_id])
            
            # 资源约束
            earliest = max(earliest, unit_last_end[node.pipe])
            
            # 设置时间
            start_time[node_id] = earliest
            end_time[node_id] = earliest + node.cycles
            unit_last_end[node.pipe] = end_time[node_id]
            
            # 记录
            time_info[node.pipe].append((node_id, earliest, end_time[node_id]))
        
        self.metrics['makespan'] = max(end_time.values()) if end_time else 0
        
        return time_info
    
    # ==================== 结果输出 ====================
    def save_results(self, problem_num: int, output_dir: Path = Path("output")):
        """保存结果（严格按照附录E格式）"""
        output_dir = output_dir / f"Problem{problem_num}"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存调度序列
        schedule_file = output_dir / f"{self.task_name}_schedule.txt"
        with open(schedule_file, 'w') as f:
            for node_id in self.schedule:
                f.write(f"{node_id}\n")
        
        # 问题2和3需要保存额外信息
        if problem_num >= 2:
            # 保存内存分配
            memory_file = output_dir / f"{self.task_name}_memory.txt"
            with open(memory_file, 'w') as f:
                for buf_id in sorted(self.memory_allocation.keys()):
                    offset = self.memory_allocation[buf_id]
                    f.write(f"{buf_id}:{offset}\n")
            
            # 保存SPILL操作
            spill_file = output_dir / f"{self.task_name}_spill.txt"
            with open(spill_file, 'w') as f:
                if self.spill_operations:
                    for spill_op in self.spill_operations:
                        f.write(f"{spill_op.buf_id}:{spill_op.new_offset}\n")
                # 如果没有SPILL操作，创建空文件
        
        logger.info(f"结果已保存到 {output_dir}")
    
    def print_summary(self):
        """打印结果摘要（论文需要的表格数据）"""
        print(f"\n{'='*70}")
        print(f"任务: {self.task_name}")
        print(f"{'='*70}")
        print(f"节点数: {len(self.nodes)}")
        print(f"边数: {len(self.edges)}")
        print(f"缓冲区数: {len(self.buffers)}")
        print(f"{'-'*70}")
        print(f"问题1 - max(V_stay): {self.metrics['max_v_stay']} bytes")
        print(f"  L1峰值: {self.metrics['l1_peak']} bytes")
        print(f"  UB峰值: {self.metrics['ub_peak']} bytes")
        print(f"问题2 - 总额外数据搬运量: {self.metrics['extra_data_movement']} bytes")
        print(f"  SPILL操作数: {self.metrics['spill_count']}")
        print(f"问题3 - Makespan: {self.metrics['makespan']} cycles")
        print(f"{'='*70}\n")

# ==================== 批量处理函数 ====================
def process_all_test_cases():
    """处理所有测试案例"""
    test_cases = [
        "Matmul_Case0",
        "Matmul_Case1",
        "FlashAttention_Case0",
        "FlashAttention_Case1",
        "Conv_Case0",
        "Conv_Case1"
    ]
    
    results_summary = []
    
    print(f"\n{'='*80}")
    print("处理所有测试案例")
    print(f"{'='*80}")
    
    for case_name in test_cases:
        json_file = Path("data") / f"{case_name}.json"
        
        if not json_file.exists():
            print(f"警告: 文件 {json_file} 不存在")
            continue
        
        print(f"\n处理 {case_name}...")
        
        try:
            # 创建调度器
            scheduler = CompetitionNPUScheduler(str(json_file))
            
            # 问题1
            p1_schedule = scheduler.solve_problem1()
            p1_max_v_stay = scheduler.metrics['max_v_stay']
            scheduler.save_results(1)
            
            # 问题2
            scheduler = CompetitionNPUScheduler(str(json_file))
            p2_schedule = scheduler.solve_problem1()
            p2_allocation, p2_spills = scheduler.solve_problem2()
            p2_extra_movement = scheduler.metrics['extra_data_movement']
            scheduler.save_results(2)
            
            # 问题3
            scheduler = CompetitionNPUScheduler(str(json_file))
            p3_schedule, p3_allocation, p3_spills = scheduler.solve_problem3()
            p3_makespan = scheduler.metrics['makespan']
            p3_extra_movement = scheduler.metrics['extra_data_movement']
            scheduler.save_results(3)
            
            # 打印摘要
            scheduler.print_summary()
            
            # 生成可视化
            vis_dir = Path("visualizations") / case_name
            vis_dir.mkdir(parents=True, exist_ok=True)
            
            scheduler.visualize_pipeline(vis_dir / "pipeline.png")
            scheduler.visualize_memory_usage(vis_dir / "memory.png")
            scheduler.visualize_optimization_comparison(vis_dir / "comparison.png")
            
            # 收集结果
            results_summary.append({
                '任务名': case_name,
                '节点数': len(scheduler.nodes),
                '边数': len(scheduler.edges),
                'max(V_stay)': p1_max_v_stay,
                '总额外数据搬运量': p2_extra_movement,
                'SPILL操作数': len(p2_spills),
                '优化后Makespan': p3_makespan,
                '优化后额外搬运': p3_extra_movement
            })
            
        except Exception as e:
            print(f"错误: 处理 {case_name} 时发生异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 生成汇总表格（论文使用）
    if results_summary:
        df = pd.DataFrame(results_summary)
        print(f"\n{'='*80}")
        print("结果汇总表（论文使用）")
        print(f"{'='*80}")
        print(df.to_string(index=False))
        
        # 保存到Excel
        excel_path = Path("output") / "competition_results.xlsx"
        df.to_excel(excel_path, index=False)
        print(f"\n结果已保存到 {excel_path}")

# ==================== 主函数 ====================
def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='2025数模竞赛A题 - NPU调度器')
    parser.add_argument('--input', type=str, help='输入JSON文件路径')
    parser.add_argument('--problem', type=int, default=3, choices=[1, 2, 3],
                       help='求解的问题编号')
    parser.add_argument('--all', action='store_true', help='处理所有测试案例')
    parser.add_argument('--visualize', action='store_true', help='生成可视化')
    
    args = parser.parse_args()
    
    if args.all:
        process_all_test_cases()
    elif args.input:
        scheduler = CompetitionNPUScheduler(args.input)
        
        if args.problem == 1:
            schedule = scheduler.solve_problem1()
            scheduler.save_results(1)
        elif args.problem == 2:
            scheduler.solve_problem1()
            allocation, spills = scheduler.solve_problem2()
            scheduler.save_results(2)
        else:  # problem == 3
            schedule, allocation, spills = scheduler.solve_problem3()
            scheduler.save_results(3)
        
        scheduler.print_summary()
        
        if args.visualize:
            scheduler.visualize_pipeline()
            scheduler.visualize_memory_usage()
            scheduler.visualize_optimization_comparison()
    else:
        # 默认处理所有测试案例
        process_all_test_cases()

if __name__ == "__main__":
    main()