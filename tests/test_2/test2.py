"""
2025年中国研究生数学建模竞赛A题 - 超大显存GPU优化版 (46GB)
NPU核内调度问题 - 充分利用大显存GPU的高性能实现
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import seaborn as sns
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Set, Optional, Any
import heapq
import logging
import time
from tqdm import tqdm
import pandas as pd
from copy import deepcopy
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from numba import jit, cuda, prange, float32, int32
import numba as nb
import gc

# 尝试导入CuPy进行GPU加速
try:
    import cupy as cp
    import cupyx
    from cupyx.scipy import sparse as cp_sparse

    # 测试基本GPU功能，包括可能触发CUDA编译的操作
    test_array = cp.zeros((10, 10), dtype=cp.float32)
    test_ones = cp.ones(5, dtype=cp.float32)  # 这会触发CUDA编译
    cp.cuda.Stream.null.synchronize()

    GPU_AVAILABLE = True

    # 配置GPU内存池以充分利用46GB显存
    mempool = cp.get_default_memory_pool()
    pinned_mempool = cp.get_default_pinned_memory_pool()

    # 设置内存池大小为40GB（留6GB给系统）
    mempool.set_limit(size=40 * 1024**3)

    print(f"GPU加速已启用 - 可用显存: {cp.cuda.Device().mem_info[1] / 1024**3:.1f}GB")
    print(f"GPU内存池限制: 40GB")

except Exception as e:
    # 如果任何GPU操作失败，完全禁用GPU
    import numpy as np
    cp = np
    cp_sparse = None
    GPU_AVAILABLE = False
    print(f"GPU不可用，使用CPU模式: {str(e)[:100]}...")

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
CACHE_SIZES = {
    "L1": 4096,
    "UB": 1024,
    "L0A": 256,
    "L0B": 256,
    "L0C": 512
}

SPILL_COST_FACTOR = 2
SPILL_COST_BASE = 150

# GPU批处理大小（充分利用显存）
GPU_BATCH_SIZE = 10000  # 每批处理的节点数
MAX_PARALLEL_GRAPHS = 100  # 同时处理的图数量

# ==================== CUDA核函数 ====================
# 注意：CUDA核函数需要完整的CUDA开发环境，如果编译失败则使用CPU版本
CUDA_KERNELS_AVAILABLE = False

if GPU_AVAILABLE:
    try:
        @cuda.jit
        def cuda_compute_critical_paths(adj_matrix, node_cycles, critical_paths):
            """CUDA核函数：并行计算关键路径"""
            idx = cuda.grid(1)
            n = adj_matrix.shape[0]

            if idx < n:
                # 使用共享内存加速
                shared_cycles = cuda.shared.array(1024, dtype=int32)

                # 加载部分数据到共享内存
                if idx < 1024:
                    shared_cycles[idx] = node_cycles[idx]
                cuda.syncthreads()

                # 计算当前节点的关键路径
                max_path = node_cycles[idx]

                for j in range(n):
                    if adj_matrix[idx, j] == 1:
                        if j < 1024:
                            path = shared_cycles[j] + node_cycles[idx]
                        else:
                            path = node_cycles[j] + node_cycles[idx]

                        if path > max_path:
                            max_path = path

                critical_paths[idx] = max_path

        @cuda.jit
        def cuda_parallel_memory_allocation(sizes, capacities, allocations):
            """CUDA核函数：并行内存分配"""
            idx = cuda.grid(1)

            if idx < sizes.shape[0]:
                size = sizes[idx]
                best_fit = -1
                best_waste = 999999999

                for i in range(capacities.shape[0]):
                    if capacities[i] >= size:
                        waste = capacities[i] - size
                        if waste < best_waste:
                            best_waste = waste
                            best_fit = i

                allocations[idx] = best_fit

        # 测试CUDA编译
        test_arr = cp.zeros(10, dtype=cp.int32)
        cuda_compute_critical_paths[1, 1](cp.eye(10, dtype=cp.int32), test_arr, test_arr)
        cp.cuda.Stream.null.synchronize()

        CUDA_KERNELS_AVAILABLE = True
        print("CUDA核函数编译成功")

    except Exception as e:
        print(f"CUDA核函数编译失败，使用CPU版本: {e}")
        CUDA_KERNELS_AVAILABLE = False

# ==================== GPU优化的数据结构 ====================
class GPUGraphBatch:
    """GPU批处理图数据结构（优化内存使用）"""
    
    def __init__(self, actual_nodes=None, actual_edges=None):
        """初始化GPU批处理结构，动态分配内存"""
        if GPU_AVAILABLE:
            # 根据实际需求动态分配，而不是预分配巨大矩阵
            # 使用稀疏矩阵存储邻接关系
            self.sparse_adj_list = []  # 稀疏矩阵列表
            self.node_data = {}  # 节点数据字典
            self.batch_size = 1  # 初始批次大小
            
            logger.info(f"GPU批处理结构初始化完成（动态内存分配）")

class LargeScaleMemoryAllocator:
    """大规模GPU内存分配器"""
    
    def __init__(self, capacity: int, gpu_enabled=True):
        self.capacity = capacity
        self.gpu_enabled = gpu_enabled and GPU_AVAILABLE
        
        if self.gpu_enabled:
            # 使用GPU内存池 - 使用float32避免数据类型问题
            self.free_blocks = cp.array([[0, capacity]], dtype=cp.float32)
            self.allocated_blocks = cp.empty((0, 3), dtype=cp.float32)

            # 预分配大块内存以避免碎片
            self.memory_map = cp.zeros(capacity, dtype=cp.float32)
            self.allocation_history = cp.zeros((100000, 4), dtype=cp.float32)
            self.history_count = 0
    
    def batch_allocate(self, requests):
        """批量分配内存（GPU并行）"""
        if not self.gpu_enabled:
            return self._batch_allocate_cpu(requests)
        
        n_requests = len(requests)
        results = cp.zeros(n_requests, dtype=cp.float32)

        # 使用CUDA并行处理分配请求（如果可用）
        # 注意：由于CUDA编译问题，这里使用简化的GPU并行处理
        
        # 简化的GPU并行处理
        for i, (buf_id, size) in enumerate(requests):
            offset = self._find_best_fit_gpu(size)
            results[i] = offset
            if offset >= 0:
                self._update_allocation_gpu(buf_id, offset, size)

        return cp.asnumpy(results).astype(int)
    
    def _find_best_fit_gpu(self, size):
        """GPU上查找最佳适配"""
        if len(self.free_blocks) == 0:
            return -1
        
        valid_blocks = self.free_blocks[self.free_blocks[:, 1] >= size]
        if len(valid_blocks) == 0:
            return -1
        
        wastes = valid_blocks[:, 1] - size
        best_idx = cp.argmin(wastes)
        return int(valid_blocks[best_idx, 0])
    
    def _update_allocation_gpu(self, buf_id, offset, size):
        """更新GPU分配信息"""
        # 更新内存映射
        self.memory_map[offset:offset+size] = 1
        
        # 记录分配历史
        if self.history_count < len(self.allocation_history):
            self.allocation_history[self.history_count] = [float(buf_id), float(offset), float(size), 1.0]
            self.history_count += 1
    
    def _batch_allocate_cpu(self, requests):
        """CPU版本的批量分配"""
        results = []
        for buf_id, size in requests:
            # 简化的CPU分配
            offset = 0 if size <= self.capacity else -1
            results.append(offset)
        return results

# ==================== 数据结构定义 ====================
@dataclass
class Node:
    """节点数据结构"""
    id: int
    op: str
    buf_id: Optional[int] = None
    size: Optional[int] = None
    type: Optional[str] = None
    pipe: Optional[str] = None
    cycles: Optional[int] = None
    bufs: List[int] = field(default_factory=list)
    
    def is_alloc(self) -> bool:
        return self.op == "ALLOC"
    
    def is_free(self) -> bool:
        return self.op == "FREE"
    
    def is_compute(self) -> bool:
        return not (self.is_alloc() or self.is_free())
    
    def is_copy_in(self) -> bool:
        return self.op == "COPY_IN"
    
    def is_copy_out(self) -> bool:
        return self.op == "COPY_OUT"
    
    def is_spill_out(self) -> bool:
        return self.op == "SPILL_OUT"
    
    def is_spill_in(self) -> bool:
        return self.op == "SPILL_IN"

@dataclass
class Buffer:
    """缓冲区数据结构"""
    buf_id: int
    size: int
    type: str
    offset: int = -1
    alloc_node: Optional[int] = None
    free_node: Optional[int] = None
    producer_nodes: List[int] = field(default_factory=list)
    consumer_nodes: List[int] = field(default_factory=list)
    copy_in_nodes: List[int] = field(default_factory=list)

@dataclass
class SpillOperation:
    """SPILL操作数据结构"""
    buf_id: int
    new_offset: int

# ==================== 主调度器类 ====================
class MassiveGPUNPUScheduler:
    """充分利用46GB显存的NPU调度器"""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = Path(json_file_path)
        self.task_name = self.json_file_path.stem
        
        # 加载数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 初始化数据结构
        self.nodes: Dict[int, Node] = {}
        self.edges: List[Tuple[int, int]] = []
        self.buffers: Dict[int, Buffer] = {}
        
        # 解析数据
        self._parse_data()
        self._build_graph_structures()
        
        # 创建GPU批处理结构（根据实际大小）
        if GPU_AVAILABLE:
            actual_nodes = len(self.nodes)
            actual_edges = len(self.edges)
            logger.info(f"图规模: {actual_nodes}节点, {actual_edges}边")
            
            # 只有当节点数合理时才使用GPU
            if actual_nodes <= 50000:  # 限制最大节点数
                self.gpu_batch = GPUGraphBatch(actual_nodes, actual_edges)
                self._upload_to_gpu_batch()
            else:
                logger.warning(f"图太大({actual_nodes}节点)，使用CPU模式")
                self.gpu_batch = None
        
        # 结果存储
        self.schedule: List[int] = []
        self.memory_allocation: Dict[int, int] = {}
        self.spill_operations: List[SpillOperation] = []
        
        # 性能指标
        self.metrics = {
            'max_v_stay': 0,
            'makespan': 0,
            'extra_data_movement': 0,
            'l1_peak': 0,
            'ub_peak': 0,
            'spill_count': 0,
            'gpu_memory_used': 0
        }
    
    def _parse_data(self):
        """解析JSON数据"""
        for node_data in self.data["Nodes"]:
            node = Node(
                id=node_data["Id"],
                op=node_data["Op"],
                buf_id=node_data.get("BufId"),
                size=node_data.get("Size"),
                type=node_data.get("Type"),
                pipe=node_data.get("Pipe"),
                cycles=node_data.get("Cycles", 0),
                bufs=node_data.get("Bufs", [])
            )
            self.nodes[node.id] = node
            
            if node.is_alloc():
                if node.buf_id not in self.buffers:
                    self.buffers[node.buf_id] = Buffer(
                        buf_id=node.buf_id,
                        size=node.size,
                        type=node.type
                    )
                self.buffers[node.buf_id].alloc_node = node.id
            elif node.is_free():
                if node.buf_id in self.buffers:
                    self.buffers[node.buf_id].free_node = node.id
        
        self.edges = [(e[0], e[1]) for e in self.data["Edges"]]
    
    def _build_graph_structures(self):
        """构建图的辅助数据结构"""
        self.adj_list = defaultdict(list)
        self.reverse_adj_list = defaultdict(list)
        self.in_degree = defaultdict(int)
        
        for src, dst in self.edges:
            self.adj_list[src].append(dst)
            self.reverse_adj_list[dst].append(src)
            self.in_degree[dst] += 1
        
        for node_id, node in self.nodes.items():
            if node.is_compute() and node.bufs:
                for buf_id in node.bufs:
                    if buf_id in self.buffers:
                        if node.is_copy_in():
                            self.buffers[buf_id].copy_in_nodes.append(node_id)
                            self.buffers[buf_id].producer_nodes.append(node_id)
                        else:
                            self.buffers[buf_id].consumer_nodes.append(node_id)
    
    def _upload_to_gpu_batch(self):
        """上传数据到GPU批处理结构"""
        global GPU_AVAILABLE

        if not GPU_AVAILABLE:
            logger.info("GPU不可用，跳过GPU数据上传")
            return

        try:
            n = len(self.nodes)
            self.node_ids = sorted(self.nodes.keys())
            self.id_to_idx = {nid: i for i, nid in enumerate(self.node_ids)}

            # 创建稀疏邻接矩阵以节省内存
            row_indices = []
            col_indices = []

            for src, dst in self.edges:
                row_indices.append(self.id_to_idx[src])
                col_indices.append(self.id_to_idx[dst])

            # 使用稀疏矩阵格式 - 使用float32避免数据类型问题
            if row_indices and cp_sparse:
                self.sparse_adj = cp_sparse.csr_matrix(
                    (cp.ones(len(row_indices), dtype=cp.float32),
                     (cp.array(row_indices), cp.array(col_indices))),
                    shape=(n, n)
                )
            else:
                # 降级到CPU稀疏矩阵
                from scipy import sparse as sp_sparse
                if row_indices:
                    self.sparse_adj = sp_sparse.csr_matrix(
                        (np.ones(len(row_indices), dtype=np.float32),
                         (np.array(row_indices), np.array(col_indices))),
                        shape=(n, n)
                    )
                else:
                    self.sparse_adj = sp_sparse.csr_matrix((n, n), dtype=np.float32)

            # 上传节点属性 - 使用float32避免数据类型问题
            if GPU_AVAILABLE:
                self.gpu_node_cycles = cp.array([self.nodes[nid].cycles or 0
                                                 for nid in self.node_ids], dtype=cp.float32)
                self.gpu_node_sizes = cp.array([self.nodes[nid].size or 0
                                                for nid in self.node_ids], dtype=cp.float32)
                self.gpu_in_degrees = cp.array([self.in_degree[nid]
                                                for nid in self.node_ids], dtype=cp.float32)
            else:
                # CPU版本
                self.gpu_node_cycles = np.array([self.nodes[nid].cycles or 0
                                                 for nid in self.node_ids], dtype=np.float32)
                self.gpu_node_sizes = np.array([self.nodes[nid].size or 0
                                                for nid in self.node_ids], dtype=np.float32)
                self.gpu_in_degrees = np.array([self.in_degree[nid]
                                                for nid in self.node_ids], dtype=np.float32)

            # 预计算多个图属性
            self._precompute_gpu_properties()

            if GPU_AVAILABLE:
                self.metrics['gpu_memory_used'] = (
                    self.sparse_adj.data.nbytes +
                    self.gpu_node_cycles.nbytes +
                    self.gpu_node_sizes.nbytes
                ) / 1024**3
                logger.info(f"GPU内存使用: {self.metrics['gpu_memory_used']:.2f}GB")
            else:
                self.metrics['gpu_memory_used'] = 0
                logger.info("使用CPU模式，无GPU内存使用")

        except Exception as e:
            logger.warning(f"GPU数据上传失败，降级到CPU模式: {e}")
            # 强制使用CPU模式
            GPU_AVAILABLE = False
            self._upload_to_gpu_batch()  # 递归调用CPU版本
    
    def _precompute_gpu_properties(self):
        """预计算GPU上的图属性"""
        if not GPU_AVAILABLE:
            return
        
        n = len(self.node_ids)
        
        # 批量计算关键路径
        self.gpu_critical_paths = self._compute_critical_paths_gpu_batch()
        
        # 计算节点层级
        self.gpu_node_levels = self._compute_node_levels_gpu()
        
        # 计算数据亲和度矩阵
        self.gpu_affinity_matrix = self._compute_affinity_matrix_gpu()
        
        logger.info("GPU预计算完成")
    
    def _compute_critical_paths_gpu_batch(self):
        """批量GPU计算关键路径"""
        if not GPU_AVAILABLE:
            return self._compute_critical_paths_cpu()

        try:
            n = len(self.node_ids)
            if GPU_AVAILABLE and hasattr(cp, 'zeros'):
                critical_paths = cp.zeros(n, dtype=cp.float32)
            else:
                critical_paths = np.zeros(n, dtype=np.float32)

            # 使用GPU动态规划
            # 拓扑排序
            topo_order = self._gpu_topological_sort()

            # 反向遍历计算关键路径
            for idx in reversed(topo_order):
                node_idx = int(idx)
                max_path = float(self.gpu_node_cycles[node_idx])

                # 获取后继节点
                successors = self.sparse_adj.getrow(node_idx).indices

                for succ in successors:
                    path = float(self.gpu_node_cycles[node_idx]) + float(critical_paths[succ])
                    if path > max_path:
                        max_path = path

                critical_paths[node_idx] = max_path

            return critical_paths
        except Exception as e:
            logger.warning(f"GPU关键路径计算失败，使用CPU版本: {e}")
            return self._compute_critical_paths_cpu()

    def _compute_critical_paths_cpu(self):
        """CPU版本的关键路径计算"""
        n = len(self.node_ids)
        critical_paths = np.zeros(n, dtype=np.float32)

        # 简化的CPU拓扑排序
        topo_order = list(range(n))

        # 反向遍历计算关键路径
        for idx in reversed(topo_order):
            node_id = self.node_ids[idx]
            max_path = float(self.nodes[node_id].cycles or 0)

            # 获取后继节点
            for succ_id in self.adj_list[node_id]:
                if succ_id in self.id_to_idx:
                    succ_idx = self.id_to_idx[succ_id]
                    path = float(self.nodes[node_id].cycles or 0) + float(critical_paths[succ_idx])
                    if path > max_path:
                        max_path = path

            critical_paths[idx] = max_path

        return critical_paths
    
    def _gpu_topological_sort(self):
        """GPU拓扑排序"""
        if not GPU_AVAILABLE:
            return self._cpu_topological_sort()

        try:
            n = len(self.node_ids)
            if hasattr(self.gpu_in_degrees, 'copy'):
                in_deg = self.gpu_in_degrees.copy()
            else:
                in_deg = np.copy(self.gpu_in_degrees)
            result = []

            # 使用GPU加速的BFS
            while len(result) < n:
                # 找到所有入度为0的节点
                if GPU_AVAILABLE and hasattr(cp, 'where'):
                    zero_in_degree = cp.where(in_deg == 0)[0]
                else:
                    zero_in_degree = np.where(in_deg == 0)[0]

                if len(zero_in_degree) == 0:
                    break

                # 批量处理
                for node_idx in zero_in_degree:
                    result.append(int(node_idx))
                    in_deg[node_idx] = -1.0  # 标记为已处理

                    # 更新后继节点入度
                    successors = self.sparse_adj.getrow(int(node_idx)).indices
                    if len(successors) > 0:
                        in_deg[successors] -= 1.0

            return result
        except Exception as e:
            logger.warning(f"GPU拓扑排序失败，使用CPU版本: {e}")
            return self._cpu_topological_sort()

    def _cpu_topological_sort(self):
        """CPU版本的拓扑排序"""
        in_deg = {nid: self.in_degree[nid] for nid in self.node_ids}
        result = []
        queue = [nid for nid in self.node_ids if in_deg[nid] == 0]

        while queue:
            node_id = queue.pop(0)
            result.append(self.id_to_idx[node_id])

            for succ_id in self.adj_list[node_id]:
                if succ_id in in_deg:
                    in_deg[succ_id] -= 1
                    if in_deg[succ_id] == 0:
                        queue.append(succ_id)

        return result
    
    def _compute_node_levels_gpu(self):
        """计算节点层级（GPU）"""
        if not GPU_AVAILABLE:
            return self._compute_node_levels_cpu()

        try:
            n = len(self.node_ids)
            if GPU_AVAILABLE and hasattr(cp, 'zeros'):
                levels = cp.zeros(n, dtype=cp.float32)
            else:
                levels = np.zeros(n, dtype=np.float32)

            # BFS计算层级
            if hasattr(self.gpu_in_degrees, 'copy'):
                in_deg = self.gpu_in_degrees.copy()
            else:
                in_deg = np.copy(self.gpu_in_degrees)
            current_level = 0

            # 使用适当的数组操作
            array_module = cp if GPU_AVAILABLE and hasattr(cp, 'any') else np

            while array_module.any(in_deg >= 0):
                # 当前层级的节点
                current_nodes = array_module.where(in_deg == 0)[0]

                if len(current_nodes) == 0:
                    break

                levels[current_nodes] = float(current_level)

                # 标记为已处理
                in_deg[current_nodes] = -1.0

                # 更新后继节点
                for node_idx in current_nodes:
                    successors = self.sparse_adj.getrow(int(node_idx)).indices
                    if len(successors) > 0:
                        in_deg[successors] -= 1.0

                current_level += 1

            return levels
        except Exception as e:
            logger.warning(f"GPU节点层级计算失败，使用CPU版本: {e}")
            return self._compute_node_levels_cpu()

    def _compute_node_levels_cpu(self):
        """CPU版本的节点层级计算"""
        n = len(self.node_ids)
        levels = np.zeros(n, dtype=np.float32)

        # 简化的层级计算
        for i, node_id in enumerate(self.node_ids):
            # 计算到根节点的最大距离
            level = 0
            visited = set()
            queue = [(node_id, 0)]

            while queue:
                current, dist = queue.pop(0)
                if current in visited:
                    continue
                visited.add(current)
                level = max(level, dist)

                for pred in self.reverse_adj_list[current]:
                    if pred not in visited:
                        queue.append((pred, dist + 1))

            levels[i] = float(level)

        return levels
    
    def _compute_affinity_matrix_gpu(self):
        """计算数据亲和度矩阵（GPU）- 内存优化版本"""
        if not GPU_AVAILABLE:
            return None
        
        n = len(self.node_ids)
        
        # 对于大图，使用稀疏矩阵
        if n > 5000:
            logger.info(f"使用稀疏亲和度矩阵（节点数={n}）")
            # 只存储非零元素
            affinity_data = []
            affinity_rows = []
            affinity_cols = []
            
            for buf_id, buffer in self.buffers.items():
                users = (buffer.producer_nodes + buffer.consumer_nodes + 
                        buffer.copy_in_nodes)
                
                if len(users) > 1:
                    user_indices = [self.id_to_idx[uid] for uid in users 
                                  if uid in self.id_to_idx]
                    
                    for i in user_indices:
                        for j in user_indices:
                            if i != j:
                                affinity_data.append(buffer.size / 1000.0)
                                affinity_rows.append(i)
                                affinity_cols.append(j)
            
            if affinity_data:
                self.sparse_affinity = cp_sparse.csr_matrix(
                    (cp.array(affinity_data, dtype=cp.float32),
                     (cp.array(affinity_rows), cp.array(affinity_cols))),
                    shape=(n, n)
                )
            else:
                self.sparse_affinity = cp_sparse.csr_matrix((n, n), dtype=cp.float32)
            
            return self.sparse_affinity
        
        else:
            # 小图可以使用密集矩阵
            affinity = cp.zeros((n, n), dtype=cp.float32)
            
            for buf_id, buffer in self.buffers.items():
                users = (buffer.producer_nodes + buffer.consumer_nodes + 
                        buffer.copy_in_nodes)
                
                if len(users) > 1:
                    user_indices = [self.id_to_idx[uid] for uid in users 
                                  if uid in self.id_to_idx]
                    
                    for i in user_indices:
                        for j in user_indices:
                            if i != j:
                                affinity[i, j] += buffer.size / 1000.0
            
            return affinity
    
    def solve_problem1_massive_gpu(self) -> List[int]:
        """问题1：使用大规模GPU并行求解"""
        logger.info(f"[问题1-大规模GPU] 开始求解: {self.task_name}")

        if not GPU_AVAILABLE:
            schedule = self._solve_problem1_cpu()
            self.schedule = schedule
            logger.info(f"[问题1-CPU] 完成 - max(V_stay)={self.metrics['max_v_stay']}")
            return schedule

        n = len(self.nodes)

        # GPU上的优先级计算
        priorities = self._compute_priorities_massive_gpu()

        # 使用GPU加速的调度算法
        schedule = self._gpu_accelerated_scheduling(priorities)

        # 计算缓存使用
        self._compute_cache_usage_gpu(schedule)

        self.schedule = schedule
        logger.info(f"[问题1-大规模GPU] 完成 - max(V_stay)={self.metrics['max_v_stay']}")

        return schedule
    
    def _compute_priorities_massive_gpu(self):
        """大规模GPU优先级计算"""
        n = len(self.node_ids)
        
        # 综合多个因素计算优先级
        priorities = cp.zeros(n, dtype=cp.float32)
        
        # 1. 关键路径因素
        if hasattr(self, 'gpu_critical_paths'):
            priorities -= self.gpu_critical_paths.astype(cp.float32)
        
        # 2. 层级因素
        if hasattr(self, 'gpu_node_levels'):
            priorities += self.gpu_node_levels.astype(cp.float32) * 100
        
        # 3. 节点类型因素
        for i, node_id in enumerate(self.node_ids):
            node = self.nodes[node_id]
            
            if node.is_alloc():
                priorities[i] -= 3000
                if "L0" in (node.type or ""):
                    priorities[i] -= 2000
            elif node.is_free():
                priorities[i] -= 1000
            elif node.is_copy_in():
                priorities[i] -= 500
            elif node.is_copy_out():
                priorities[i] += 1000
        
        return cp.asnumpy(priorities)
    
    def _gpu_accelerated_scheduling(self, priorities):
        """GPU加速的调度生成"""
        schedule = []
        scheduled = set()
        ready_queue = []
        
        # 初始化就绪队列
        for i, node_id in enumerate(self.node_ids):
            if self.in_degree[node_id] == 0:
                heapq.heappush(ready_queue, (priorities[i], node_id))
        
        # L0缓存占用跟踪
        l0_occupied = {"L0A": None, "L0B": None, "L0C": None}
        
        pbar = tqdm(total=len(self.nodes), desc=f"GPU调度")
        
        while ready_queue:
            _, node_id = heapq.heappop(ready_queue)
            
            if node_id in scheduled:
                continue
            
            node = self.nodes[node_id]
            
            # L0约束检查
            if node.type in ["L0A", "L0B", "L0C"]:
                if node.is_alloc():
                    if l0_occupied[node.type] is not None:
                        # 延迟调度
                        idx = self.id_to_idx[node_id]
                        heapq.heappush(ready_queue, (priorities[idx] + 10000, node_id))
                        continue
                    l0_occupied[node.type] = node.buf_id
                elif node.is_free() and node.buf_id in self.buffers:
                    buf = self.buffers[node.buf_id]
                    if buf.type in l0_occupied:
                        l0_occupied[buf.type] = None
            
            schedule.append(node_id)
            scheduled.add(node_id)
            pbar.update(1)
            
            # 更新后继节点
            for successor in self.adj_list[node_id]:
                if successor not in scheduled:
                    all_preds_scheduled = all(
                        pred in scheduled for pred in self.reverse_adj_list[successor]
                    )
                    if all_preds_scheduled:
                        idx = self.id_to_idx[successor]
                        heapq.heappush(ready_queue, (priorities[idx], successor))
        
        pbar.close()
        return schedule
    
    def _compute_cache_usage_gpu(self, schedule):
        """GPU计算缓存使用情况"""
        current_cache = defaultdict(int)
        max_cache = defaultdict(int)
        total_max = 0
        
        for node_id in schedule:
            node = self.nodes[node_id]
            
            if node.is_alloc() and node.type in ["L1", "UB"]:
                current_cache[node.type] += node.size
                max_cache[node.type] = max(max_cache[node.type], current_cache[node.type])
                total_max = max(total_max, current_cache["L1"] + current_cache["UB"])
            elif node.is_free() and node.buf_id in self.buffers:
                buf = self.buffers[node.buf_id]
                if buf.type in ["L1", "UB"]:
                    current_cache[buf.type] = max(0, current_cache[buf.type] - buf.size)
        
        self.metrics['max_v_stay'] = total_max
        self.metrics['l1_peak'] = max_cache["L1"]
        self.metrics['ub_peak'] = max_cache["UB"]
    
    def solve_problem2_massive_gpu(self, base_schedule=None):
        """问题2：大规模GPU内存分配"""
        logger.info(f"[问题2-大规模GPU] 开始求解缓存分配")
        
        if base_schedule:
            self.schedule = base_schedule
        elif not self.schedule:
            self.solve_problem1_massive_gpu()
        
        # 创建GPU内存分配器
        allocators = {
            cache_type: LargeScaleMemoryAllocator(CACHE_SIZES[cache_type], GPU_AVAILABLE)
            for cache_type in CACHE_SIZES
        }
        
        # 批量收集分配请求
        allocation_requests = defaultdict(list)
        spill_operations = []
        
        # 预处理：收集所有分配请求
        for node_id in self.schedule:
            node = self.nodes[node_id]
            if node.is_alloc():
                buf = self.buffers[node.buf_id]
                allocation_requests[buf.type].append((node.buf_id, buf.size))
        
        # GPU批量分配
        allocation_results = {}
        for cache_type, requests in allocation_requests.items():
            if requests:
                allocator = allocators[cache_type]
                results = allocator.batch_allocate(requests)
                
                for (buf_id, _), offset in zip(requests, results):
                    if offset >= 0:
                        allocation_results[buf_id] = offset
                    else:
                        # 需要SPILL
                        spill_op = SpillOperation(buf_id=buf_id, new_offset=0)
                        spill_operations.append(spill_op)
        
        self.memory_allocation = allocation_results
        self.spill_operations = spill_operations
        
        self._calculate_extra_data_movement()
        
        logger.info(f"[问题2-大规模GPU] 完成 - SPILL={len(spill_operations)}")
        
        return allocation_results, spill_operations
    
    def solve_problem3_massive_gpu(self):
        """问题3：大规模GPU性能优化"""
        logger.info(f"[问题3-大规模GPU] 开始性能优化")
        
        # 基础方案
        base_schedule = self.solve_problem1_massive_gpu()
        base_allocation, base_spills = self.solve_problem2_massive_gpu(base_schedule)
        base_makespan = self._calculate_makespan_gpu(base_schedule)
        
        # 使用GPU并行尝试多种优化策略
        strategies = [
            self._optimize_with_affinity_gpu,
            self._optimize_with_levels_gpu,
            self._optimize_hybrid_gpu
        ]
        
        best_schedule = base_schedule
        best_makespan = base_makespan
        
        for strategy in strategies:
            try:
                opt_schedule = strategy(base_schedule)
                if opt_schedule:
                    opt_makespan = self._calculate_makespan_gpu(opt_schedule)
                    if opt_makespan < best_makespan:
                        best_schedule = opt_schedule
                        best_makespan = opt_makespan
            except Exception as e:
                logger.debug(f"策略失败: {e}")
        
        self.schedule = best_schedule
        self.metrics['makespan'] = best_makespan
        
        improvement = (1 - best_makespan/base_makespan) * 100 if base_makespan > 0 else 0
        logger.info(f"[问题3-大规模GPU] Makespan={best_makespan} (提升{improvement:.1f}%)")
        
        return best_schedule, self.memory_allocation, self.spill_operations
    
    def _optimize_with_affinity_gpu(self, schedule):
        """基于GPU亲和度矩阵优化 - 内存优化版本"""
        if not hasattr(self, 'sparse_affinity') and not hasattr(self, 'gpu_affinity_matrix'):
            return None
        
        n = len(schedule)
        new_order = []
        remaining = set(schedule)
        
        # 使用稀疏矩阵版本
        use_sparse = hasattr(self, 'sparse_affinity')
        
        while remaining:
            if not new_order:
                node = remaining.pop()
                new_order.append(node)
            else:
                best_node = None
                best_affinity = -1
                
                # 批量计算亲和度，避免重复GPU传输
                candidates = list(remaining)[:100]  # 限制候选数量
                
                for candidate in candidates:
                    cand_idx = self.id_to_idx[candidate]
                    total_affinity = 0
                    
                    for scheduled in new_order[-10:]:  # 只考虑最近的10个节点
                        sched_idx = self.id_to_idx[scheduled]
                        
                        if use_sparse:
                            # 稀疏矩阵访问
                            total_affinity += float(
                                self.sparse_affinity[cand_idx, sched_idx]
                            )
                        elif hasattr(self, 'gpu_affinity_matrix'):
                            # 密集矩阵访问
                            total_affinity += float(
                                self.gpu_affinity_matrix[cand_idx, sched_idx]
                            )
                    
                    if total_affinity > best_affinity:
                        best_affinity = total_affinity
                        best_node = candidate
                
                if best_node:
                    remaining.remove(best_node)
                    new_order.append(best_node)
                else:
                    # 如果没有找到，选择任意一个
                    node = remaining.pop()
                    new_order.append(node)
        
        if self._is_valid_schedule(new_order):
            return new_order
        
        return None
    
    def _optimize_with_levels_gpu(self, schedule):
        """基于GPU层级信息优化"""
        if not hasattr(self, 'gpu_node_levels'):
            return None
        
        # 按层级重排，同层内按优先级
        level_groups = defaultdict(list)
        
        for node_id in schedule:
            idx = self.id_to_idx[node_id]
            level = int(self.gpu_node_levels[idx])
            level_groups[level].append(node_id)
        
        new_order = []
        for level in sorted(level_groups.keys()):
            new_order.extend(level_groups[level])
        
        if self._is_valid_schedule(new_order):
            return new_order
        
        return None
    
    def _optimize_hybrid_gpu(self, schedule):
        """混合GPU优化策略"""
        # 先层级优化
        temp = self._optimize_with_levels_gpu(schedule)
        if temp:
            # 再亲和度优化
            final = self._optimize_with_affinity_gpu(temp)
            if final:
                return final
            return temp
        
        return None
    
    def _calculate_makespan_gpu(self, schedule):
        """GPU加速的makespan计算"""
        if not schedule:
            return 0
        
        if GPU_AVAILABLE:
            # 使用GPU并行计算
            n = len(schedule)
            start_times = cp.zeros(n, dtype=cp.float32)
            end_times = cp.zeros(n, dtype=cp.float32)
            
            schedule_pos = {node_id: i for i, node_id in enumerate(schedule)}
            unit_last_end = {}
            
            for i, node_id in enumerate(schedule):
                node = self.nodes[node_id]
                
                # 依赖约束
                earliest = 0
                for pred in self.reverse_adj_list[node_id]:
                    if pred in schedule_pos:
                        pred_pos = schedule_pos[pred]
                        if pred_pos < i:
                            earliest = max(earliest, float(end_times[pred_pos]))

                # 资源约束
                if node.pipe and node.cycles > 0:
                    if node.pipe in unit_last_end:
                        earliest = max(earliest, unit_last_end[node.pipe])

                start_times[i] = float(earliest)
                end_times[i] = float(earliest + (node.cycles or 0))

                if node.pipe and node.cycles > 0:
                    unit_last_end[node.pipe] = float(end_times[i])
            
            return int(cp.max(end_times))
        else:
            return self._calculate_makespan_cpu(schedule)
    
    def _calculate_makespan_cpu(self, schedule):
        """CPU版本的makespan计算"""
        start_time = {}
        end_time = {}
        unit_last_end = defaultdict(int)
        
        for node_id in schedule:
            node = self.nodes[node_id]
            
            earliest = 0
            for pred in self.reverse_adj_list[node_id]:
                if pred in end_time:
                    earliest = max(earliest, end_time[pred])
            
            if node.pipe and node.cycles > 0:
                earliest = max(earliest, unit_last_end[node.pipe])
            
            start_time[node_id] = earliest
            end_time[node_id] = earliest + (node.cycles or 0)
            
            if node.pipe and node.cycles > 0:
                unit_last_end[node.pipe] = end_time[node_id]
        
        return max(end_time.values()) if end_time else 0
    
    def _is_valid_schedule(self, schedule):
        """验证调度合法性"""
        if len(schedule) != len(set(schedule)):
            return False
        
        position = {node_id: i for i, node_id in enumerate(schedule)}
        
        for src, dst in self.edges:
            if src in position and dst in position:
                if position[src] >= position[dst]:
                    return False
        
        return True
    
    def _calculate_extra_data_movement(self):
        """计算额外数据搬运量"""
        total = 0
        
        for spill_op in self.spill_operations:
            buf = self.buffers[spill_op.buf_id]
            has_copy_in = bool(buf.copy_in_nodes)
            
            if has_copy_in:
                total += buf.size
            else:
                total += buf.size * 2
        
        self.metrics['extra_data_movement'] = total
        self.metrics['spill_count'] = len(self.spill_operations)
    
    def _solve_problem1_cpu(self):
        """CPU版本的问题1求解（降级方案）"""
        logger.info("使用CPU版本求解")

        # 拓扑排序生成基础调度
        schedule = []
        in_degree = self.in_degree.copy()
        ready_queue = []

        # 初始化就绪队列
        for node_id in self.nodes:
            if in_degree[node_id] == 0:
                ready_queue.append(node_id)

        # 拓扑排序
        while ready_queue:
            node_id = ready_queue.pop(0)
            schedule.append(node_id)

            # 更新后继节点
            for successor in self.adj_list[node_id]:
                in_degree[successor] -= 1
                if in_degree[successor] == 0:
                    ready_queue.append(successor)

        # 计算缓存使用
        self._compute_cache_usage_cpu(schedule)

        return schedule

    def _compute_cache_usage_cpu(self, schedule):
        """CPU版本的缓存使用计算"""
        current_cache = {"L1": 0, "UB": 0}
        max_cache = {"L1": 0, "UB": 0}
        total_max = 0

        for node_id in schedule:
            node = self.nodes[node_id]

            if node.is_alloc() and node.type in ["L1", "UB"]:
                current_cache[node.type] += node.size
                max_cache[node.type] = max(max_cache[node.type], current_cache[node.type])
                total_max = max(total_max, current_cache["L1"] + current_cache["UB"])
            elif node.is_free() and node.buf_id in self.buffers:
                buf = self.buffers[node.buf_id]
                if buf.type in ["L1", "UB"]:
                    current_cache[buf.type] = max(0, current_cache[buf.type] - buf.size)

        self.metrics['max_v_stay'] = total_max
        self.metrics['l1_peak'] = max_cache["L1"]
        self.metrics['ub_peak'] = max_cache["UB"]
    
    def cleanup_gpu_memory(self):
        """清理GPU内存"""
        if GPU_AVAILABLE:
            # 清理GPU内存池
            mempool.free_all_blocks()
            pinned_mempool.free_all_blocks()
            cp.cuda.MemoryPool().free_all_blocks()
            gc.collect()
            logger.info("GPU内存已清理")
    
    def save_results(self, problem_num: int, output_dir: Path = Path("output")):
        """保存结果"""
        output_dir = output_dir / f"Problem{problem_num}"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        schedule_file = output_dir / f"{self.task_name}_schedule.txt"
        with open(schedule_file, 'w') as f:
            for node_id in self.schedule:
                f.write(f"{node_id}\n")
        
        if problem_num >= 2:
            memory_file = output_dir / f"{self.task_name}_memory.txt"
            with open(memory_file, 'w') as f:
                for buf_id in sorted(self.memory_allocation.keys()):
                    offset = self.memory_allocation[buf_id]
                    f.write(f"{buf_id}:{offset}\n")
            
            spill_file = output_dir / f"{self.task_name}_spill.txt"
            with open(spill_file, 'w') as f:
                if self.spill_operations:
                    for spill_op in self.spill_operations:
                        f.write(f"{spill_op.buf_id}:{spill_op.new_offset}\n")
        
        logger.info(f"结果已保存到 {output_dir}")
    
    def print_summary(self):
        """打印结果摘要"""
        print(f"\n{'='*70}")
        print(f"任务: {self.task_name} (大规模GPU - 46GB)")
        print(f"{'='*70}")
        print(f"节点数: {len(self.nodes)}")
        print(f"边数: {len(self.edges)}")
        print(f"缓冲区数: {len(self.buffers)}")
        print(f"GPU状态: {'启用' if GPU_AVAILABLE else '未启用'}")
        print(f"GPU内存使用: {self.metrics['gpu_memory_used']:.2f}GB")
        print(f"{'-'*70}")
        print(f"问题1 - max(V_stay): {self.metrics['max_v_stay']} bytes")
        print(f"  L1峰值: {self.metrics['l1_peak']} bytes")
        print(f"  UB峰值: {self.metrics['ub_peak']} bytes")
        print(f"问题2 - 总额外数据搬运量: {self.metrics['extra_data_movement']} bytes")
        print(f"  SPILL操作数: {self.metrics['spill_count']}")
        print(f"问题3 - Makespan: {self.metrics['makespan']} cycles")
        print(f"{'='*70}\n")

# ==================== 批量处理函数 ====================
def process_batch_gpu(json_files, problem_num=3):
    """批量GPU处理多个文件"""
    results = []
    
    # 预热GPU
    if GPU_AVAILABLE:
        _ = cp.zeros((1000, 1000))
        cp.cuda.Stream.null.synchronize()
    
    for json_file in json_files:
        try:
            logger.info(f"处理: {json_file.name}")
            scheduler = MassiveGPUNPUScheduler(str(json_file))
            
            if problem_num == 1:
                scheduler.solve_problem1_massive_gpu()
            elif problem_num == 2:
                scheduler.solve_problem1_massive_gpu()
                scheduler.solve_problem2_massive_gpu()
            else:
                scheduler.solve_problem3_massive_gpu()
            
            scheduler.save_results(problem_num)
            scheduler.print_summary()
            
            results.append({
                'file': json_file.name,
                'success': True,
                'metrics': scheduler.metrics.copy()
            })
            
            # 清理GPU内存
            scheduler.cleanup_gpu_memory()
            
        except Exception as e:
            logger.error(f"处理 {json_file.name} 失败: {e}")
            results.append({
                'file': json_file.name,
                'success': False,
                'error': str(e)
            })
    
    return results

# ==================== 主函数 ====================
def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='2025数模竞赛A题 - 46GB GPU优化版')
    parser.add_argument('--input', type=str, help='输入JSON文件或目录')
    parser.add_argument('--problem', type=int, default=3, choices=[1, 2, 3])
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    
    args = parser.parse_args()
    
    if args.input:
        input_path = Path(args.input)
        
        if input_path.is_file():
            scheduler = MassiveGPUNPUScheduler(str(input_path))
            
            if args.problem == 1:
                scheduler.solve_problem1_massive_gpu()
            elif args.problem == 2:
                scheduler.solve_problem1_massive_gpu()
                scheduler.solve_problem2_massive_gpu()
            else:
                scheduler.solve_problem3_massive_gpu()
            
            scheduler.save_results(args.problem)
            scheduler.print_summary()
            scheduler.cleanup_gpu_memory()
            
        elif input_path.is_dir():
            json_files = list(input_path.glob("*.json"))
            
            if args.batch:
                # 批量GPU处理
                results = process_batch_gpu(json_files, args.problem)
            else:
                # 逐个处理
                for json_file in json_files:
                    scheduler = MassiveGPUNPUScheduler(str(json_file))
                    scheduler.solve_problem3_massive_gpu()
                    scheduler.save_results(3)
                    scheduler.print_summary()
                    scheduler.cleanup_gpu_memory()
    else:
        print("请提供输入文件或目录")
        print("用法: python script.py --input data/ --problem 3 --batch")

if __name__ == "__main__":
    main()