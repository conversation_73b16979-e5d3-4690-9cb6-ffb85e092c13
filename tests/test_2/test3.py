"""
2025年中国研究生数学建模竞赛A题 - 完整解决方案
通用神经网络处理器下的核内调度问题
"""

# ================================ 第一部分：主调度器 ================================

import json
import heapq
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Set, Optional, Any
import logging
import time
import pandas as pd
import seaborn as sns
from copy import deepcopy

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
CACHE_SIZES = {
    "L1": 4096,
    "UB": 1024,
    "L0A": 256,
    "L0B": 256,
    "L0C": 512
}

# SPILL操作耗时计算参数
SPILL_COST_FACTOR = 2
SPILL_COST_BASE = 150

# ==================== 数据结构定义 ====================
@dataclass
class Node:
    """节点数据结构"""
    id: int
    op: str
    buf_id: Optional[int] = None
    size: Optional[int] = None
    type: Optional[str] = None
    pipe: Optional[str] = None
    cycles: Optional[int] = None
    bufs: List[int] = field(default_factory=list)
    
    def is_alloc(self) -> bool:
        return self.op == "ALLOC"
    
    def is_free(self) -> bool:
        return self.op == "FREE"
    
    def is_compute(self) -> bool:
        return not (self.is_alloc() or self.is_free())
    
    def is_copy_in(self) -> bool:
        return self.op == "COPY_IN"
    
    def is_copy_out(self) -> bool:
        return self.op == "COPY_OUT"
    
    def is_spill_out(self) -> bool:
        return self.op == "SPILL_OUT"
    
    def is_spill_in(self) -> bool:
        return self.op == "SPILL_IN"

@dataclass
class Buffer:
    """缓冲区数据结构"""
    buf_id: int
    size: int
    type: str
    offset: int = -1
    alloc_node: Optional[int] = None
    free_node: Optional[int] = None
    producer_nodes: List[int] = field(default_factory=list)
    consumer_nodes: List[int] = field(default_factory=list)
    copy_in_nodes: List[int] = field(default_factory=list)
    is_spilled: bool = False
    spill_offset: int = -1

@dataclass
class SpillOperation:
    """SPILL操作数据结构"""
    buf_id: int
    new_offset: int
    spill_out_node_id: int
    spill_in_node_id: int
    position_in_schedule: int

@dataclass
class ScheduleMetrics:
    """调度指标"""
    max_cache: int = 0
    makespan: int = 0
    extra_data_movement: int = 0
    spill_count: int = 0
    l1_peak_usage: int = 0
    ub_peak_usage: int = 0
    parallelism: float = 0.0

# ==================== 缓存管理器 ====================
class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_type: str, capacity: int):
        self.cache_type = cache_type
        self.capacity = capacity
        self.free_blocks = [(0, capacity)]  # (offset, size)列表
        self.allocated_blocks = {}  # buf_id -> (offset, size)
        self.current_usage = 0
    
    def allocate(self, buf_id: int, size: int) -> int:
        """分配内存，返回偏移地址，失败返回-1"""
        if size > self.capacity:
            return -1
        
        # 最佳适应算法
        best_idx = -1
        best_offset = -1
        best_waste = float('inf')
        
        for i, (offset, block_size) in enumerate(self.free_blocks):
            if block_size >= size:
                waste = block_size - size
                if waste < best_waste:
                    best_waste = waste
                    best_idx = i
                    best_offset = offset
        
        if best_idx == -1:
            return -1
        
        # 分配内存
        offset, block_size = self.free_blocks[best_idx]
        self.allocated_blocks[buf_id] = (offset, size)
        self.current_usage += size
        
        # 更新空闲块
        if block_size == size:
            del self.free_blocks[best_idx]
        else:
            self.free_blocks[best_idx] = (offset + size, block_size - size)
        
        return offset
    
    def free(self, buf_id: int) -> bool:
        """释放内存"""
        if buf_id not in self.allocated_blocks:
            return False
        
        offset, size = self.allocated_blocks[buf_id]
        del self.allocated_blocks[buf_id]
        self.current_usage -= size
        
        # 添加到空闲列表
        self.free_blocks.append((offset, size))
        
        # 合并相邻空闲块
        self._merge_free_blocks()
        
        return True
    
    def _merge_free_blocks(self):
        """合并相邻的空闲块"""
        if not self.free_blocks:
            return
        
        self.free_blocks.sort()
        merged = [self.free_blocks[0]]
        
        for offset, size in self.free_blocks[1:]:
            last_offset, last_size = merged[-1]
            if last_offset + last_size == offset:
                # 相邻，合并
                merged[-1] = (last_offset, last_size + size)
            else:
                merged.append((offset, size))
        
        self.free_blocks = merged
    
    def get_fragmentation(self) -> float:
        """计算碎片率"""
        if self.current_usage == 0:
            return 0.0
        
        # 最大连续空闲块
        max_free = max((size for _, size in self.free_blocks), default=0)
        total_free = self.capacity - self.current_usage
        
        if total_free == 0:
            return 0.0
        
        return 1.0 - (max_free / total_free)
    
    def can_allocate(self, size: int) -> bool:
        """检查是否可以分配指定大小的内存"""
        return any(block_size >= size for _, block_size in self.free_blocks)

# ==================== 核心调度器类 ====================
class NPUScheduler:
    """NPU调度器主类"""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = Path(json_file_path)
        self.task_name = self.json_file_path.stem
        
        # 加载数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 初始化数据结构
        self.nodes: Dict[int, Node] = {}
        self.edges: List[Tuple[int, int]] = []
        self.buffers: Dict[int, Buffer] = {}
        
        # 解析数据
        self._parse_data()
        
        # 构建图结构
        self._build_graph_structures()
        
        # 结果存储
        self.schedule: List[int] = []
        self.memory_allocation: Dict[int, int] = {}
        self.spill_operations: List[SpillOperation] = []
        self.metrics = ScheduleMetrics()
        
        # 调度策略参数
        self.enable_prefetch = False
        self.enable_reorder = True
        self.spill_threshold = 0.9  # 缓存使用超过90%时考虑SPILL
    
    def _parse_data(self):
        """解析JSON数据"""
        # 解析节点
        for node_data in self.data["Nodes"]:
            node = Node(
                id=node_data["Id"],
                op=node_data["Op"],
                buf_id=node_data.get("BufId"),
                size=node_data.get("Size"),
                type=node_data.get("Type"),
                pipe=node_data.get("Pipe"),
                cycles=node_data.get("Cycles", 0),
                bufs=node_data.get("Bufs", [])
            )
            self.nodes[node.id] = node
            
            # 创建缓冲区对象
            if node.is_alloc():
                if node.buf_id not in self.buffers:
                    self.buffers[node.buf_id] = Buffer(
                        buf_id=node.buf_id,
                        size=node.size,
                        type=node.type
                    )
                self.buffers[node.buf_id].alloc_node = node.id
            elif node.is_free():
                if node.buf_id in self.buffers:
                    self.buffers[node.buf_id].free_node = node.id
        
        # 解析边
        self.edges = [(e[0], e[1]) for e in self.data["Edges"]]
    
    def _build_graph_structures(self):
        """构建图的辅助数据结构"""
        # 构建邻接表
        self.adj_list = defaultdict(list)
        self.reverse_adj_list = defaultdict(list)
        
        for src, dst in self.edges:
            self.adj_list[src].append(dst)
            self.reverse_adj_list[dst].append(src)
        
        # 计算入度
        self.in_degree = defaultdict(int)
        for _, dst in self.edges:
            self.in_degree[dst] += 1
        
        # 关联缓冲区与操作节点
        for node_id, node in self.nodes.items():
            if node.is_compute() and node.bufs:
                for buf_id in node.bufs:
                    if buf_id in self.buffers:
                        if node.is_copy_in():
                            self.buffers[buf_id].copy_in_nodes.append(node_id)
                            self.buffers[buf_id].producer_nodes.append(node_id)
                        else:
                            self.buffers[buf_id].consumer_nodes.append(node_id)
    
    # ==================== 问题1：最小缓存驻留调度 ====================
    def solve_min_cache_schedule(self) -> List[int]:
        """
        问题1：最小缓存驻留调度
        目标：最小化max(V_stay)
        算法：改进的贪心算法+关键路径分析
        时间复杂度: O(N^2 * log N)
        """
        logger.info(f"[问题1] 开始求解最小缓存驻留调度: {self.task_name}")
        
        # 初始化
        schedule = []
        scheduled = set()
        ready_queue = []
        
        # 缓存使用跟踪
        current_l1 = 0
        current_ub = 0
        max_l1 = 0
        max_ub = 0
        max_total = 0
        
        # L0缓存跟踪（确保同时只有一个缓冲区）
        l0a_occupied = False
        l0b_occupied = False
        l0c_occupied = False
        
        # 计算每个节点的优先级权重
        node_priorities = self._compute_node_priorities()
        
        # 初始化就绪队列（入度为0的节点）
        for node_id in self.nodes:
            if self.in_degree[node_id] == 0:
                priority = node_priorities[node_id]
                heapq.heappush(ready_queue, (priority, node_id))
        
        # 主调度循环
        while ready_queue:
            _, node_id = heapq.heappop(ready_queue)
            
            if node_id in scheduled:
                continue
            
            node = self.nodes[node_id]
            
            # 检查L0约束
            if node.type in ["L0A", "L0B", "L0C"]:
                if node.is_alloc():
                    if (node.type == "L0A" and l0a_occupied) or \
                       (node.type == "L0B" and l0b_occupied) or \
                       (node.type == "L0C" and l0c_occupied):
                        # L0已被占用，延后调度
                        heapq.heappush(ready_queue, (node_priorities[node_id] + 1000, node_id))
                        continue
                    
                    # 标记L0占用
                    if node.type == "L0A":
                        l0a_occupied = True
                    elif node.type == "L0B":
                        l0b_occupied = True
                    elif node.type == "L0C":
                        l0c_occupied = True
                
                elif node.is_free():
                    # 释放L0
                    buf = self.buffers.get(node.buf_id)
                    if buf:
                        if buf.type == "L0A":
                            l0a_occupied = False
                        elif buf.type == "L0B":
                            l0b_occupied = False
                        elif buf.type == "L0C":
                            l0c_occupied = False
            
            # 添加到调度序列
            schedule.append(node_id)
            scheduled.add(node_id)
            
            # 更新缓存使用统计
            if node.is_alloc():
                if node.type == "L1":
                    current_l1 += node.size
                    max_l1 = max(max_l1, current_l1)
                elif node.type == "UB":
                    current_ub += node.size
                    max_ub = max(max_ub, current_ub)
                max_total = max(max_total, current_l1 + current_ub)
                
            elif node.is_free():
                buf = self.buffers.get(node.buf_id)
                if buf:
                    if buf.type == "L1":
                        current_l1 = max(0, current_l1 - buf.size)
                    elif buf.type == "UB":
                        current_ub = max(0, current_ub - buf.size)
            
            # 更新后继节点的就绪状态
            for successor in self.adj_list[node_id]:
                if successor not in scheduled:
                    # 检查所有前驱是否已调度
                    all_preds_scheduled = all(
                        pred in scheduled for pred in self.reverse_adj_list[successor]
                    )
                    
                    if all_preds_scheduled:
                        priority = node_priorities[successor]
                        heapq.heappush(ready_queue, (priority, successor))
        
        # 保存结果
        self.schedule = schedule
        self.metrics.max_cache = max_total
        self.metrics.l1_peak_usage = max_l1
        self.metrics.ub_peak_usage = max_ub
        
        logger.info(f"[问题1] 完成 - max(V_stay)={max_total}, L1峰值={max_l1}, UB峰值={max_ub}")
        
        return schedule
    
    def _compute_node_priorities(self) -> Dict[int, float]:
        """计算节点调度优先级"""
        priorities = {}
        
        # 计算每个节点的关键路径长度
        critical_paths = self._compute_critical_paths()
        
        for node_id, node in self.nodes.items():
            priority = 0.0
            
            # 基础优先级
            if node.is_alloc():
                # ALLOC节点优先调度
                priority = -2000
                # L0类型的ALLOC更优先
                if node.type and "L0" in node.type:
                    priority -= 1000
                # 小缓冲区优先分配
                priority += node.size * 0.01
                
            elif node.is_free():
                # FREE节点尽早调度以释放内存
                priority = -500
                # 大缓冲区的FREE更优先
                buf = self.buffers.get(node.buf_id)
                if buf:
                    priority -= buf.size * 0.1
                    
            else:
                # 计算节点根据关键路径
                priority = -critical_paths.get(node_id, 0)
                
                # COPY_IN优先，COPY_OUT延后
                if node.is_copy_in():
                    priority -= 300
                elif node.is_copy_out():
                    priority += 500
                
                # 考虑并行度
                if node.pipe:
                    # 不同执行单元的操作可以交错
                    pipe_hash = hash(node.pipe) % 10
                    priority += pipe_hash * 10
            
            priorities[node_id] = priority
        
        return priorities
    
    def _compute_critical_paths(self) -> Dict[int, int]:
        """计算每个节点到终点的关键路径长度"""
        memo = {}
        
        def dfs(node_id):
            if node_id in memo:
                return memo[node_id]
            
            node = self.nodes[node_id]
            cycles = node.cycles or 0
            
            if not self.adj_list[node_id]:
                # 叶节点
                memo[node_id] = cycles
            else:
                # 递归计算
                max_path = 0
                for successor in self.adj_list[node_id]:
                    max_path = max(max_path, dfs(successor))
                memo[node_id] = cycles + max_path
            
            return memo[node_id]
        
        # 计算所有节点的关键路径
        for node_id in self.nodes:
            if node_id not in memo:
                dfs(node_id)
        
        return memo
    
    # ==================== 问题2：缓存分配与换入换出 ====================
    def solve_memory_allocation(self, base_schedule: Optional[List[int]] = None) -> Tuple[Dict[int, int], List[SpillOperation]]:
        """
        问题2：缓存分配与SPILL操作
        目标：最小化总额外数据搬运量
        """
        logger.info(f"[问题2] 开始求解缓存分配: {self.task_name}")
        
        if base_schedule:
            self.schedule = base_schedule
        elif not self.schedule:
            self.solve_min_cache_schedule()
        
        # 初始化缓存管理器
        cache_managers = {
            cache_type: CacheManager(cache_type, CACHE_SIZES[cache_type])
            for cache_type in CACHE_SIZES
        }
        
        # 初始化结果
        allocation = {}
        spill_operations = []
        spill_nodes = []  # 新增的SPILL节点
        
        # 活跃缓冲区跟踪
        active_buffers = {}  # buf_id -> (type, offset, size)
        buffer_life_span = self._analyze_buffer_lifetime()
        
        # 扩展的调度序列（包含SPILL节点）
        extended_schedule = []
        
        # 按调度顺序处理
        for pos, node_id in enumerate(self.schedule):
            node = self.nodes[node_id]
            
            if node.is_alloc():
                buf = self.buffers[node.buf_id]
                cache_mgr = cache_managers[buf.type]
                
                # 尝试分配
                offset = cache_mgr.allocate(node.buf_id, buf.size)
                
                if offset == -1:
                    # 需要SPILL操作
                    logger.debug(f"位置{pos}: 缓存{buf.type}空间不足，需要SPILL (需要{buf.size})")
                    
                    # 选择victim缓冲区
                    victims = self._select_spill_victims(
                        cache_mgr, buf.size, active_buffers, 
                        buf.type, pos, buffer_life_span
                    )
                    
                    # 执行SPILL
                    for victim_buf_id in victims:
                        spill_op = self._perform_spill(
                            victim_buf_id, len(self.nodes) + len(spill_nodes), 
                            cache_mgr, pos
                        )
                        
                        if spill_op:
                            spill_operations.append(spill_op)
                            
                            # 创建SPILL节点
                            spill_out_node = Node(
                                id=spill_op.spill_out_node_id,
                                op="SPILL_OUT",
                                pipe="MTE3",
                                cycles=self._calculate_spill_cycles(victim_buf_id, True),
                                bufs=[victim_buf_id]
                            )
                            spill_in_node = Node(
                                id=spill_op.spill_in_node_id,
                                op="SPILL_IN",
                                pipe="MTE2",
                                cycles=self._calculate_spill_cycles(victim_buf_id, False),
                                bufs=[victim_buf_id]
                            )
                            
                            spill_nodes.append(spill_out_node)
                            spill_nodes.append(spill_in_node)
                            
                            # 在调度序列中插入SPILL_OUT
                            extended_schedule.append(spill_op.spill_out_node_id)
                            
                            # 释放victim缓冲区
                            cache_mgr.free(victim_buf_id)
                            if victim_buf_id in active_buffers:
                                del active_buffers[victim_buf_id]
                    
                    # 重新尝试分配
                    offset = cache_mgr.allocate(node.buf_id, buf.size)
                
                if offset != -1:
                    allocation[node.buf_id] = offset
                    active_buffers[node.buf_id] = (buf.type, offset, buf.size)
                    logger.debug(f"分配缓冲区{node.buf_id}: offset={offset}, size={buf.size}")
                else:
                    logger.error(f"无法为缓冲区{node.buf_id}分配内存")
            
            elif node.is_free():
                if node.buf_id in active_buffers:
                    buf_type, _, _ = active_buffers[node.buf_id]
                    cache_mgr = cache_managers[buf_type]
                    cache_mgr.free(node.buf_id)
                    del active_buffers[node.buf_id]
                    logger.debug(f"释放缓冲区{node.buf_id}")
            
            # 添加当前节点到扩展调度序列
            extended_schedule.append(node_id)
        
        # 添加所有节点（包括SPILL节点）
        for spill_node in spill_nodes:
            self.nodes[spill_node.id] = spill_node
        
        # 更新调度序列
        if spill_operations:
            self.schedule = self._rebuild_schedule_with_spills(
                self.schedule, spill_operations
            )
        
        # 保存结果
        self.memory_allocation = allocation
        self.spill_operations = spill_operations
        
        # 计算指标
        self._calculate_extra_data_movement()
        
        logger.info(f"[问题2] 完成 - SPILL次数={len(spill_operations)}, "
                   f"额外数据搬运={self.metrics.extra_data_movement}")
        
        return allocation, spill_operations
    
    def _analyze_buffer_lifetime(self) -> Dict[int, Tuple[int, int]]:
        """分析缓冲区生命周期"""
        lifetime = {}
        
        for buf_id, buf in self.buffers.items():
            # 找到ALLOC和FREE在调度序列中的位置
            alloc_pos = -1
            free_pos = len(self.schedule)
            
            for i, node_id in enumerate(self.schedule):
                if node_id == buf.alloc_node:
                    alloc_pos = i
                elif node_id == buf.free_node:
                    free_pos = i
            
            lifetime[buf_id] = (alloc_pos, free_pos)
        
        return lifetime
    
    def _select_spill_victims(self, cache_mgr: CacheManager, required_size: int,
                             active_buffers: Dict, cache_type: str, 
                             current_pos: int, lifetime: Dict) -> List[int]:
        """选择要换出的缓冲区（改进的LRU+大小感知策略）"""
        victims = []
        freed_size = 0
        
        # 候选victim列表
        candidates = []
        
        for buf_id, (buf_type, _, size) in active_buffers.items():
            if buf_type != cache_type:
                continue
            
            # 计算优先级（越高越容易被换出）
            next_use = self._find_next_use(buf_id, current_pos)
            distance = next_use - current_pos
            
            # 考虑多个因素
            priority = distance * 1000  # 使用距离
            priority += size * 0.1  # 倾向于换出大缓冲区
            
            # 检查是否被COPY_IN使用（换出成本较低）
            buf = self.buffers.get(buf_id)
            if buf and buf.copy_in_nodes:
                priority += 1000  # 优先换出
            
            candidates.append((priority, buf_id, size))
        
        # 按优先级排序
        candidates.sort(reverse=True)
        
        # 选择victim直到释放足够空间
        for _, buf_id, size in candidates:
            victims.append(buf_id)
            freed_size += size
            if freed_size >= required_size:
                break
        
        return victims
    
    def _perform_spill(self, buf_id: int, next_node_id: int, 
                      cache_mgr: CacheManager, position: int) -> Optional[SpillOperation]:
        """执行SPILL操作"""
        buf = self.buffers.get(buf_id)
        if not buf:
            return None
        
        # 找到可用的新偏移
        new_offset = 0  # 简化处理，实际可以优化
        
        spill_op = SpillOperation(
            buf_id=buf_id,
            new_offset=new_offset,
            spill_out_node_id=next_node_id,
            spill_in_node_id=next_node_id + 1,
            position_in_schedule=position
        )
        
        buf.is_spilled = True
        buf.spill_offset = new_offset
        
        return spill_op
    
    def _calculate_spill_cycles(self, buf_id: int, is_out: bool) -> int:
        """计算SPILL操作的执行周期"""
        buf = self.buffers.get(buf_id)
        if not buf:
            return 0
        
        # 检查是否被COPY_IN使用
        has_copy_in = bool(buf.copy_in_nodes)
        
        if has_copy_in and is_out:
            # 情况2：SPILL_OUT不产生实际搬运
            return 0
        
        # 计算周期数
        return buf.size * SPILL_COST_FACTOR + SPILL_COST_BASE
    
    def _rebuild_schedule_with_spills(self, original_schedule: List[int],
                                     spill_operations: List[SpillOperation]) -> List[int]:
        """重建包含SPILL节点的调度序列"""
        new_schedule = []
        spill_map = defaultdict(list)  # position -> [spill_ops]
        
        # 按位置组织SPILL操作
        for spill_op in spill_operations:
            spill_map[spill_op.position_in_schedule].append(spill_op)
        
        # 重建调度序列
        for pos, node_id in enumerate(original_schedule):
            # 插入该位置的SPILL_OUT节点
            if pos in spill_map:
                for spill_op in spill_map[pos]:
                    new_schedule.append(spill_op.spill_out_node_id)
            
            # 添加原节点
            new_schedule.append(node_id)
            
            # 在适当位置插入SPILL_IN节点
            # （简化处理：在需要使用前插入）
        
        # 添加剩余的SPILL_IN节点
        for spill_op in spill_operations:
            # 找到缓冲区下次使用位置
            next_use_pos = self._find_next_use_in_schedule(
                spill_op.buf_id, spill_op.position_in_schedule, new_schedule
            )
            
            # 在使用前插入SPILL_IN
            if next_use_pos > 0:
                new_schedule.insert(next_use_pos, spill_op.spill_in_node_id)
        
        return new_schedule
    
    def _find_next_use(self, buf_id: int, current_pos: int) -> int:
        """找到缓冲区的下次使用位置"""
        for i in range(current_pos + 1, len(self.schedule)):
            node_id = self.schedule[i]
            node = self.nodes[node_id]
            
            if node.is_compute() and buf_id in node.bufs:
                return i
            if node.is_free() and node.buf_id == buf_id:
                return i
        
        return len(self.schedule) + 1000
    
    def _find_next_use_in_schedule(self, buf_id: int, start_pos: int, 
                                  schedule: List[int]) -> int:
        """在指定调度序列中找到缓冲区的下次使用位置"""
        for i in range(start_pos + 1, len(schedule)):
            node_id = schedule[i]
            if node_id in self.nodes:
                node = self.nodes[node_id]
                if node.is_compute() and buf_id in node.bufs:
                    return i
        
        return -1
    
    def _calculate_extra_data_movement(self):
        """计算总额外数据搬运量"""
        total = 0
        
        for spill_op in self.spill_operations:
            buf = self.buffers[spill_op.buf_id]
            
            # 检查是否被COPY_IN使用
            has_copy_in = bool(buf.copy_in_nodes)
            
            if has_copy_in:
                # 情况2：仅SPILL_IN产生搬运
                total += buf.size
            else:
                # 情况1：SPILL_OUT和SPILL_IN都产生搬运
                total += buf.size * 2
        
        self.metrics.extra_data_movement = total
        self.metrics.spill_count = len(self.spill_operations)
    
    # ==================== 问题3：性能优化 ====================
    def solve_performance_optimization(self) -> Tuple[List[int], Dict[int, int], List[SpillOperation]]:
        """
        问题3：性能优化策略
        目标：在额外数据搬运量不显著增加的前提下，优化总执行时间
        """
        logger.info(f"[问题3] 开始性能优化: {self.task_name}")
        
        # 获取基础方案
        base_schedule = self.solve_min_cache_schedule()
        base_allocation, base_spills = self.solve_memory_allocation()
        base_makespan = self._calculate_makespan(base_schedule)
        base_movement = self.metrics.extra_data_movement
        
        logger.info(f"基础方案: Makespan={base_makespan}, 额外搬运={base_movement}")
        
        # 保存最佳方案
        best_schedule = base_schedule
        best_allocation = base_allocation
        best_spills = base_spills
        best_makespan = base_makespan
        best_movement = base_movement
        
        # 策略1：流水线优化
        pipeline_result = self._optimize_pipeline()
        if pipeline_result:
            p_schedule, p_allocation, p_spills = pipeline_result
            p_makespan = self._calculate_makespan(p_schedule)
            p_movement = self._calculate_movement_for_spills(p_spills)
            
            logger.info(f"流水线优化: Makespan={p_makespan}, 额外搬运={p_movement}")
            
            if p_makespan < best_makespan and p_movement <= base_movement * 1.2:
                best_schedule = p_schedule
                best_allocation = p_allocation
                best_spills = p_spills
                best_makespan = p_makespan
                best_movement = p_movement
        
        # 策略2：数据预取优化
        prefetch_result = self._optimize_prefetch()
        if prefetch_result:
            pf_schedule, pf_allocation, pf_spills = prefetch_result
            pf_makespan = self._calculate_makespan(pf_schedule)
            pf_movement = self._calculate_movement_for_spills(pf_spills)
            
            logger.info(f"预取优化: Makespan={pf_makespan}, 额外搬运={pf_movement}")
            
            if pf_makespan < best_makespan and pf_movement <= base_movement * 1.2:
                best_schedule = pf_schedule
                best_allocation = pf_allocation
                best_spills = pf_spills
                best_makespan = pf_makespan
                best_movement = pf_movement
        
        # 策略3：关键路径优化
        critical_result = self._optimize_critical_path()
        if critical_result:
            c_schedule, c_allocation, c_spills = critical_result
            c_makespan = self._calculate_makespan(c_schedule)
            c_movement = self._calculate_movement_for_spills(c_spills)
            
            logger.info(f"关键路径优化: Makespan={c_makespan}, 额外搬运={c_movement}")
            
            if c_makespan < best_makespan and c_movement <= base_movement * 1.2:
                best_schedule = c_schedule
                best_allocation = c_allocation
                best_spills = c_spills
                best_makespan = c_makespan
                best_movement = c_movement
        
        # 更新最佳方案
        self.schedule = best_schedule
        self.memory_allocation = best_allocation
        self.spill_operations = best_spills
        self.metrics.makespan = best_makespan
        self.metrics.extra_data_movement = best_movement
        
        # 计算性能提升
        improvement = (1 - best_makespan / base_makespan) * 100
        logger.info(f"[问题3] 优化完成: Makespan={best_makespan} (提升{improvement:.1f}%), "
                   f"额外搬运={best_movement}")
        
        return best_schedule, best_allocation, best_spills
    
    def _calculate_makespan(self, schedule: List[int]) -> int:
        """计算总执行时间（makespan）"""
        if not schedule:
            return 0
        
        # 初始化
        start_time = {}
        end_time = {}
        unit_queues = defaultdict(list)  # 执行单元的任务队列
        
        # 构建临时的依赖图
        temp_deps = defaultdict(set)
        for src, dst in self.edges:
            if src in set(schedule) and dst in set(schedule):
                temp_deps[dst].add(src)
        
        # 按调度顺序处理
        for node_id in schedule:
            if node_id not in self.nodes:
                continue
                
            node = self.nodes[node_id]
            
            # 计算最早开始时间
            earliest_start = 0
            
            # 依赖约束
            for pred_id in temp_deps[node_id]:
                if pred_id in end_time:
                    earliest_start = max(earliest_start, end_time[pred_id])
            
            # 资源约束（同一执行单元串行）
            if node.pipe and node.cycles > 0:
                if unit_queues[node.pipe]:
                    last_task = unit_queues[node.pipe][-1]
                    earliest_start = max(earliest_start, end_time[last_task])
            
            # 设置时间
            start_time[node_id] = earliest_start
            end_time[node_id] = earliest_start + (node.cycles or 0)
            
            # 添加到执行单元队列
            if node.pipe and node.cycles > 0:
                unit_queues[node.pipe].append(node_id)
        
        return max(end_time.values()) if end_time else 0
    
    def _calculate_movement_for_spills(self, spills: List[SpillOperation]) -> int:
        """计算SPILL操作的数据搬运量"""
        total = 0
        
        for spill in spills:
            buf = self.buffers[spill.buf_id]
            has_copy_in = bool(buf.copy_in_nodes)
            
            if has_copy_in:
                total += buf.size
            else:
                total += buf.size * 2
        
        return total
    
    def _optimize_pipeline(self) -> Optional[Tuple[List[int], Dict[int, int], List[SpillOperation]]]:
        """流水线优化：最大化不同执行单元的并行度"""
        # 识别可并行的操作组
        parallel_groups = self._identify_parallel_operations()
        
        if not parallel_groups:
            return None
        
        # 构建优化的调度序列
        optimized = []
        scheduled = set()
        
        # 按组调度，组内交错不同执行单元的操作
        for group in parallel_groups:
            # 按执行单元分组
            by_pipe = defaultdict(list)
            for node_id in group:
                if node_id not in scheduled:
                    node = self.nodes[node_id]
                    if node.pipe:
                        by_pipe[node.pipe].append(node_id)
                    else:
                        optimized.append(node_id)
                        scheduled.add(node_id)
            
            # 交错调度不同单元的操作
            max_len = max(len(nodes) for nodes in by_pipe.values()) if by_pipe else 0
            for i in range(max_len):
                for pipe, nodes in by_pipe.items():
                    if i < len(nodes) and nodes[i] not in scheduled:
                        optimized.append(nodes[i])
                        scheduled.add(nodes[i])
        
        # 添加未调度的节点
        for node_id in self.schedule:
            if node_id not in scheduled:
                optimized.append(node_id)
        
        # 验证调度合法性
        if not self._is_valid_schedule(optimized):
            return None
        
        # 重新分配内存
        self.schedule = optimized
        allocation, spills = self.solve_memory_allocation(optimized)
        
        return optimized, allocation, spills
    
    def _optimize_prefetch(self) -> Optional[Tuple[List[int], Dict[int, int], List[SpillOperation]]]:
        """数据预取优化：提前加载数据以隐藏延迟"""
        optimized = []
        scheduled = set()
        
        # 分析数据依赖链
        data_chains = self._analyze_data_chains()
        
        # 对每条数据链进行预取优化
        for chain in data_chains:
            # 提前调度COPY_IN操作
            copy_ins = [n for n in chain if self.nodes[n].is_copy_in()]
            others = [n for n in chain if not self.nodes[n].is_copy_in()]
            
            # 先调度COPY_IN
            for node_id in copy_ins:
                if node_id not in scheduled:
                    optimized.append(node_id)
                    scheduled.add(node_id)
            
            # 再调度其他操作
            for node_id in others:
                if node_id not in scheduled:
                    optimized.append(node_id)
                    scheduled.add(node_id)
        
        # 添加未调度的节点
        for node_id in self.schedule:
            if node_id not in scheduled:
                optimized.append(node_id)
        
        # 验证调度合法性
        if not self._is_valid_schedule(optimized):
            return None
        
        # 重新分配内存
        self.schedule = optimized
        allocation, spills = self.solve_memory_allocation(optimized)
        
        return optimized, allocation, spills
    
    def _optimize_critical_path(self) -> Optional[Tuple[List[int], Dict[int, int], List[SpillOperation]]]:
        """关键路径优化：优先调度关键路径上的操作"""
        # 计算关键路径
        critical_nodes = self._find_critical_path_nodes()
        
        if not critical_nodes:
            return None
        
        # 构建优化的调度序列
        optimized = []
        scheduled = set()
        ready = set()
        
        # 初始化就绪节点
        for node_id in self.nodes:
            if self.in_degree[node_id] == 0:
                ready.add(node_id)
        
        while ready:
            # 优先选择关键路径上的节点
            critical_ready = ready & critical_nodes
            
            if critical_ready:
                # 选择关键路径上cycles最大的节点
                node_id = max(critical_ready, 
                            key=lambda x: self.nodes[x].cycles or 0)
            else:
                # 选择普通节点
                node_id = min(ready)
            
            optimized.append(node_id)
            scheduled.add(node_id)
            ready.remove(node_id)
            
            # 更新就绪节点
            for successor in self.adj_list[node_id]:
                if successor not in scheduled:
                    # 检查所有前驱是否已调度
                    if all(pred in scheduled for pred in self.reverse_adj_list[successor]):
                        ready.add(successor)
        
        # 验证调度合法性
        if not self._is_valid_schedule(optimized):
            return None
        
        # 重新分配内存
        self.schedule = optimized
        allocation, spills = self.solve_memory_allocation(optimized)
        
        return optimized, allocation, spills
    
    def _identify_parallel_operations(self) -> List[Set[int]]:
        """识别可并行的操作组"""
        parallel_groups = []
        
        # 计算节点层级
        levels = self._compute_node_levels()
        
        # 每个层级内的节点可能并行
        for level, nodes in levels.items():
            # 过滤出计算节点
            compute_nodes = {
                n for n in nodes 
                if self.nodes[n].is_compute() and self.nodes[n].pipe
            }
            
            if len(compute_nodes) > 1:
                # 检查是否有不同的执行单元
                pipes = {self.nodes[n].pipe for n in compute_nodes}
                if len(pipes) > 1:
                    parallel_groups.append(compute_nodes)
        
        return parallel_groups
    
    def _analyze_data_chains(self) -> List[List[int]]:
        """分析数据处理链"""
        chains = []
        visited = set()
        
        # 从COPY_IN节点开始追踪
        for node_id, node in self.nodes.items():
            if node.is_copy_in() and node_id not in visited:
                chain = []
                queue = deque([node_id])
                
                while queue:
                    current = queue.popleft()
                    if current in visited:
                        continue
                    
                    chain.append(current)
                    visited.add(current)
                    
                    # 添加后继节点
                    for successor in self.adj_list[current]:
                        if successor not in visited:
                            queue.append(successor)
                
                if chain:
                    chains.append(chain)
        
        return chains
    
    def _find_critical_path_nodes(self) -> Set[int]:
        """找到关键路径上的节点"""
        # 计算最早开始时间
        earliest = {}
        for node_id in self._topological_sort():
            earliest[node_id] = 0
            for pred in self.reverse_adj_list[node_id]:
                cycles = self.nodes[pred].cycles or 0
                earliest[node_id] = max(earliest[node_id], 
                                       earliest.get(pred, 0) + cycles)
        
        # 计算最晚开始时间
        latest = {}
        makespan = max(earliest.values()) if earliest else 0
        
        for node_id in reversed(self._topological_sort()):
            latest[node_id] = makespan
            for succ in self.adj_list[node_id]:
                cycles = self.nodes[node_id].cycles or 0
                latest[node_id] = min(latest[node_id],
                                     latest.get(succ, makespan) - cycles)
        
        # 关键路径上的节点：最早开始时间 = 最晚开始时间
        critical = set()
        for node_id in self.nodes:
            if earliest.get(node_id, 0) == latest.get(node_id, 0):
                critical.add(node_id)
        
        return critical
    
    def _compute_node_levels(self) -> Dict[int, Set[int]]:
        """计算节点的层级（BFS）"""
        levels = defaultdict(set)
        visited = set()
        queue = deque()
        
        # 从入度为0的节点开始
        for node_id in self.nodes:
            if self.in_degree[node_id] == 0:
                queue.append((node_id, 0))
                visited.add(node_id)
        
        while queue:
            node_id, level = queue.popleft()
            levels[level].add(node_id)
            
            for successor in self.adj_list[node_id]:
                if successor not in visited:
                    # 检查所有前驱是否已访问
                    all_preds_visited = all(
                        pred in visited for pred in self.reverse_adj_list[successor]
                    )
                    if all_preds_visited:
                        queue.append((successor, level + 1))
                        visited.add(successor)
        
        return levels
    
    def _topological_sort(self) -> List[int]:
        """拓扑排序"""
        result = []
        in_degree_copy = self.in_degree.copy()
        queue = deque([n for n in self.nodes if in_degree_copy[n] == 0])
        
        while queue:
            node_id = queue.popleft()
            result.append(node_id)
            
            for successor in self.adj_list[node_id]:
                in_degree_copy[successor] -= 1
                if in_degree_copy[successor] == 0:
                    queue.append(successor)
        
        return result
    
    def _is_valid_schedule(self, schedule: List[int]) -> bool:
        """验证调度序列的合法性"""
        if len(schedule) != len(self.nodes):
            return False
        
        position = {node_id: i for i, node_id in enumerate(schedule)}
        
        # 检查依赖约束
        for src, dst in self.edges:
            if src in position and dst in position:
                if position[src] >= position[dst]:
                    return False
        
        return True
    
    # ==================== 可视化功能 ====================
    def visualize_pipeline(self, save_path: Optional[Path] = None):
        """可视化流水线执行图"""
        if not self.schedule:
            logger.warning("没有可用的调度序列")
            return
        
        # 计算时序信息
        time_info = self._compute_timeline()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(16, 10))
        
        # 颜色映射
        colors = {
            'MTE1': '#FF9999', 'MTE2': '#99CCFF', 'MTE3': '#99FF99',
            'FIXP': '#FFFF99', 'Cube': '#FF99CC', 'Vector': '#CC99FF'
        }
        
        y_pos = 0
        y_labels = []
        
        # 按执行单元绘制
        for pipe in ['MTE1', 'MTE2', 'MTE3', 'FIXP', 'Cube', 'Vector']:
            if pipe not in time_info:
                continue
                
            y_labels.append(pipe)
            
            for node_id, start, end in time_info[pipe]:
                node = self.nodes[node_id]
                
                # 绘制任务块
                ax.barh(y_pos, end - start, left=start, height=0.8,
                       color=colors.get(pipe, 'gray'), 
                       edgecolor='black', linewidth=0.5, alpha=0.8)
                
                # 添加标签（如果空间足够）
                if end - start > 10:
                    label = f"{node.op[:8]}\n#{node_id}"
                    ax.text(start + (end - start) / 2, y_pos, label,
                           ha='center', va='center', fontsize=7, fontweight='bold')
            
            y_pos += 1
        
        ax.set_yticks(range(len(y_labels)))
        ax.set_yticklabels(y_labels, fontsize=10)
        ax.set_xlabel('Time (cycles)', fontsize=11)
        ax.set_title(f'Pipeline Schedule - {self.task_name}\n'
                    f'Makespan: {self.metrics.makespan} cycles', 
                    fontsize=13, fontweight='bold')
        ax.grid(True, axis='x', alpha=0.3)
        
        # 添加统计信息
        info_text = (f"Nodes: {len(self.nodes)}\n"
                    f"Edges: {len(self.edges)}\n"
                    f"SPILLs: {self.metrics.spill_count}")
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               fontsize=9, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            logger.info(f"流水线图已保存到 {save_path}")
        else:
            plt.show()
    
    def _compute_timeline(self) -> Dict[str, List[Tuple[int, int, int]]]:
        """计算时间线信息"""
        time_info = defaultdict(list)
        
        start_time = {}
        end_time = {}
        unit_last_end = defaultdict(int)
        
        # 构建依赖关系快速查找
        deps = defaultdict(set)
        for src, dst in self.edges:
            deps[dst].add(src)
        
        for node_id in self.schedule:
            if node_id not in self.nodes:
                continue
                
            node = self.nodes[node_id]
            
            if not node.pipe or node.cycles == 0:
                start_time[node_id] = 0
                end_time[node_id] = 0
                continue
            
            # 计算最早开始时间
            earliest = 0
            
            # 依赖约束
            for pred_id in deps[node_id]:
                if pred_id in end_time:
                    earliest = max(earliest, end_time[pred_id])
            
            # 资源约束
            earliest = max(earliest, unit_last_end[node.pipe])
            
            # 设置时间
            start_time[node_id] = earliest
            end_time[node_id] = earliest + node.cycles
            unit_last_end[node.pipe] = end_time[node_id]
            
            # 记录
            time_info[node.pipe].append((node_id, earliest, end_time[node_id]))
        
        self.metrics.makespan = max(end_time.values()) if end_time else 0
        
        return time_info
    
    def visualize_memory_usage(self, save_path: Optional[Path] = None):
        """可视化内存使用情况"""
        if not self.schedule:
            logger.warning("没有可用的调度序列")
            return
        
        # 计算内存使用时间线
        memory_timeline = self._compute_memory_timeline()
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))
        
        # L1缓存使用
        ax1 = axes[0, 0]
        positions = list(range(len(memory_timeline['L1'])))
        ax1.plot(positions, memory_timeline['L1'], 'b-', linewidth=2)
        ax1.axhline(y=CACHE_SIZES['L1'], color='r', linestyle='--', 
                   label=f'Capacity ({CACHE_SIZES["L1"]}B)')
        ax1.fill_between(positions, 0, memory_timeline['L1'], alpha=0.3, color='blue')
        ax1.set_xlabel('Schedule Position')
        ax1.set_ylabel('L1 Usage (bytes)')
        ax1.set_title('L1 Cache Usage')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # UB缓存使用
        ax2 = axes[0, 1]
        ax2.plot(positions, memory_timeline['UB'], 'g-', linewidth=2)
        ax2.axhline(y=CACHE_SIZES['UB'], color='r', linestyle='--',
                   label=f'Capacity ({CACHE_SIZES["UB"]}B)')
        ax2.fill_between(positions, 0, memory_timeline['UB'], alpha=0.3, color='green')
        ax2.set_xlabel('Schedule Position')
        ax2.set_ylabel('UB Usage (bytes)')
        ax2.set_title('UB Cache Usage')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 总缓存使用（L1+UB）
        ax3 = axes[1, 0]
        total_usage = [l1 + ub for l1, ub in zip(memory_timeline['L1'], memory_timeline['UB'])]
        ax3.plot(positions, total_usage, 'r-', linewidth=2)
        ax3.fill_between(positions, 0, total_usage, alpha=0.3, color='red')
        ax3.set_xlabel('Schedule Position')
        ax3.set_ylabel('Total Usage (bytes)')
        ax3.set_title(f'Total Cache Usage (L1+UB)\nPeak: {max(total_usage)}B')
        ax3.grid(True, alpha=0.3)
        
        # 缓存利用率
        ax4 = axes[1, 1]
        l1_util = [u / CACHE_SIZES['L1'] * 100 for u in memory_timeline['L1']]
        ub_util = [u / CACHE_SIZES['UB'] * 100 for u in memory_timeline['UB']]
        
        ax4.plot(positions, l1_util, 'b-', label='L1 Utilization', linewidth=2)
        ax4.plot(positions, ub_util, 'g-', label='UB Utilization', linewidth=2)
        ax4.axhline(y=100, color='r', linestyle='--', alpha=0.5)
        ax4.set_xlabel('Schedule Position')
        ax4.set_ylabel('Utilization (%)')
        ax4.set_title('Cache Utilization')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_ylim([0, 120])
        
        # 标记SPILL位置
        for spill_op in self.spill_operations:
            for ax in [ax1, ax2, ax3, ax4]:
                ax.axvline(x=spill_op.position_in_schedule, 
                          color='orange', linestyle=':', alpha=0.7)
        
        plt.suptitle(f'Memory Usage Analysis - {self.task_name}', 
                    fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            logger.info(f"内存使用图已保存到 {save_path}")
        else:
            plt.show()
    
    def _compute_memory_timeline(self) -> Dict[str, List[int]]:
        """计算内存使用时间线"""
        timeline = {
            'L1': [],
            'UB': [],
            'L0A': [],
            'L0B': [],
            'L0C': []
        }
        
        current = {cache_type: 0 for cache_type in CACHE_SIZES}
        
        for node_id in self.schedule:
            if node_id not in self.nodes:
                continue
                
            node = self.nodes[node_id]
            
            if node.is_alloc():
                if node.type in current:
                    current[node.type] += node.size
            elif node.is_free():
                buf = self.buffers.get(node.buf_id)
                if buf and buf.type in current:
                    current[buf.type] = max(0, current[buf.type] - buf.size)
            
            # 记录当前状态
            for cache_type in timeline:
                timeline[cache_type].append(current[cache_type])
        
        return timeline
    
    # ==================== 结果输出 ====================
    def save_results(self, problem_num: int, output_dir: Path = Path("output")):
        """保存结果到文件"""
        output_dir = output_dir / f"Problem{problem_num}"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存调度序列
        schedule_file = output_dir / f"{self.task_name}_schedule.txt"
        with open(schedule_file, 'w') as f:
            for node_id in self.schedule:
                f.write(f"{node_id}\n")
        
        # 问题2和3需要保存额外信息
        if problem_num >= 2:
            # 保存内存分配
            memory_file = output_dir / f"{self.task_name}_memory.txt"
            with open(memory_file, 'w') as f:
                for buf_id in sorted(self.memory_allocation.keys()):
                    offset = self.memory_allocation[buf_id]
                    f.write(f"{buf_id}:{offset}\n")
            
            # 保存SPILL操作
            spill_file = output_dir / f"{self.task_name}_spill.txt"
            with open(spill_file, 'w') as f:
                for spill_op in self.spill_operations:
                    f.write(f"{spill_op.buf_id}:{spill_op.new_offset}\n")
        
        logger.info(f"结果已保存到 {output_dir}")
    
    def print_summary(self):
        """打印结果摘要"""
        print(f"\n{'='*70}")
        print(f"{'任务摘要':^70}")
        print(f"{'='*70}")
        print(f"任务名称: {self.task_name}")
        print(f"节点数量: {len(self.nodes)}")
        print(f"边数量: {len(self.edges)}")
        print(f"缓冲区数量: {len(self.buffers)}")
        print(f"{'-'*70}")
        print(f"{'性能指标':^70}")
        print(f"{'-'*70}")
        print(f"max(V_stay): {self.metrics.max_cache} bytes")
        print(f"  - L1峰值: {self.metrics.l1_peak_usage} bytes")
        print(f"  - UB峰值: {self.metrics.ub_peak_usage} bytes")
        print(f"Makespan: {self.metrics.makespan} cycles")
        print(f"总额外数据搬运量: {self.metrics.extra_data_movement} bytes")
        print(f"SPILL操作数: {self.metrics.spill_count}")
        print(f"{'='*70}\n")


# ================================ 第二部分：批量测试脚本 ================================

def run_all_problems():
    """运行所有问题的完整测试"""
    test_cases = [
        "Matmul_Case0",
        "Matmul_Case1",
        "FlashAttention_Case0",
        "FlashAttention_Case1",
        "Conv_Case0",
        "Conv_Case1"
    ]
    
    results_summary = []
    
    for case_name in test_cases:
        json_file = f"data/{case_name}.json"
        
        # 检查文件是否存在
        if not Path(json_file).exists():
            print(f"警告: 文件 {json_file} 不存在，跳过")
            continue
        
        print(f"\n{'='*80}")
        print(f"处理案例: {case_name}")
        print(f"{'='*80}")
        
        try:
            # 创建调度器
            scheduler = NPUScheduler(json_file)
            
            # 问题1：最小缓存驻留调度
            print(f"\n[问题1] 求解最小缓存驻留调度...")
            t1_start = time.time()
            schedule_p1 = scheduler.solve_min_cache_schedule()
            t1_time = time.time() - t1_start
            max_cache_p1 = scheduler.metrics.max_cache
            scheduler.save_results(1)
            
            # 问题2：缓存分配与换入换出
            print(f"\n[问题2] 求解缓存分配...")
            scheduler = NPUScheduler(json_file)  # 重新初始化
            t2_start = time.time()
            schedule_p2 = scheduler.solve_min_cache_schedule()
            allocation_p2, spills_p2 = scheduler.solve_memory_allocation()
            t2_time = time.time() - t2_start
            extra_movement_p2 = scheduler.metrics.extra_data_movement
            scheduler.save_results(2)
            
            # 问题3：性能优化
            print(f"\n[问题3] 性能优化...")
            scheduler = NPUScheduler(json_file)  # 重新初始化
            t3_start = time.time()
            schedule_p3, allocation_p3, spills_p3 = scheduler.solve_performance_optimization()
            t3_time = time.time() - t3_start
            makespan_p3 = scheduler.metrics.makespan
            extra_movement_p3 = scheduler.metrics.extra_data_movement
            scheduler.save_results(3)
            
            # 打印摘要
            scheduler.print_summary()
            
            # 可视化（仅对小规模案例）
            if len(scheduler.nodes) <= 200:
                print("生成可视化...")
                vis_dir = Path("visualizations") / case_name
                vis_dir.mkdir(parents=True, exist_ok=True)
                
                scheduler.visualize_pipeline(vis_dir / "pipeline.png")
                scheduler.visualize_memory_usage(vis_dir / "memory.png")
            
            # 收集结果
            results_summary.append({
                '案例': case_name,
                '节点数': len(scheduler.nodes),
                '边数': len(scheduler.edges),
                'max(V_stay)': max_cache_p1,
                '额外搬运(P2)': extra_movement_p2,
                'SPILL数(P2)': len(spills_p2),
                'Makespan(P3)': makespan_p3,
                '额外搬运(P3)': extra_movement_p3,
                'SPILL数(P3)': len(spills_p3),
                '时间(P1)': f"{t1_time:.2f}s",
                '时间(P2)': f"{t2_time:.2f}s",
                '时间(P3)': f"{t3_time:.2f}s"
            })
            
        except Exception as e:
            print(f"错误: 处理 {case_name} 时发生异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 打印汇总表
    if results_summary:
        print(f"\n{'='*100}")
        print(f"{'所有测试案例结果汇总':^100}")
        print(f"{'='*100}")
        
        df = pd.DataFrame(results_summary)
        print(df.to_string(index=False))
        
        # 保存到Excel
        excel_path = Path("output") / "results_summary.xlsx"
        excel_path.parent.mkdir(exist_ok=True)
        df.to_excel(excel_path, index=False)
        print(f"\n结果已保存到 {excel_path}")


# ================================ 第三部分：主函数 ================================

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='2025数模竞赛A题 - NPU调度器')
    parser.add_argument('--mode', type=str, default='all',
                       choices=['single', 'all', 'test'],
                       help='运行模式：single-单个文件，all-所有问题，test-测试')
    parser.add_argument('--input', type=str, help='输入JSON文件路径（single模式）')
    parser.add_argument('--problem', type=int, default=3, choices=[1, 2, 3],
                       help='求解的问题编号（single模式）')
    parser.add_argument('--output', type=str, default='output', help='输出目录')
    parser.add_argument('--visualize', action='store_true', help='生成可视化')
    
    args = parser.parse_args()
    
    if args.mode == 'single':
        if not args.input:
            print("错误: single模式需要指定输入文件 (--input)")
            return
        
        scheduler = NPUScheduler(args.input)
        
        if args.problem == 1:
            schedule = scheduler.solve_min_cache_schedule()
        elif args.problem == 2:
            schedule = scheduler.solve_min_cache_schedule()
            allocation, spills = scheduler.solve_memory_allocation()
        else:  # problem == 3
            schedule, allocation, spills = scheduler.solve_performance_optimization()
        
        scheduler.print_summary()
        scheduler.save_results(args.problem, Path(args.output))
        
        if args.visualize:
            scheduler.visualize_pipeline()
            scheduler.visualize_memory_usage()
    
    elif args.mode == 'all':
        run_all_problems()
    
    elif args.mode == 'test':
        # 快速测试模式
        test_file = "data/Matmul_Case0.json"
        if Path(test_file).exists():
            print("运行快速测试...")
            scheduler = NPUScheduler(test_file)
            schedule = scheduler.solve_min_cache_schedule()
            scheduler.print_summary()
            print("测试完成！")
        else:
            print(f"测试文件 {test_file} 不存在")


if __name__ == "__main__":
    main()