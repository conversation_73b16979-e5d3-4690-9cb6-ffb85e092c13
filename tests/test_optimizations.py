#!/usr/bin/env python
"""
Test script for advanced NPU scheduling optimizations
Verifies that all optimization modules work correctly
"""

import sys
import traceback
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing

def test_imports():
    """Test that all optimization modules can be imported"""
    print("Testing imports...")
    
    results = {}
    
    # Test core scheduler
    try:
        from optimized_npu_scheduler import OptimizedNPUScheduler, SchedulingStrategy
        results['core_scheduler'] = True
        print("  ✓ Core scheduler")
    except Exception as e:
        results['core_scheduler'] = False
        print(f"  ✗ Core scheduler: {e}")
    
    # Test RL scheduler
    try:
        from rl_scheduler import RLNPUScheduler, NPUSchedulingEnv
        results['rl_scheduler'] = True
        print("  ✓ RL scheduler")
    except Exception as e:
        results['rl_scheduler'] = False
        print(f"  ✗ RL scheduler: {e}")
    
    # Test dynamic scheduler
    try:
        from dynamic_scheduler import AdaptiveScheduler, PerformanceMonitor
        results['dynamic_scheduler'] = True
        print("  ✓ Dynamic scheduler")
    except Exception as e:
        results['dynamic_scheduler'] = False
        print(f"  ✗ Dynamic scheduler: {e}")
    
    # Test multicore scheduler
    try:
        from multicore_scheduler import DistributedScheduler, NUMAManager
        results['multicore_scheduler'] = True
        print("  ✓ Multicore scheduler")
    except Exception as e:
        results['multicore_scheduler'] = False
        print(f"  ✗ Multicore scheduler: {e}")
    
    # Test prefetch optimizer
    try:
        from prefetch_optimizer import NPUVectorRunahead, MemoryLatencyHider
        results['prefetch_optimizer'] = True
        print("  ✓ Prefetch optimizer")
    except Exception as e:
        results['prefetch_optimizer'] = False
        print(f"  ✗ Prefetch optimizer: {e}")
    
    # Test energy optimizer
    try:
        from energy_optimizer import EnergyAwareOptimizer, PowerProfile
        results['energy_optimizer'] = True
        print("  ✓ Energy optimizer")
    except Exception as e:
        results['energy_optimizer'] = False
        print(f"  ✗ Energy optimizer: {e}")
    
    return results

def test_basic_functionality():
    """Test basic functionality of optimization modules"""
    print("\nTesting basic functionality...")
    
    # Find test data
    data_dir = Path("data")
    test_files = list(data_dir.glob("*.json"))
    
    if not test_files:
        print("  ✗ No test data found in data/ directory")
        return False
    
    test_file = test_files[0]
    print(f"  Using test file: {test_file.name}")
    
    try:
        from optimized_npu_scheduler import OptimizedNPUScheduler, SchedulingStrategy
        
        # Create scheduler
        scheduler = OptimizedNPUScheduler(str(test_file))
        print(f"  ✓ Loaded graph with {len(scheduler.nodes)} nodes")
        
        # Test basic strategies
        basic_strategies = [
            SchedulingStrategy.GREEDY,
            SchedulingStrategy.HEFT,
            SchedulingStrategy.HYBRID
        ]
        
        for strategy in basic_strategies:
            try:
                result = scheduler.solve_problem1(strategy)
                print(f"  ✓ {strategy.value}: {result.max_cache} bytes")
            except Exception as e:
                print(f"  ✗ {strategy.value}: {e}")
        
        # Test advanced strategies (if available)
        advanced_strategies = [
            SchedulingStrategy.RL_BASED,
            SchedulingStrategy.ADAPTIVE,
            SchedulingStrategy.DISTRIBUTED,
            SchedulingStrategy.ENERGY_AWARE
        ]
        
        for strategy in advanced_strategies:
            try:
                result = scheduler.solve_problem1(strategy)
                print(f"  ✓ {strategy.value}: {result.max_cache} bytes")
            except Exception as e:
                print(f"  ⚠ {strategy.value}: {e}")  # Warning, not error
        
        # Cleanup
        scheduler.cleanup_optimizers()
        print("  ✓ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Basic functionality test failed: {e}")
        return False

def test_individual_modules():
    """Test individual optimization modules"""
    print("\nTesting individual modules...")
    
    # Create simple test data
    test_nodes = {
        0: {'id': 0, 'op': 'ALLOC', 'buf_id': 0, 'size': 100, 'type': 'L1', 'cycles': 10, 'predecessors': [], 'successors': [1]},
        1: {'id': 1, 'op': 'COMPUTE', 'cycles': 50, 'predecessors': [0], 'successors': [2]},
        2: {'id': 2, 'op': 'FREE', 'buf_id': 0, 'size': 100, 'type': 'L1', 'cycles': 5, 'predecessors': [1], 'successors': []}
    }
    test_edges = [(0, 1), (1, 2)]
    
    # Test RL scheduler
    try:
        from rl_scheduler import NPUSchedulingEnv
        env = NPUSchedulingEnv(test_nodes, test_edges)
        obs, _ = env.reset()
        print("  ✓ RL environment creation")
    except Exception as e:
        print(f"  ✗ RL environment: {e}")
    
    # Test adaptive scheduler
    try:
        from dynamic_scheduler import PerformanceMonitor
        monitor = PerformanceMonitor(sampling_interval=0.1)
        print("  ✓ Performance monitor creation")
    except Exception as e:
        print(f"  ✗ Performance monitor: {e}")
    
    # Test NUMA manager
    try:
        from multicore_scheduler import NUMAManager
        numa = NUMAManager()
        topology = numa.topology
        print(f"  ✓ NUMA manager: {len(topology.numa_nodes)} NUMA nodes")
    except Exception as e:
        print(f"  ✗ NUMA manager: {e}")
    
    # Test prefetch optimizer
    try:
        from prefetch_optimizer import StrideDetector
        detector = StrideDetector()
        print("  ✓ Stride detector creation")
    except Exception as e:
        print(f"  ✗ Stride detector: {e}")
    
    # Test energy optimizer
    try:
        from energy_optimizer import PowerMonitor
        power_monitor = PowerMonitor()
        baseline = power_monitor.baseline_power
        print(f"  ✓ Power monitor: {baseline:.1f}W baseline")
    except Exception as e:
        print(f"  ✗ Power monitor: {e}")

def test_dependencies():
    """Test optional dependencies"""
    print("\nTesting optional dependencies...")
    
    dependencies = [
        ('torch', 'PyTorch for neural networks'),
        ('torch_geometric', 'PyTorch Geometric for GNNs'),
        ('stable_baselines3', 'Stable Baselines3 for RL'),
        ('gymnasium', 'Gymnasium for RL environments'),
        ('networkx', 'NetworkX for graph processing'),
        ('pymoo', 'Pymoo for multi-objective optimization'),
        ('ray', 'Ray for distributed computing'),
        ('psutil', 'psutil for system monitoring'),
        ('numpy', 'NumPy for numerical computing'),
        ('scipy', 'SciPy for scientific computing')
    ]
    
    available_deps = []
    missing_deps = []
    
    for dep_name, description in dependencies:
        try:
            __import__(dep_name)
            available_deps.append(dep_name)
            print(f"  ✓ {dep_name}: {description}")
        except ImportError:
            missing_deps.append(dep_name)
            print(f"  ✗ {dep_name}: {description}")
    
    print(f"\nDependency summary:")
    print(f"  Available: {len(available_deps)}/{len(dependencies)}")
    print(f"  Missing: {len(missing_deps)}")
    
    if missing_deps:
        print(f"  Install missing: pip install {' '.join(missing_deps)}")
    
    return len(missing_deps) == 0

def main():
    """Main test function"""
    print("="*60)
    print("Advanced NPU Scheduling Optimization Tests")
    print("="*60)
    
    all_passed = True
    
    # Test imports
    import_results = test_imports()
    import_success = all(import_results.values())
    
    if not import_success:
        print(f"\nImport test: {sum(import_results.values())}/{len(import_results)} modules imported successfully")
        all_passed = False
    else:
        print("\n✓ All modules imported successfully")
    
    # Test dependencies
    deps_available = test_dependencies()
    if not deps_available:
        print("\n⚠ Some optional dependencies are missing")
    
    # Test basic functionality
    if import_results.get('core_scheduler', False):
        basic_success = test_basic_functionality()
        if not basic_success:
            all_passed = False
    else:
        print("\n✗ Skipping functionality tests (core scheduler not available)")
        all_passed = False
    
    # Test individual modules
    test_individual_modules()
    
    # Final summary
    print("\n" + "="*60)
    print("Test Summary")
    print("="*60)
    
    if all_passed:
        print("🎉 All critical tests passed!")
        print("   The advanced optimization system is ready to use.")
    else:
        print("⚠ Some tests failed.")
        print("   Check the error messages above for details.")
    
    print(f"\nModule availability:")
    for module, available in import_results.items():
        status = "✓" if available else "✗"
        print(f"  {status} {module.replace('_', ' ').title()}")
    
    if not all_passed:
        print(f"\nTo install missing dependencies:")
        print(f"  pip install -r requirements.txt")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nTest suite failed with error: {e}")
        traceback.print_exc()
        sys.exit(1)
