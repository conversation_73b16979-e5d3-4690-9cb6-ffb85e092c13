#!/usr/bin/env python
"""
生成更复杂的NPU调度测试数据
"""

import json
import random
from pathlib import Path

def generate_complex_matmul_case():
    """生成复杂的矩阵乘法案例"""
    nodes = []
    edges = []
    node_id = 0
    
    # 输入矩阵A的分配和加载
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 0,
        "Size": 2048,
        "Type": "L1"
    })
    node_id += 1
    
    nodes.append({
        "Id": node_id,
        "Op": "COPY_IN",
        "Pipe": "MTE2",
        "Cycles": 100,
        "Bufs": [0]
    })
    edges.append([node_id-1, node_id])
    node_id += 1
    
    # 输入矩阵B的分配和加载
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 1,
        "Size": 2048,
        "Type": "L1"
    })
    node_id += 1
    
    nodes.append({
        "Id": node_id,
        "Op": "COPY_IN",
        "Pipe": "MTE2",
        "Cycles": 100,
        "Bufs": [1]
    })
    edges.append([node_id-1, node_id])
    node_id += 1
    
    # 输出矩阵C的分配
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 2,
        "Size": 4096,
        "Type": "L1"
    })
    node_id += 1
    
    # 矩阵乘法计算
    nodes.append({
        "Id": node_id,
        "Op": "MATMUL",
        "Pipe": "CUBE",
        "Cycles": 500,
        "Bufs": [0, 1, 2]
    })
    edges.append([1, node_id])  # 依赖A的加载
    edges.append([3, node_id])  # 依赖B的加载
    edges.append([4, node_id])  # 依赖C的分配
    node_id += 1
    
    # 结果输出
    nodes.append({
        "Id": node_id,
        "Op": "COPY_OUT",
        "Pipe": "MTE2",
        "Cycles": 150,
        "Bufs": [2]
    })
    edges.append([node_id-1, node_id])
    node_id += 1
    
    # 释放内存
    nodes.append({
        "Id": node_id,
        "Op": "FREE",
        "BufId": 0,
        "Type": "L1"
    })
    edges.append([1, node_id])  # 在使用完A后释放
    edges.append([5, node_id])  # 在计算完成后释放
    node_id += 1
    
    nodes.append({
        "Id": node_id,
        "Op": "FREE",
        "BufId": 1,
        "Type": "L1"
    })
    edges.append([3, node_id])  # 在使用完B后释放
    edges.append([5, node_id])  # 在计算完成后释放
    node_id += 1
    
    nodes.append({
        "Id": node_id,
        "Op": "FREE",
        "BufId": 2,
        "Type": "L1"
    })
    edges.append([6, node_id])  # 在输出完成后释放
    
    return {"Nodes": nodes, "Edges": edges}

def generate_complex_conv_case():
    """生成复杂的卷积案例"""
    nodes = []
    edges = []
    node_id = 0
    
    # 输入特征图分配
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 0,
        "Size": 8192,
        "Type": "L1"
    })
    node_id += 1
    
    # 卷积核分配
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 1,
        "Size": 1024,
        "Type": "L0A"
    })
    node_id += 1
    
    # 输出特征图分配
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 2,
        "Size": 6144,
        "Type": "L1"
    })
    node_id += 1
    
    # 偏置分配
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 3,
        "Size": 256,
        "Type": "L0B"
    })
    node_id += 1
    
    # 数据加载
    for i in range(4):
        nodes.append({
            "Id": node_id,
            "Op": "COPY_IN",
            "Pipe": "MTE2",
            "Cycles": 80 + i * 10,
            "Bufs": [i]
        })
        edges.append([i, node_id])
        node_id += 1
    
    # 卷积计算
    nodes.append({
        "Id": node_id,
        "Op": "CONV2D",
        "Pipe": "CUBE",
        "Cycles": 800,
        "Bufs": [0, 1, 2, 3]
    })
    for i in range(4, 8):
        edges.append([i, node_id])
    node_id += 1
    
    # 激活函数
    nodes.append({
        "Id": node_id,
        "Op": "RELU",
        "Pipe": "VECTOR",
        "Cycles": 50,
        "Bufs": [2]
    })
    edges.append([node_id-1, node_id])
    node_id += 1
    
    # 输出
    nodes.append({
        "Id": node_id,
        "Op": "COPY_OUT",
        "Pipe": "MTE2",
        "Cycles": 120,
        "Bufs": [2]
    })
    edges.append([node_id-1, node_id])
    node_id += 1
    
    # 释放内存
    for buf_id in range(4):
        nodes.append({
            "Id": node_id,
            "Op": "FREE",
            "BufId": buf_id,
            "Type": ["L1", "L0A", "L1", "L0B"][buf_id]
        })
        edges.append([8, node_id])  # 在卷积完成后释放
        if buf_id == 2:  # 输出缓冲区在输出完成后释放
            edges.append([10, node_id])
        node_id += 1
    
    return {"Nodes": nodes, "Edges": edges}

def generate_complex_attention_case():
    """生成复杂的注意力机制案例"""
    nodes = []
    edges = []
    node_id = 0
    
    # Q, K, V矩阵分配
    for i, name in enumerate(['Q', 'K', 'V']):
        nodes.append({
            "Id": node_id,
            "Op": "ALLOC",
            "BufId": i,
            "Size": 3072,
            "Type": "L1"
        })
        node_id += 1
    
    # 注意力分数矩阵分配
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 3,
        "Size": 4096,
        "Type": "L1"
    })
    node_id += 1
    
    # 输出矩阵分配
    nodes.append({
        "Id": node_id,
        "Op": "ALLOC",
        "BufId": 4,
        "Size": 3072,
        "Type": "L1"
    })
    node_id += 1
    
    # 数据加载
    for i in range(3):
        nodes.append({
            "Id": node_id,
            "Op": "COPY_IN",
            "Pipe": "MTE2",
            "Cycles": 150,
            "Bufs": [i]
        })
        edges.append([i, node_id])
        node_id += 1
    
    # Q*K^T计算
    nodes.append({
        "Id": node_id,
        "Op": "MATMUL",
        "Pipe": "CUBE",
        "Cycles": 600,
        "Bufs": [0, 1, 3]
    })
    edges.append([5, node_id])  # Q加载完成
    edges.append([6, node_id])  # K加载完成
    edges.append([3, node_id])  # 分数矩阵分配完成
    node_id += 1
    
    # Softmax
    nodes.append({
        "Id": node_id,
        "Op": "SOFTMAX",
        "Pipe": "VECTOR",
        "Cycles": 200,
        "Bufs": [3]
    })
    edges.append([node_id-1, node_id])
    node_id += 1
    
    # 注意力*V计算
    nodes.append({
        "Id": node_id,
        "Op": "MATMUL",
        "Pipe": "CUBE",
        "Cycles": 600,
        "Bufs": [3, 2, 4]
    })
    edges.append([node_id-1, node_id])  # Softmax完成
    edges.append([7, node_id])  # V加载完成
    edges.append([4, node_id])  # 输出矩阵分配完成
    node_id += 1
    
    # 输出
    nodes.append({
        "Id": node_id,
        "Op": "COPY_OUT",
        "Pipe": "MTE2",
        "Cycles": 150,
        "Bufs": [4]
    })
    edges.append([node_id-1, node_id])
    node_id += 1
    
    # 释放内存
    for buf_id in range(5):
        nodes.append({
            "Id": node_id,
            "Op": "FREE",
            "BufId": buf_id,
            "Type": "L1"
        })
        if buf_id < 3:
            edges.append([8, node_id])  # Q*K^T完成后可以释放Q,K
            if buf_id == 2:  # V在最后计算完成后释放
                edges.append([11, node_id])
        elif buf_id == 3:  # 注意力分数在最后计算完成后释放
            edges.append([11, node_id])
        else:  # 输出在输出完成后释放
            edges.append([12, node_id])
        node_id += 1
    
    return {"Nodes": nodes, "Edges": edges}

def main():
    """生成所有测试数据"""
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 生成复杂的测试案例
    test_cases = {
        "Matmul_Case0_Complex": generate_complex_matmul_case(),
        "Conv_Case0_Complex": generate_complex_conv_case(),
        "FlashAttention_Case0_Complex": generate_complex_attention_case()
    }
    
    for case_name, data in test_cases.items():
        file_path = data_dir / f"{case_name}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        print(f"生成测试数据: {file_path}")
        print(f"  节点数: {len(data['Nodes'])}")
        print(f"  边数: {len(data['Edges'])}")

if __name__ == "__main__":
    main()
