import json
from pathlib import Path
from collections import defaultdict
from constants import DATA_PATH, OUTPUT_PATH

class Utils:
    """
    工具类
    """
    base_data_path = Path(DATA_PATH)
    output_data_path = Path(OUTPUT_PATH)
    
    @classmethod
    def check_or_create_dir(cls, dir_path):
        dir_path = Path(dir_path)
        if not dir_path.exists():
            dir_path.mkdir(parents=True)
        return dir_path
    
    @classmethod
    def get_data_from_json(cls, file_path):
        current_file_path = cls.base_data_path / file_path
        with open(current_file_path, 'r') as json_file:
            data = json.load(json_file)
            return data
        
    @classmethod
    def get_data_from_dir(cls, dir_path=DATA_PATH):
        dir_path = Path(dir_path)
        return [Utils.get_data_from_json(file) for file in dir_path.glob('*.json')]
        
    @classmethod
    def write_data_to_json(cls, file_path, data):
        output_file_path = cls.output_data_path / file_path
        Utils.check_or_create_dir(output_file_path.parent)
        with open(output_file_path, 'w') as json_file:
            json.dump(data, json_file, ensure_ascii=False, indent=4)
            
    @classmethod
    def write_data_to_jsonline(cls, file_path, data):
        output_file_path = cls.output_data_path / file_path
        Utils.check_or_create_dir(output_file_path.parent)
        with open(output_file_path, 'a') as json_file:
            json.dump(data, json_file, ensure_ascii=False)
            json_file.write('\n')

class Node:
    def __init__(self, node_data):
        self.id = node_data["Id"]
        self.op = node_data["Op"]
        self.buf_id = node_data.get("BufId")
        self.size = node_data.get("Size")
        self.type = node_data.get("Type")
        self.pipe = node_data.get("Pipe")
        self.cycles = node_data.get("Cycles")
        self.bufs = node_data.get("Bufs")
        self.in_degree = 0
        self.predecessors = [] # List of Node objects that point to this node
        self.successors = [] # List of Node objects that this node points to

    def __repr__(self):
        return f"Node(Id={self.id}, Op={self.op}, Type={self.type}, Size={self.size})"

    def __lt__(self, other):
        # For priority queue, prioritize FREE nodes, then smaller size ALLOC nodes
        if self.op == "FREE" and other.op != "FREE":
            return True
        if self.op != "FREE" and other.op == "FREE":
            return False
        if self.op == "ALLOC" and other.op == "ALLOC":
            if self.size is not None and other.size is not None:
                return self.size < other.size
        return self.id < other.id # Fallback for consistent ordering

class Graph:
    """
    图类: 该图为有向无环图
    """
    def __init__(self, data):
        self.nodes = {}
        self.adj = defaultdict(list)  # Adjacency list using node IDs
        self.rev_adj = defaultdict(list)  # Reverse adjacency list for in-degree calculation
        self.json_data = data # Store original json data for reconstruction

        for n_data in data["Nodes"]:
            node = Node(n_data)
            self.nodes[node.id] = node

        for edge in data["Edges"]:
            start_node_id, end_node_id = edge
            self.adj[start_node_id].append(end_node_id)
            self.rev_adj[end_node_id].append(start_node_id)
            self.nodes[start_node_id].successors.append(self.nodes[end_node_id])
            self.nodes[end_node_id].predecessors.append(self.nodes[start_node_id])
            self.nodes[end_node_id].in_degree += 1

    def get_root_nodes(self):
        return [node for node_id, node in self.nodes.items() if node.in_degree == 0]

    def deepcopy(self):
        new_json_data = json.loads(json.dumps(self.json_data))
        return Graph(new_json_data)

    def get_node_by_id(self, node_id):
        return self.nodes.get(node_id)

    def get_all_in_degree(self):
        return [node.in_degree for node in self.nodes.values()]

    def get_all_out_degree(self):
        return [len(node.successors) for node in self.nodes.values()]